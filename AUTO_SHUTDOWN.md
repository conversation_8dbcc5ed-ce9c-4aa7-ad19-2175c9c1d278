# Auto-Shutdown Feature

This document describes the automatic server shutdown functionality that has been implemented for the Om: The Journey game server.

## Overview

The server now automatically shuts down once all players have completed the final round of the game. This feature is designed to:

- Provide a clean end to game sessions
- Prevent servers from running indefinitely after games complete
- Ensure proper cleanup of resources and connections
- Save the final game state before shutdown

## How It Works

### Game Completion Detection

The server listens for game completion events that are emitted when:

1. **OM Token Victory**: A player reaches the required number of OM tokens (5 for 3 players, 7 for 2 players)
2. **Score Threshold Victory**: All players complete the final round after someone reaches 100+ points

### Shutdown Process

When a game completes:

1. **Immediate Notification**: All connected clients receive a `serverShutdown` event with a 5-second countdown
2. **Delay Period**: The server waits 5 seconds to allow clients to process the game completion
3. **Graceful Shutdown**: The server then:
   - Closes all Socket.IO connections
   - Closes the HTTP server
   - Saves the final game state to `gameState.json`
   - Exits the process cleanly

### Signal Handling

The server also responds to system signals for graceful shutdown:
- `SIGINT` (Ctrl+C)
- `SIGTERM` (process termination)

## Configuration

### Enabling/Disabling Auto-Shutdown

Auto-shutdown is **enabled by default**. To disable it:

```bash
# Disable auto-shutdown
AUTO_SHUTDOWN=false npm start

# Or set environment variable
export AUTO_SHUTDOWN=false
node server/start_server.js
```

### Customizing Shutdown Delay

The default delay is 5 seconds. This can be modified in `server/index.js`:

```javascript
const AUTO_SHUTDOWN_CONFIG = {
  enabled: process.env.AUTO_SHUTDOWN !== 'false',
  delayMs: 5000 // Change this value to adjust delay
};
```

## Client-Side Handling

Clients should listen for the `serverShutdown` event to handle the shutdown gracefully:

```javascript
socket.on('serverShutdown', (data) => {
  if (data.immediate) {
    // Server is shutting down immediately
    console.log('Server is shutting down now');
  } else {
    // Server will shutdown after delay
    console.log(`Server will shutdown in ${data.delayMs / 1000} seconds`);
  }
});
```

## Testing

A test script is provided to verify the auto-shutdown functionality:

```bash
node server/test_auto_shutdown.js
```

This script:
1. Starts a test server on port 4001
2. Creates a mock game scenario
3. Simulates game completion
4. Verifies that auto-shutdown occurs

## Logs and Monitoring

When auto-shutdown occurs, you'll see logs like:

```
=== GAME COMPLETED ===
Winner: Alice
Win Condition: SCORE_THRESHOLD
Total Rounds: 8
Server will shutdown in 5 seconds...

=== INITIATING GRACEFUL SHUTDOWN ===
Closing Socket.IO connections...
Socket.IO server closed.
Closing HTTP server...
HTTP server closed.
Saving final game state...
Game state saved successfully.
=== GRACEFUL SHUTDOWN COMPLETE ===
```

## Use Cases

This feature is particularly useful for:

- **Bot simulations**: Automatically end server processes after bot games complete
- **Automated testing**: Ensure test servers don't remain running after tests
- **Resource management**: Prevent memory leaks from long-running game servers
- **Clean deployments**: Ensure proper cleanup in containerized environments

## Troubleshooting

### Server Not Shutting Down

If the server doesn't shut down automatically:

1. Check that `AUTO_SHUTDOWN` is not set to `false`
2. Verify the game actually completed (check logs for "GAME COMPLETED" message)
3. Ensure the `gameCompleted` event is being emitted from the game state

### Force Exit

If graceful shutdown fails, the server has a 10-second timeout that will force exit the process to prevent hanging.

### Manual Shutdown

You can always manually shut down the server using:
- `Ctrl+C` (SIGINT)
- `kill <process_id>` (SIGTERM)
- `kill -9 <process_id>` (SIGKILL - force kill)
