/**
 * Bot Client - Connects bots to the server via websockets
 *
 * This provides the same interface as the browser client but runs headlessly.
 * Each bot instance connects to the server via websockets just like a regular player.
 */

const io = require('socket.io-client');
const BotFactory = require('./BotFactory');
const logger = require('../server/utils/logger');

/**
 * Generate a random bot name
 * @returns {string} A random bot name
 */
function generateRandomBotName() {
  const adjectives = ['Swift', 'Clever', 'Mighty', 'Wise', 'Brave', 'Noble', 'Mystic', 'Cosmic', 'Ancient', 'Eternal'];
  const nouns = ['Traveler', 'Seeker', 'Wanderer', 'Explorer', 'Voyager', 'Pilgrim', 'Nomad', 'Journeyer', 'Pathfinder', 'Wayfarer'];

  const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];

  return `${randomAdjective}${randomNoun}`;
  // return 'MightyPilgrim';
  // return 'CosmicNomad';
}

class BotClient {
  /**
   * Create a new bot client
   * @param {object} config - Bot configuration
   * @param {string} config.name - Bot name
   * @param {string} config.serverUrl - Server URL
   * @param {string} config.strategy - Bot strategy name
   * @param {string} config.type - Bot type (normal, defensive, etc.)
   * @param {number} [config.thinkingSpeed=1] - Simulation speed multiplier (higher = faster)
   * @param {boolean} [config.omTrackEnabled=false] - Whether Om track features are enabled
   * @param {boolean} [config.prioritizeOmTrack=false] - Whether to prioritize Om track in decision making
   */
  constructor(config) {
    this.config = {
      name: generateRandomBotName(),
      serverUrl: 'http://localhost:4000',
      strategy: 'random',
      type: 'normal',
      thinkingSpeed: 1,
      omTrackEnabled: false,
      prioritizeOmTrack: false,
      ...config
    };

    this.socket = null;
    this.connected = false;
    this.gameState = null;
    this.botFactory = new BotFactory({ logger });
    this.bot = null;
    this.isMyTurn = false;
    this.joinedGame = false;
    this.lastRoundCount = 0; // Track the last round count to detect new rounds
    
    // Om track position tracking
    this.omSpace = 0; // Current Om track space
    this.omStackPosition = 0; // Position in the stack at current Om space
  }

  /**
   * Start the bot client
   */
  async start() {
    try {
      logger.info(`Starting bot client for ${this.config.name} with ${this.config.strategy} strategy`);
      logger.info(`Om Track: enabled=${this.config.omTrackEnabled}, prioritize=${this.config.prioritizeOmTrack}`);

      // Connect to the server with additional options
      this.socket = io(this.config.serverUrl, {
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000,
        transports: ['websocket', 'polling'],
        extraHeaders: {
          'User-Agent': 'BotClient/1.0'
        }
      });

      logger.info(`Attempting to connect to ${this.config.serverUrl}`);

      // Set up socket event listeners
      this._setupSocketEvents();

      // Create the bot strategy (but don't create the Bot instance yet as it needs gameState)
      // We'll create it after joining when we have more context

      return true;
    } catch (error) {
      logger.error(`Error starting bot client: ${error.message}`);
      return false;
    }
  }

  /**
   * Set up socket event listeners
   * @private
   */
  _setupSocketEvents() {
    // Handle connection
    this.socket.on('connect', () => {
      logger.info(`Bot ${this.config.name} connected to server with socket ID: ${this.socket.id}`);
      this.connected = true;

      // Join the game automatically
      this.socket.emit('joinGame', this.config.name);
    });

    // Handle disconnect
    this.socket.on('disconnect', () => {
      logger.info(`Bot ${this.config.name} disconnected from server`);
      this.connected = false;
    });

    // Handle errors
    this.socket.on('connect_error', (error) => {
      logger.error(`Bot ${this.config.name} connection error: ${error.message}`);
      logger.error(`Connection details: URL=${this.config.serverUrl}, transport=${this.socket.io.engine?.transport?.name || 'unknown'}`);

      // Try to reconnect with a different transport
      if (this.socket.io.engine?.transport?.name === 'polling') {
        logger.info('Trying to reconnect with websocket transport only');
        this.socket.io.engine.transport.polling = false;
        this.socket.io.engine.transport.websocket = true;
      }
    });

    // Handle game state updates
    this.socket.on('gameState', (state) => {
      // Save the game state
      this.gameState = state;

      // Create bot instance if not yet created
      if (!this.bot && state) {
        this._createBot();
      }

      // Update bot's player state
      this._updateBotPlayerState(state);

      // Check if it's our turn
      // Find our player in the game state (for debugging purposes)
      const playerIndex = state.players.findIndex(p => p.id === this.socket.id);
      const isMyTurn = state.started && state.turnIndex !== undefined &&
                     state.players[state.turnIndex]?.id === this.socket.id;

      if (playerIndex === -1) {
        // This is normal during initial connection before the server processes the joinGame event
        logger.info(`Bot ${this.config.name} waiting to be added to the game. Socket ID: ${this.socket.id}`);
      } else {
        // Log when the bot is successfully added to the game (only log this once)
        if (!this.joinedGame) {
          logger.info(`Bot ${this.config.name} successfully joined the game at position ${state.players[playerIndex].position}`);
          this.joinedGame = true;
        }
        
        // Update Om track position if Om track is enabled
        if (this.config.omTrackEnabled && state.omTrack) {
          this._updateOmTrackPosition(state);
        }
      }

      // If it's our turn and game has started, plan and execute a move
      if (isMyTurn && !this.isMyTurn && state.started) {
        logger.info(`Bot ${this.config.name}'s turn`);
        this.isMyTurn = true;

        // Check if this is a new game or round
        // If the bot's turn state needs to be reset, do it here
        if (this.bot && this.bot.strategy && this.bot.strategy._turnState) {
          // If the game state indicates a new round or game, reset the turn state
          if (state.roundCount !== this.lastRoundCount || state.turnCount === 0) {
            logger.info(`Bot ${this.config.name} detected new round or game, resetting turn state`);
            // Reset the turn state to ensure proper initialization
            this.bot.strategy._turnState.turnNumber = this.bot.turnsPlayed - 1;
            this.lastRoundCount = state.roundCount;
          }
        }

        this._takeTurn();
      } else if (!isMyTurn) {
        this.isMyTurn = false;
      }
    });

    // Handle join error
    this.socket.on('joinError', (error) => {
      logger.error(`Bot ${this.config.name} join error: ${error}`);
    });

    // Handle move error
    this.socket.on('moveError', (error) => {
      logger.error(`Bot ${this.config.name} move error: ${error}`);
      // End turn after error to avoid getting stuck
      this.socket.emit('endTurn');
    });

    // Handle card selection requests
    this.socket.on('selectTravelCards', (data) => {
      // Use the bot's strategy to select cards
      this._handleCardSelection(data);
    });

    // Store the thinkingSpeed received from the server
    this.socket.on('botConfig', (config) => {
      logger.info(`Received bot config from server: ${JSON.stringify(config)}`);
      if (config.thinkingSpeed) {
        this.config.thinkingSpeed = config.thinkingSpeed;
        logger.info(`Updated bot thinking speed to: ${this.config.thinkingSpeed}x`);
      }
      
      // Update Om track related config if present
      if (config.omTrackEnabled !== undefined) {
        this.config.omTrackEnabled = config.omTrackEnabled;
      }
      if (config.prioritizeOmTrack !== undefined) {
        this.config.prioritizeOmTrack = config.prioritizeOmTrack;
      }

      // Always enable omTrackEnabled for reliable Om track position tracking
      this.config.omTrackEnabled = true;
      logger.info(`Forcing Om track features to be enabled for bot ${this.config.name}`);
    });
    
    // Handle Om track updates
    this.socket.on('omTrackUpdate', (data) => {
      logger.info(`Received Om track update: ${JSON.stringify(data)}`);
      if (data && data.omSpace !== undefined) {
        this.omSpace = data.omSpace;
        this.omStackPosition = data.stackPosition || 0;
        logger.info(`Bot ${this.config.name} Om track position updated: Space=${this.omSpace}, Stack=${this.omStackPosition}`);
      }
    });
  }

  /**
   * Create the bot instance
   * @private
   */
  _createBot() {
    try {
      // Create the strategy with Om track configuration
      const strategy = this.botFactory.createStrategy(this.config.strategy, {
        omTrackEnabled: this.config.omTrackEnabled,
        prioritizeOmTrack: this.config.prioritizeOmTrack
      });

      // Create a simplified Bot that only handles decision-making
      this.bot = {
        id: this.socket.id,
        name: generateRandomBotName(),
        strategy: strategy,
        playerState: null,
        logger: logger,
        turnsPlayed: 0, // Add turnsPlayed property to track turns like in simulation mode
        // Add getPlayerState method that the strategy expects
        getPlayerState: function() {
          return this.playerState;
        },
        // Add Om track properties
        omSpace: this.omSpace,
        omStackPosition: this.omStackPosition,
        // Add method to get turn order ranking
        getTurnOrderRanking: function() {
          return {
            omSpace: this.omSpace,
            omStackPosition: this.omStackPosition
          };
        }
      };

      logger.info(`Created bot ${this.bot.name} with ${strategy.name} strategy, Om track position: Space=${this.omSpace}, Stack=${this.omStackPosition}`);
    } catch (error) {
      logger.error(`Error creating bot: ${error.message}`);
    }
  }

  /**
   * Update the bot's player state from the current game state
   * @param {Object} gameState - The current game state
   * @private
   */
  _updateBotPlayerState(gameState) {
    if (!this.bot) return;

    try {
      if (!gameState) {
        logger.warn(`Bot ${this.config.name} received invalid gameState in _updateBotPlayerState`);
        return;
      }
      
      // Check if there's a simulation speed setting in the game state
      if (gameState.simulation && typeof gameState.simulation.simulationSpeed === 'number') {
        // Update thinking speed if it has changed
        if (this.config.thinkingSpeed !== gameState.simulation.simulationSpeed) {
          this.config.thinkingSpeed = gameState.simulation.simulationSpeed;
          logger.info(`Updated bot thinking speed from game state: ${this.config.thinkingSpeed}x`);
        }
      }

      // Find player state for this bot
      const player = gameState.players.find(p => p.id === this.socket.id);
      if (!player) return;

      // Create a player state object similar to what the server-side bot would use
      const playerState = {
        isMyTurn: gameState.started && gameState.turnIndex !== undefined &&
                 gameState.players[gameState.turnIndex]?.id === this.socket.id,
        player: player,
        numPlayers: gameState.players.length,
        locations: gameState.locations || [],
        edges: gameState.edges || [],
        faceUpTravel: gameState.faceUpTravel || [],
        faceUpEvent: gameState.faceUpEvent || [],
        faceUpJourneyInner: gameState.faceUpJourneyInner || [],
        faceUpJourneyOuter: gameState.faceUpJourneyOuter || [],
        locationOm: gameState.locationOm || {},
        locationCubes: gameState.locationCubes || {},
        // Add global event properties from gameState
        currentGlobalEvent: gameState.currentGlobalEvent || null,
        globalEvent: gameState.globalEvent || null,
        activeGlobalEvent: gameState.activeGlobalEvent || null,
        // Important: Preserve detailed opponent data structure
        opponents: gameState.players
          .filter(p => p.id !== this.socket.id)
          .map(p => ({
            id: p.id,
            name: p.name,
            position: p.position,
            handSize: p.hand?.length || 0,
            journeyCards: p.collectedJourneys?.length || 0,
            // Add om token properties from player object
            omTemp: p.omTemp || [],
            omSlotsInner: p.omSlotsInner || [],
            omSlotsOuter: p.omSlotsOuter || [],
            // For backward compatibility
            omTokens: {
              inner: (p.omSlotsInner || []).filter(x => x > 0).length,
              outer: (p.omSlotsOuter || []).filter(x => x > 0).length,
              temp: (p.omTemp || []).reduce((sum, val) => sum + val, 0)
            }
          }))
      };

      // Add Om track data if enabled
      if (this.config.omTrackEnabled && gameState.omTrack) {
        playerState.omTrack = gameState.omTrack;
        
        // Update bot's Om track position
        this._updateOmTrackPosition(gameState);
        this.bot.omSpace = this.omSpace;
        this.bot.omStackPosition = this.omStackPosition;
      }

      // Log journey card information for debugging
      logger.info(`Bot ${this.config.name} state update - Journey cards: Inner=${(gameState.faceUpJourneyInner || []).length}, Outer=${(gameState.faceUpJourneyOuter || []).length}`);

      // Ensure journey card arrays are properly initialized
      if (!playerState.faceUpJourneyInner) {
        logger.warn(`Bot ${this.config.name} missing faceUpJourneyInner in gameState, initializing empty array`);
        playerState.faceUpJourneyInner = [];
      }

      if (!playerState.faceUpJourneyOuter) {
        logger.warn(`Bot ${this.config.name} missing faceUpJourneyOuter in gameState, initializing empty array`);
        playerState.faceUpJourneyOuter = [];
      }

      // Ensure journey cards have all required properties
      // This is critical for the bot strategy to work correctly
      playerState.faceUpJourneyInner = playerState.faceUpJourneyInner.map(card => {
        // Make sure each card has an id and locationId
        if (!card.id || !card.locationId) {
          logger.warn(`Bot ${this.config.name} found inner journey card with missing properties: ${JSON.stringify(card)}`);
        }
        return {
          ...card,
          // Ensure these properties exist
          id: card.id || `JI${Math.floor(Math.random() * 1000)}`,
          locationId: card.locationId || 0,
          reward: card.reward || { inner: 10 } // Default reward
        };
      });

      playerState.faceUpJourneyOuter = playerState.faceUpJourneyOuter.map(card => {
        // Make sure each card has an id and locationId
        if (!card.id || !card.locationId) {
          logger.warn(`Bot ${this.config.name} found outer journey card with missing properties: ${JSON.stringify(card)}`);
        }
        return {
          ...card,
          // Ensure these properties exist
          id: card.id || `JO${Math.floor(Math.random() * 1000)}`,
          locationId: card.locationId || 0,
          reward: card.reward || { outer: 10 } // Default reward
        };
      });

      // Log journey card details after processing
      if (playerState.faceUpJourneyInner.length > 0 || playerState.faceUpJourneyOuter.length > 0) {
        logger.info(`Bot ${this.config.name} journey cards after processing:`);
        logger.info(`Inner (${playerState.faceUpJourneyInner.length}): ${JSON.stringify(playerState.faceUpJourneyInner.map(c => ({ id: c.id, loc: c.locationId })))}`);
        logger.info(`Outer (${playerState.faceUpJourneyOuter.length}): ${JSON.stringify(playerState.faceUpJourneyOuter.map(c => ({ id: c.id, loc: c.locationId })))}`);
      }

      // Ensure player has collectedJourneys property
      if (!playerState.player.collectedJourneys) {
        playerState.player.collectedJourneys = [];
        logger.info(`Bot ${this.config.name} missing collectedJourneys property in player state. Adding empty array.`);
      }

      // Update the bot's player state
      this.bot.playerState = playerState;
    } catch (error) {
      logger.error(`Error updating bot player state: ${error.message}`);
    }
  }
  
  /**
   * Update the bot's Om track position based on the current game state
   * @param {Object} gameState - The current game state
   * @private
   */
  _updateOmTrackPosition(gameState) {
    if (!gameState.omTrack) return;
    
    // Find this bot's position on the Om track
    for (let space = gameState.omTrack.length - 1; space >= 0; space--) {
      const stack = gameState.omTrack[space] || [];
      const stackPosition = stack.findIndex(playerId => playerId === this.socket.id);
      
      if (stackPosition !== -1) {
        this.omSpace = space;
        this.omStackPosition = stackPosition;
        return;
      }
    }
    
    // If not found on the Om track, default to position 0
    this.omSpace = 0;
    this.omStackPosition = 0;
  }

  /**
   * Have the bot take its turn
   * @private
   */
  async _takeTurn() {
    if (!this.bot || !this.bot.strategy) {
      logger.error('Cannot take turn: Bot or strategy not defined');
      this.socket.emit('endTurn');
      return;
    }

    try {
      // Add a delay to simulate "thinking" - make it longer for more human-like behavior
      // Adjust thinking time based on the thinkingSpeed parameter
      const thinkingTimeBase = Math.random() * 1000 + 1500; // 1.5-2.5 seconds of thinking time
      const thinkingTime = Math.max(50, thinkingTimeBase / this.config.thinkingSpeed); // Min 50ms thinking time
      logger.info(`Bot ${this.bot.name} thinking for ${Math.round(thinkingTime/100)/10} seconds (speed: ${this.config.thinkingSpeed}x)...`);
      await new Promise(resolve => setTimeout(resolve, thinkingTime));

      // Ensure the bot has a journey target before planning the turn
      // This is a safeguard in case the turn state reset logic didn't trigger
      if (this.bot.strategy._currentTarget === null || this.bot.strategy._currentTarget === undefined) {
        logger.info(`Bot ${this.bot.name} has no journey target, attempting to select one before planning turn`);
        if (typeof this.bot.strategy._selectTargetJourneyCard === 'function') {
          await this.bot.strategy._selectTargetJourneyCard(this.bot);

          // Also update the resource plan if needed
          if (typeof this.bot.strategy._updateResourcePlan === 'function') {
            await this.bot.strategy._updateResourcePlan(this.bot);
          }
        }
      }

      // Plan the turn using the bot's strategy
      const actions = await this.bot.strategy.planTurn(this.bot);

      if (!actions) {
        logger.warn(`Bot ${this.bot.name} got no actions from strategy. Ending turn.`);
        this.socket.emit('endTurn');
        // Reset isMyTurn flag to allow detection of consecutive turns
        this.isMyTurn = false;
        return;
      }

      // Handle both single action and array of actions
      const actionList = Array.isArray(actions) ? actions : [actions];

      // Filter out any undefined actions
      const validActions = actionList.filter(action => action && action.action);

      if (validActions.length === 0) {
        logger.warn(`Bot ${this.bot.name} got no valid actions from strategy. Ending turn.`);
        this.socket.emit('endTurn');
        // Reset isMyTurn flag to allow detection of consecutive turns
        this.isMyTurn = false;
        return;
      }

      // Log all actions that will be executed
      logger.info(`Bot ${this.bot.name} planning turn with ${validActions.length} actions (moved=${this.bot.strategy._turnState?.hasMoved}, cards=${this.bot.strategy._turnState?.hasCollectedCards})`);

      // Log the action types for debugging
      const actionTypes = validActions.map(a => a.action).join(', ');
      logger.info(`RETURNING ${validActions.length} ACTIONS: ${actionTypes}`);

      // Execute each action in sequence
      for (const action of validActions) {
        // Execute the action based on its type
        switch (action.action) {
          case 'movePlayer':
            this._executeMoveAction(action.params);
            // Add a longer delay after movement to allow human players to see the UI effects
            // Adjust by thinking speed
            const moveDelay = Math.max(300, 3000 / this.config.thinkingSpeed);
            logger.info(`Bot ${this.bot.name} waiting ${moveDelay/1000} seconds after movement for UI effects...`);
            await new Promise(resolve => setTimeout(resolve, moveDelay));
            break;
          case 'pickCards':
            this._executePickCardsAction(action.params);
            // Add a longer delay after picking travel cards
            if (action.params.type === 'travel') {
              // Adjust by thinking speed 
              const travelPickDelay = Math.max(300, 3000 / this.config.thinkingSpeed);
              logger.info(`Bot ${this.bot.name} waiting ${travelPickDelay/1000} seconds after picking travel cards for UI effects...`);
              await new Promise(resolve => setTimeout(resolve, travelPickDelay));
            } else {
              // Regular delay for other card types, adjusted by thinking speed
              await new Promise(resolve => setTimeout(resolve, Math.max(50, 500 / this.config.thinkingSpeed)));
            }
            break;
          case 'collectJourney':
            this._executeCollectJourneyAction(action.params);
            // Add a longer delay after collecting journey cards, adjusted by thinking speed
            const journeyDelay = Math.max(300, 3000 / this.config.thinkingSpeed);
            logger.info(`Bot ${this.bot.name} waiting ${journeyDelay/1000} seconds after collecting journey for UI effects...`);
            await new Promise(resolve => setTimeout(resolve, journeyDelay));
            break;
          case 'placeOmToken':
            this._executePlaceOmAction(action.params);
            // Regular delay for other actions, adjusted by thinking speed
            await new Promise(resolve => setTimeout(resolve, Math.max(50, 500 / this.config.thinkingSpeed)));
            break;
          case 'endTurn':
            this.socket.emit('endTurn');
            // No delay needed after ending turn
            break;
          default:
            logger.warn(`Bot ${this.bot.name} got unknown action: ${action.action}. Skipping.`);
            // Regular delay for unknown actions, adjusted by thinking speed
            await new Promise(resolve => setTimeout(resolve, Math.max(50, 500 / this.config.thinkingSpeed)));
        }

        // No additional delay needed between actions since we've added specific delays
      }

      // If no endTurn action was included, end the turn explicitly
      const hasEndTurn = validActions.some(action => action.action === 'endTurn');
      if (!hasEndTurn) {
        logger.info(`Bot ${this.bot.name} ending turn explicitly (no endTurn action found)`);
        this.socket.emit('endTurn');
      }
      
      // Reset isMyTurn flag after completing the turn to allow detection of consecutive turns
      // This is critical for when a bot advances on the Om track and immediately becomes first player next round
      logger.info(`Bot ${this.bot.name} resetting isMyTurn flag after turn completion`);
      this.isMyTurn = false;
    } catch (error) {
      logger.error(`Error during bot turn: ${error.message}`);
      // End turn on error to prevent getting stuck
      this.socket.emit('endTurn');
      // Also reset isMyTurn flag on error
      this.isMyTurn = false;
    }
  }

  /**
   * Execute a move player action
   * @param {object} params - Move parameters
   * @private
   */
  _executeMoveAction(params) {
    try {
      logger.info(`Bot ${this.bot.name} moving to ${params.path.join(' -> ')}`);

      // Convert the params to match what the server expects
      // The server expects 'travelCardIds' but the strategy uses 'cardIds'
      const moveParams = {
        path: params.path,
        travelCardIds: params.cardIds, // Convert cardIds to travelCardIds
        extraHopCount: params.extraHopCount || 0,
        isTriathlon: params.isTriathlon || false
      };

      logger.info(`Bot ${this.bot.name} sending move with cards: ${moveParams.travelCardIds}`);
      this.socket.emit('movePlayer', moveParams);
    } catch (error) {
      logger.error(`Error executing move action: ${error.message}`);
      this.socket.emit('endTurn');
    }
  }

  /**
   * Execute a pick cards action
   * @param {object} params - Pick cards parameters
   * @private
   */
  _executePickCardsAction(params) {
    try {
      logger.info(`Bot ${this.bot.name} picking cards: ${JSON.stringify(params)}`);
      this.socket.emit('pickCards', params);
    } catch (error) {
      logger.error(`Error executing pick cards action: ${error.message}`);
      this.socket.emit('endTurn');
    }
  }

  /**
   * Execute a collect journey action
   * @param {object} params - Collect journey parameters
   * @private
   */
  _executeCollectJourneyAction(params) {
    try {
      logger.info(`Bot ${this.bot.name} collecting journey: ${params.journeyCardId}`);
      this.socket.emit('collectJourney', params);
    } catch (error) {
      logger.error(`Error executing collect journey action: ${error.message}`);
      this.socket.emit('endTurn');
    }
  }

  /**
   * Execute a place OM token action
   * @param {object} params - Place OM parameters
   * @private
   */
  _executePlaceOmAction(params) {
    try {
      logger.info(`Bot ${this.bot.name} placing OM token: ${JSON.stringify(params)}`);
      this.socket.emit('placeOmToken', params);
    } catch (error) {
      logger.error(`Error executing place OM action: ${error.message}`);
      this.socket.emit('endTurn');
    }
  }

  /**
   * Handle card selection requests
   * @param {object} data - Card selection data
   * @private
   */
  _handleCardSelection(data) {
    try {
      // Use the bot's strategy to select cards
      const selectedCards = this.bot.strategy.selectCards(this.bot, data);

      // Send the selection back to the server
      this.socket.emit('selectTravelCards', selectedCards);
    } catch (error) {
      logger.error(`Error handling card selection: ${error.message}`);
      // Send an empty selection as fallback
      this.socket.emit('selectTravelCards', []);
    }
  }

  /**
   * Stop the bot client
   */
  stop() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    logger.info(`Bot client ${this.config.name} stopped`);
  }
}

module.exports = BotClient;