/**
 * DefensiveStrategy.js
 * A defensive strategy that prioritizes collecting journey cards with minimal resources.
 * This strategy focuses on one journey card at a time, collecting required resources efficiently.
 * General turn rules:
 *  1. Player can always move to any location using travel cards.
 *  2. Player can perform one of the following actions:
 *    i. Collect 1 journey card (if possible)
 *    ii. Pick 2 travel cards (if possible)
 *  3. Player can perform trade using their character card.
 *  All of the above actions can be performed in any order and only once in a turn.
 *
 * Defensive strategy:
 *  1. Select a journey card to target.
 *  2. Calculate the required om tokens and energy cubes to collect the journey card.
 *  3. Om token collection:
 *    i. If player does not have enough om tokens, move to the closest jyotirlinga with om tokens.
 *    ii. If player does not have travel cards in hand to reach the jyotirlinga, check if any face-up travel cards can help reach the jyotirlinga.
 *    iii. If no face-up travel cards can help, go to step 4.
 *  4. Energy cube collection:
 *    i. If player does not have enough energy cubes, move to the closest location with energy cubes.
 *    ii. If player does not have travel cards in hand to reach the location, check if any face-up travel cards can help reach the location.
 *    iii. If no face-up travel cards can help, choose the closest location with energy cubes reachable via current travel cards.
 *  5. Journey card collection:
 *    i. If player is at the location of the journey card, collect it. After collecting the card, choose the next journey card to target and move to collect resources (om token , energy cube resources) for the next journey card.
 *    ii. If player is not at the location of the journey card, move to the location.
 *  6. Travel card collection:
 *    i. Collect the travel cards at any point during (3) or (4) if needed to travel to the targeted location.
 *    ii. Else:
 *        If travelled during (3) then choose the next turn target location for energy cube collection
 *        If travelled during (4) then choose the next turn target location for journey card collection
 *        Choose travel cards to pick which will enable exact travel to the next turn target location.
 *    iii. Always ensure to pick 2 travel cards if hand permits regardless of whether 1 travel card being strictly required except if performed 5
 *    iv. Always prioritize cards that provide additional points due to current global event and that can enable exact travel in combination with/without other cards. If there are cards available then pick and use them in the current turn even if there are travel cards in hand that enable exact travel.
 *
 * NOTICE: This strategy is designed to throw errors rather than silently continuing
 * when critical issues are found. This helps identify and fix server integration issues quickly.
 */

const BaseStrategy = require('./BaseStrategy');
const PathCalculator = require('../PathCalculator');
const { journeyDeckOuter, journeyDeckInner } = require('../../boardData');

// Helper function to get OM temp count, handling both array and number formats
const getOmTempCount = (player) => {
  if (!player) return 0;
  // If omTemp is an array, sum the values instead of just counting the length
  if (Array.isArray(player.omTemp)) {
    return player.omTemp.reduce((sum, val) => sum + val, 0);
  }
  // Otherwise treat it as a scalar value
  return player.omTemp || 0;
};

// Helper function to count total Om tokens (inner, outer, and temp)
const getTotalOmCount = (player) => {
  if (!player) return 0;

  // Count temp Om tokens
  const tempOm = getOmTempCount(player);

  // Count inner Om tokens
  const innerOm = player.omTokens?.inner || (player.omSlotsInner?.filter(x => x > 0)?.length || 0);

  // Count outer Om tokens
  const outerOm = player.omTokens?.outer || (player.omSlotsOuter?.filter(x => x > 0)?.length || 0);

  return tempOm + innerOm + outerOm;
};

// Helper function to calculate the OM token cost for the next journey card
// Costs follow pattern: 1-1-2-3 for 2-3 players, 0-1-1-2 for 4 players
const calculateOmCostForNextCard = (count, playerCount) => {
  if (playerCount === 4) {
    if (count === 0) return 0; // First card (free)
    if (count === 1) return 1; // Second card
    if (count === 2) return 1; // Third card
    if (count === 3) return 2; // Fourth card
  } else {
    if (count === 0) return 1; // First card
    if (count === 1) return 1; // Second card
    if (count === 2) return 2; // Third card
    if (count === 3) return 3; // Fourth card
  }
  return 0; // No more slots
};

// Helper function to evaluate Om turn track position advantage
const evaluateOmTurnTrackAdvantage = (bot, state) => {
  if (!state.omTrack || !bot) return 0;
  
  const currentOmSpace = bot.omSpace || 0;
  const currentStackPosition = bot.omStackPosition || 0;
  
  // Higher Om space is better
  // Lower stack position (closer to top) is better
  // Calculate a score that prioritizes Om space first, then stack position
  // Higher return value means better position
  return (currentOmSpace * 10) + (9 - Math.min(currentStackPosition, 9));
};

class DefensiveStrategy extends BaseStrategy {
  /**
   * Create a new defensive strategy
   * @param {object} config - Strategy configuration
   */
  constructor(config = {}) {
    super({
      name: 'defensive',
      config
    });

    this.pathCalculator = new PathCalculator();

    // Track failed attempts to avoid infinite loops
    this._failedAttempts = 0;

    // Memory management for move history
    this.moveHistoryLimit = config.moveHistoryLimit || 50;

    // Track turn state
    this._turnState = {
      hasMoved: false,
      hasCollectedCards: false,
      hasCollectedTravelCards: false,
      moveToCollectJourney: false,
      justPickedCards: false,
      pickedCardType: null,
      turnNumber: 0,
      usedVehicle: false,
      handledGlobalEvent: false,
      hasCollectedEventCards: false,
      hasUsedVehicle: false,
      hasCollectedJourney: false,
      hasReachedMaxHand: false,
      hasPerformedBadMovement: false,
      hasPickedCards: false,
      triedCardPickCount: 0,
      lastVehicleEvaluation: null,
      hasTriedOmToken: false,
      movementStatus: null
    };

    // Target journey card to work on
    this._currentTarget = null;

    // Resource collection plan
    this._resourcePlan = {
      needsOmTokens: false,
      needsEnergyCubes: false,
      targetJyotirlinga: null,
      targetEnergyLocation: null,
      targetEnergyDistance: null,
      fallbackEnergyLocation: null,
      fallbackEnergyDistance: null,
      targetVehicle: null,
      prioritizeOmTokens: false
    };

    // Current character abilities
    this._characterAbilities = null;

    // Om track strategy
    this._omTrackStrategy = {
      shouldImprovePosition: false,
      targetOmSpace: 0,
      minimumRequiredOmTokens: 1
    };

    // Debug counters
    this._debugCounters = {
      totalTurns: 0,
      failedTurns: 0,
      successfulTurns: 0,
      collectedJourneys: 0
    };

    // For movement planning
    this._lastTargetNodeId = null;
    this._moveAttempts = {};
  }

  /**
   * Log detailed state information for debugging purposes
   * @param {Bot} bot - The bot instance
   * @param {object} state - The current player state
   * @private
   */
  _logDetailedState(bot, state) {
    if (!bot || !state) return;

    // Log basic player info
    const player = state.player;
    if (player) {
      bot.logger.info(`Bot ${bot.name} state: Position=${player.position}, Hand=${player.hand?.length || 0} cards`);

      // Log OM tokens
      const innerOm = player.omTokens?.inner || (player.omSlotsInner?.filter(x => x > 0)?.length || 0);
      const outerOm = player.omTokens?.outer || (player.omSlotsOuter?.filter(x => x > 0)?.length || 0);
      bot.logger.info(`OM Tokens: Inner=${innerOm}, Outer=${outerOm}`);
      
      // Log Om track position
      bot.logger.info(`Om Track Position: Space=${bot.omSpace || 0}, Stack Position=${bot.omStackPosition || 0}`);

      // Log energy cubes if they exist
      if (player.energyCubes) {
        bot.logger.info(`Energy: Bhakti=${player.energyCubes.bhakti || 0}, Gnana=${player.energyCubes.gnana || 0}, Karma=${player.energyCubes.karma || 0}, Artha=${player.energyCubes.artha || 0}`);
      }

      // Log character info if available
      if (player.character) {
        bot.logger.info(`Character: ${player.character.name}`);
      }
    }

    // Log resource plan
    if (this._resourcePlan) {
      if (this._resourcePlan.needsOmTokens) {
        bot.logger.info(`Resource plan: Needs OM tokens, target Jyotirlinga=${this._resourcePlan.targetJyotirlinga}`);
      }
      if (this._resourcePlan.needsEnergyCubes) {
        bot.logger.info(`Resource plan: Needs energy cubes, target location=${this._resourcePlan.targetEnergyLocation?.id}`);
        if (this._resourcePlan.fallbackEnergyLocation) {
          bot.logger.info(`Resource plan: Fallback energy location=${this._resourcePlan.fallbackEnergyLocation?.id}`);
        }
      }
    }
    
    // Log Om track strategy
    if (this._omTrackStrategy && this._omTrackStrategy.shouldImprovePosition) {
      bot.logger.info(`Om Track Strategy: Improve position, target space=${this._omTrackStrategy.targetOmSpace}, need ${this._omTrackStrategy.minimumRequiredOmTokens} OM tokens`);
    }

    // Log current journey target
    if (this._currentTarget) {
      // Format the required cubes for display
      let requiredCubesStr = '';
      if (this._currentTarget.requiredCubes) {
        const reqCubes = this._currentTarget.requiredCubes;
        requiredCubesStr = Object.entries(reqCubes)
          .filter(([_, count]) => count > 0)
          .map(([type, count]) => `${type}=${count}`)
          .join(', ');
        requiredCubesStr = requiredCubesStr ? ` (Requires: ${requiredCubesStr})` : '';
      }

      bot.logger.info(`Current journey target: ${this._currentTarget.id} at location ${this._currentTarget.locationId}${requiredCubesStr}`);
    }
  }

  /**
   * Count the number of travel cards in a bot's hand
   * @param {Bot} bot - The bot instance
   * @returns {number} Number of travel cards
   * @private
   */
  _countTravelCards(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !state.player.hand) return 0;

    const hand = state.player.hand;
    return hand.filter(card => card.type === 'travel' || card.cardType === 'travel').length;
  }

  /**
   * Check if a bot has any travel cards
   * @param {Bot} bot - The bot instance
   * @returns {boolean} True if the bot has travel cards
   * @private
   */
  _hasTravelCards(bot) {
    return this._countTravelCards(bot) > 0;
  }

  /**
   * Plan the next turn for a bot
   * @param {Bot} bot - The bot this strategy is planning for
   * @returns {Promise<object>} Action plan with 'action' and 'params'
   */
  async planTurn(bot) {
    // Get the current player state
    const state = bot.getPlayerState();
    if (!state || !state.player) {
      bot.logger.error(`Bot ${bot.name} missing player state for planning`);
      // Just end the turn if we can't access the state
      return [{ action: 'endTurn', params: {} }];
    }

    try {
      // Check if this is a new turn
      if (bot.turnsPlayed > this._turnState.turnNumber) {
        bot.logger.info(`Bot ${bot.name} starting new turn ${bot.turnsPlayed} (previous: ${this._turnState.turnNumber})`);

        // Reset turn state for the new turn
        this._turnState = {
          hasMoved: false,
          hasCollectedCards: false,
          hasCollectedTravelCards: false,
          moveToCollectJourney: false,
          justPickedCards: false,
          pickedCardType: null,
          turnNumber: bot.turnsPlayed,
          usedVehicle: false,
          handledGlobalEvent: false,
          avoidAirports: false, // Reset the avoidAirports flag for the new turn
          avoidNorthEast: false // Reset the avoidNorthEast flag for the new turn
        };

        // Reset resource plan when a new turn starts
        this._resourcePlan = {
          needsOmTokens: false,
          needsEnergyCubes: false,
          targetJyotirlinga: null,
          targetEnergyLocation: null,
          prioritizeOmTokens: false // Reset the prioritizeOmTokens flag for the new turn
        };

        // Check if we're in a 3-player game with competitive Om token collection situation
        const isThreePlayerGame = state.opponents && state.opponents.length === 2;
        if (isThreePlayerGame) {
          // Get our total Om token count
          const selfOmCount = getTotalOmCount(state.player);
          
          // Count temp OM tokens specifically (max is 3)
          const tempOmCount = getOmTempCount(state.player);
          
          // Check if any other player has 2 or more Om tokens
          let otherPlayerHasMultipleOm = false;
          for (const opponent of state.opponents) {
            bot.logger.info(`DEFENSIVE STRATEGY: Opponent: ${JSON.stringify(opponent)}, position: ${opponent.position}, omTemp: ${opponent.omTemp}, omSlotsInner: ${opponent.omSlotsInner}, omSlotsOuter: ${opponent.omSlotsOuter}, omTokens: ${JSON.stringify(opponent.omTokens)}`);
            
            // Calculate Om count - handle both data formats
            let opponentOmCount = 0;
            
            // Handle direct array properties first (new format)
            if (Array.isArray(opponent.omTemp)) {
              opponentOmCount += opponent.omTemp.reduce((sum, val) => sum + val, 0);
            }
            if (Array.isArray(opponent.omSlotsInner)) {
              opponentOmCount += opponent.omSlotsInner.reduce((sum, val) => sum + val, 0);
            }
            if (Array.isArray(opponent.omSlotsOuter)) {
              opponentOmCount += opponent.omSlotsOuter.reduce((sum, val) => sum + val, 0);
            }
            
            // If arrays were empty or not present, try the nested omTokens object (old format)
            if (opponentOmCount === 0 && opponent.omTokens) {
              opponentOmCount = (opponent.omTokens.inner || 0) + (opponent.omTokens.outer || 0) + (opponent.omTokens.temp || 0);
            }
            
            // Finally, check if there's a totalOmCount property
            if (opponentOmCount === 0 && opponent.totalOmCount !== undefined) {
              opponentOmCount = opponent.totalOmCount;
            }

            if (opponentOmCount >= 2) {
              otherPlayerHasMultipleOm = true;
              break;
            }
          }

          // If we have less than 4 Om tokens AND we haven't maxed out temp slots (max 3) 
          // AND another player has 2+ Om tokens, prioritize Om collection
          if (selfOmCount < 4 && tempOmCount < 3 && otherPlayerHasMultipleOm) {
            bot.logger.info(`DEFENSIVE STRATEGY: In 3-player game, another player has 2+ Om tokens while we have ${selfOmCount} (${tempOmCount} in temp slots). Prioritizing Om token collection!`);
            this._resourcePlan.prioritizeOmTokens = true;
            this._resourcePlan.needsOmTokens = true;
          } else if (tempOmCount >= 3 && otherPlayerHasMultipleOm) {
            bot.logger.info(`DEFENSIVE STRATEGY: Not prioritizing Om tokens despite competition because temp slots are full (${tempOmCount}/3).`);
          }
        }

        // Select a new journey card target at the start of each turn
        bot.logger.info(`Bot ${bot.name} selecting journey card target at start of turn ${bot.turnsPlayed}`);
        await this._selectTargetJourneyCard(bot);

        // Log whether we have a target after selection
        if (this._currentTarget) {
          bot.logger.info(`Bot ${bot.name} selected target: ${this._currentTarget.id} at location ${this._currentTarget.locationId}`);
        } else {
          bot.logger.warn(`Bot ${bot.name} failed to select a journey target`);
        }

        // Update resource plan based on the new target
        await this._updateResourcePlan(bot);

        // If we need to prioritize Om tokens, adjust the resource plan
        if (this._resourcePlan.prioritizeOmTokens) {
          // Find the closest jyotirlinga
          const jyotirlingaIds = this._getJyotirlingas(state);
          const currentPosition = state.player.position || state.player.currentNodeId;

          if (jyotirlingaIds.length > 0) {
            let closestJyotirlinga = null;
            let shortestDistance = Infinity;

            for (const jId of jyotirlingaIds) {
              const distance = this._estimateTravelDistance(state, currentPosition, jId.id);
              bot.logger.debug(`Distance from ${currentPosition} to jyotirlinga ${jId.id}: ${distance} hops`);
              if (distance < shortestDistance) {
                shortestDistance = distance;
                closestJyotirlinga = jId;
              }
            }

            if (closestJyotirlinga) {
              this._resourcePlan.targetJyotirlinga = closestJyotirlinga;
              bot.logger.info(`Om token collection priority: targeting Jyotirlinga at ${closestJyotirlinga.id}`);
            }
          }
        }

        // Log extended debug info at start of turn
        this._logDetailedState(bot, state);
      }

      // Always check current resource needs before planning
      const resourceNeeds = await this._calculateResourceNeed(bot);

      // STRATEGY POINT iv: Check if we should pick rewarded vehicle cards and use them immediately
      // This implements step 6.iv in the defensive strategy
      const globalEventForReward = this._getCurrentGlobalEvent(bot);
      const rewardedVehicle = globalEventForReward ? this._getRewardedVehicleFromGlobalEvent(globalEventForReward) : null;
      
      if (rewardedVehicle && !this._turnState.hasMoved && !this._turnState.hasCollectedCards) {
        // We're at the start of our turn and there might be rewarded vehicles
        bot.logger.info(`STRATEGY POINT iv: Checking for face-up ${rewardedVehicle} cards rewarded by current global event`);
        
        // Check for face-up travel cards with the rewarded vehicle type
        const faceUpTravel = state.faceUpTravel || [];
        const rewardedVehicleCards = faceUpTravel.filter(card => card.vehicle === rewardedVehicle);
        
        if (rewardedVehicleCards.length > 0) {
          bot.logger.info(`STRATEGY POINT iv: Found ${rewardedVehicleCards.length} face-up ${rewardedVehicle} cards that provide additional points due to current global event`);
          
          // Only consider the first rewarded vehicle card since the reward only applies once
          const firstRewardedCard = rewardedVehicleCards[0];
          bot.logger.info(`STRATEGY POINT iv: Only considering the first ${rewardedVehicle} card: ${firstRewardedCard.id} (value: ${firstRewardedCard.value}) since the reward only applies once`);
          
          // Create a filtered list instead of reassigning
          const filteredRewardedCards = [firstRewardedCard];
          
          // Check if we have room in our hand to pick cards
          const handSize = state.player.hand ? state.player.hand.length : 0;
          
          if (handSize < 4) {
            // Check if we're planning to collect a journey card this turn
            // If we're at a journey location and can collect it, we should prioritize that
            const canCollectJourney = this._canCollectCurrentTargetJourney(bot);
            
            if (!canCollectJourney) {
              // We're not collecting a journey, so we can pick rewarded vehicle cards
              bot.logger.info(`STRATEGY POINT iv: Trying to pick rewarded ${rewardedVehicle} cards and use them in the same turn`);
              
              // Instead of recursively calling evaluateCardPicks, directly create a card pick action
              // to select the rewarded vehicle card
              const cardPickAction = {
                action: 'pickCards',
                params: {
                  type: 'travel',
                  pickFromFaceUp: [firstRewardedCard.id]
                }
              };
              
              if (cardPickAction && cardPickAction.params.pickFromFaceUp) {
                // Check if we're actually picking rewarded vehicle cards
                const pickingRewardedCards = cardPickAction.params.pickFromFaceUp.some(id => 
                  filteredRewardedCards.some(card => card.id === id)
                );
                
                if (pickingRewardedCards) {
                  // We're picking at least one rewarded vehicle card
                  bot.logger.info(`STRATEGY POINT iv: Picking ${rewardedVehicle} cards: ${cardPickAction.params.pickFromFaceUp.join(', ')}`);
                  
                  // Create a hypothetical updated state after picking these cards
                  const updatedState = {...state};
                  if (updatedState.player && updatedState.player.hand) {
                    // Add the rewarded vehicle cards we're picking to our hypothetical hand
                    const pickedCards = filteredRewardedCards.filter(card => 
                      cardPickAction.params.pickFromFaceUp.includes(card.id)
                    );
                    updatedState.player.hand = [...updatedState.player.hand, ...pickedCards];
                    
                    // Now try to plan a move using our updated hypothetical hand
                    const moveAction = await this._planStrategicMove(bot, updatedState);
                    
                    if (moveAction && moveAction.action === 'movePlayer') {
                      // Check if the move would use the rewarded vehicle cards
                      const movingWithRewardedVehicle = moveAction.params.cardIds && 
                        moveAction.params.cardIds.some(id => pickedCards.some(card => card.id === id));
                      
                      if (movingWithRewardedVehicle) {
                        // Perfect! We can pick rewarded vehicle cards and use them in the same turn
                        bot.logger.info(`STRATEGY POINT iv: Can use picked ${rewardedVehicle} cards for movement in the same turn!`);
                        
                        // Return both actions: pick cards first, then move
                        const turnActions = [cardPickAction];
                        
                        // Update the card IDs in the move action to ensure they are valid
                        // (only use cards that will be in hand after picking)
                        const validCardIds = moveAction.params.cardIds.filter(id => {
                          // Card is either in current hand
                          const inCurrentHand = state.player.hand.some(card => card.id === id);
                          // Or it's one of the cards we're picking
                          const isPicking = cardPickAction.params.pickFromFaceUp.includes(id);
                          return inCurrentHand || isPicking;
                        });
                        
                        // Only proceed if we have valid cards for the move
                        if (validCardIds.length > 0 && validCardIds.length === moveAction.params.cardIds.length) {
                          turnActions.push(moveAction);
                          this._turnState.hasMoved = true;
                          this._turnState.hasCollectedCards = true;
                          this._turnState.hasCollectedTravelCards = true;
                          
                          // End the turn
                          turnActions.push({ action: 'endTurn', params: {} });
                          
                          bot.logger.info(`STRATEGY POINT iv: Planning turn with ${turnActions.length} actions: pick ${cardPickAction.params.pickFromFaceUp.length} cards, use them to move, end turn`);
                          return turnActions;
                        }
                      }
                    }
                  }
                }
              } else {
                bot.logger.info(`STRATEGY POINT iv: Bot can collect journey card at current location, will prioritize that over picking ${rewardedVehicle} cards`);
              }
            } else {
              bot.logger.info(`STRATEGY POINT iv: Hand is full (${handSize}/4), cannot pick ${rewardedVehicle} cards`);
            }
          } else {
            bot.logger.info(`STRATEGY POINT iv: No face-up ${rewardedVehicle} cards found`);
          }
        } else {
          bot.logger.info(`STRATEGY POINT iv: No face-up ${rewardedVehicle} cards found`);
        }
      }

      // SPECIAL CASE: Check if we should only collect travel cards this turn
      // Requirements:
      // 1. Bot has targeted a journey card
      // 2. Bot has required OM tokens for that journey card
      // 3. Bot has required energy cubes for that journey card
      // 4. Bot has exactly 5 energy cubes (at limit)
      // 5. Bot doesn't have travel cards to enable exact travel to the journey card location
      if (this._currentTarget &&
          !resourceNeeds.needsOmTokens &&
          !resourceNeeds.needsEnergyCubes) {

        const state = bot.getPlayerState();
        const player = state.player;
        const totalEnergyCubes = this._countEnergyCubes(player);

        // Check energy cube limit (exactly 5)
        if (totalEnergyCubes === 5) {
          // Check if we can travel exactly to the target
          const currentTravelCards = this._getTravelCards(bot);
          const targetNodeId = this._currentTarget.locationId;
          const currentNodeId = player.position || player.currentNodeId;
          const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, targetNodeId);

          let canExactTravel = false;
          try {
            const pathToTarget = this._calculatePathToTarget(
              state,
              currentNodeId,
              targetNodeId,
              currentTravelCards
            );

            canExactTravel = pathToTarget && pathToTarget.cardValue === exactDistanceNeeded;
          } catch (error) {
            bot.logger.warn(`Path calculation failed: ${error.message}`);
          }

          if (!canExactTravel) {
            bot.logger.info(`SPECIAL STRATEGY: Bot ${bot.name} has target journey card, all resources, at energy limit (${totalEnergyCubes}/5), but no exact travel cards.`);
            bot.logger.info(`SPECIAL STRATEGY: Bot will only collect travel cards this turn to enable exact travel next turn.`);

            // Initialize the turnActions array before adding actions
            const turnActions = [];

            // Skip movement this turn, only collect travel cards
            const cardPickAction = await this.evaluateCardPicks(bot, 'travel');
            if (cardPickAction) {
              this._turnState.hasCollectedCards = true;
              turnActions.push(cardPickAction);

              // End turn after collecting cards
              bot.logger.info(`SPECIAL STRATEGY: Bot ${bot.name} collected travel cards and will end turn without moving.`);
              turnActions.push({ action: 'endTurn', params: {} });

              // Log the actions taken by the special strategy
              bot.logger.info(`SPECIAL STRATEGY: Bot ${bot.name} planning turn with ${turnActions.length} actions (moved=false, cards=true)`);
              const actionTypes = turnActions.map(a => a.action).join(', ');
              bot.logger.info(`SPECIAL STRATEGY: Returning ${turnActions.length} actions: ${actionTypes}`);

              return turnActions;
            }
          }
        }
      }

      // Track current player state to determine if a journey card can be collected
      const canCollectJourney = this._canCollectCurrentTargetJourney(bot);

      // Array for returning multiple actions in one turn
      const turnActions = [];

      // Get global event using our helper method that tries multiple approaches
      const globalEvent = this._getCurrentGlobalEvent(bot);

      if (globalEvent && !this._turnState.handledGlobalEvent) {
        // Handle the global event first
        const eventAction = this._handleGlobalEvent(bot, globalEvent);
        if (eventAction) {
          this._turnState.handledGlobalEvent = true;
          turnActions.push(eventAction);

          // If the event action is a movement, mark that we've moved
          if (eventAction.action === 'movePlayer') {
            this._turnState.hasMoved = true;
          }

          // Some events may block further actions, so return immediately
          if (globalEvent.effect === 'no_travel' || globalEvent.effect === 'double_trade_no_travel') {
            // If we can't travel, just end the turn
            turnActions.push({ action: 'endTurn', params: {} });
            return turnActions;
          }
        }
      }

      // Log the current state and available actions
      bot.logger.info(`Bot ${bot.name} planning with state: moved=${this._turnState.hasMoved}, cards=${this._turnState.hasCollectedCards}, resources=${JSON.stringify(resourceNeeds)}`);

      // If we have no valid actions to take, log detailed state and end turn
      if (!this._hasTravelCards(bot) && this._turnState.hasCollectedCards) {
        bot.logger.warn(`Bot ${bot.name} has no travel cards and already collected cards. State: ${JSON.stringify({
          position: state.player.position,
          handSize: state.player.hand?.length || 0,
          hasMoved: this._turnState.hasMoved,
          hasCollectedCards: this._turnState.hasCollectedCards,
          resourceNeeds
        })}`);
        turnActions.push({ action: 'endTurn', params: {} });
        return turnActions;
      }

      // Check if the bot can collect a journey card at its current location
      if (canCollectJourney) {
        bot.logger.info(`Bot ${bot.name} can collect journey card at current location`);

        // Add the journey collection action
        const journeyAction = await this._collectTargetJourneyCard(bot);
        if (journeyAction) {
          turnActions.push(journeyAction);

          // Since we're collecting a journey card, we can't also collect travel cards in this turn
          this._turnState.hasCollectedCards = true;
          this._turnState.justPickedCards = true;
          this._turnState.pickedCardType = 'journey';

          // Update our target after collecting
          await this._selectTargetJourneyCard(bot);
          await this._updateResourcePlan(bot);

          // After collecting a journey card, we should try to move toward resources for the next journey
          // This implements strategy point 5.i - target the resource location needed for the next journey card
          if (!this._turnState.hasMoved) {
            // Update resource plan to ensure we're targeting resources for the next journey
            await this._updateResourcePlan(bot);

            // Force the strategy to prioritize resource collection over journey collection
            // This ensures we go to resource locations first
            if (this._resourcePlan) {
              // Temporarily increase the resource needs to ensure we prioritize resources
              const originalNeedsOmTokens = this._resourcePlan.needsOmTokens;
              const originalNeedsEnergyCubes = this._resourcePlan.needsEnergyCubes;

              // Force resource needs to be true to prioritize resource locations
              this._resourcePlan.needsOmTokens = this._resourcePlan.missingOmTokens > 0;
              this._resourcePlan.needsEnergyCubes = this._resourcePlan.totalMissingCubes > 0;

              // Plan the move with resource priority
              const moveAction = await this._planStrategicMove(bot);

              // Restore original values
              this._resourcePlan.needsOmTokens = originalNeedsOmTokens;
              this._resourcePlan.needsEnergyCubes = originalNeedsEnergyCubes;

              if (moveAction) {
                turnActions.push(moveAction);
                this._turnState.hasMoved = true;
                bot.logger.info(`After journey collection, targeting resource location for next journey card`);
              }
            } else {
              // If no resource plan, just do normal strategic move
              const moveAction = await this._planStrategicMove(bot);
              if (moveAction) {
                turnActions.push(moveAction);
                this._turnState.hasMoved = true;
              }
            }
          }
        }
      }

      // If we haven't moved this turn, try to move
      if (!this._turnState.hasMoved) {
        // Plan a strategic move
        const moveAction = await this._planStrategicMove(bot);
        if (moveAction) {
          // Add the move action, but don't mark as moved yet - we'll verify after execution
          turnActions.push(moveAction);

          // If the move action is picking cards, mark that we've collected cards
          if (moveAction.action === 'pickCards') {
            this._turnState.hasCollectedCards = true;
            this._turnState.justPickedCards = true;
            this._turnState.pickedCardType = 'travel';

            // Check if we can add a strategic move after picking cards
            const hasTravelCards = this._hasTravelCards(bot);

            if (hasTravelCards) {
              // Get an updated state after hypothetically picking cards
              const updatedState = {...state};
              if (updatedState.player && updatedState.player.hand) {
                // Add the face-up cards we're picking to our hypothetical hand
                const faceUpCardIds = moveAction.params.pickFromFaceUp || [];
                if (faceUpCardIds.length > 0 && state.faceUpTravel) {
                  const faceUpCards = state.faceUpTravel.filter(c => faceUpCardIds.includes(c.id));
                  updatedState.player.hand = [...updatedState.player.hand, ...faceUpCards];
                }

                // Now plan a move with our updated hypothetical hand
                const secondMoveAction = await this._planStrategicMove(bot, updatedState);
                if (secondMoveAction && secondMoveAction.action === 'movePlayer') {
                  // Make sure this move is different from any previous move actions in this turn
                  const isDifferentMove = !turnActions.some(action =>
                    action.action === 'movePlayer' &&
                    JSON.stringify(action.params.path) === JSON.stringify(secondMoveAction.params.path)
                  );

                  if (isDifferentMove) {
                    bot.logger.info(`Second move plan: path=${secondMoveAction.params.path.join(' -> ')}, using cards: ${secondMoveAction.params.cardIds.join(', ')}`);
                    bot.logger.info(`Added strategic move action after picking travel cards: ${secondMoveAction.action}`);

                    // Only add this second move if it uses cards we will have after picking
                    const willHaveCards = secondMoveAction.params.cardIds.every(cardId => {
                      // Check if it's in our current hand
                      const inCurrentHand = state.player.hand.some(c => c.id === cardId);
                      // Or it's one of the cards we're picking
                      const isPicking = faceUpCardIds.includes(cardId);
                      return inCurrentHand || isPicking;
                    });

                    if (willHaveCards) {
                      turnActions.push(secondMoveAction);
                    } else {
                      bot.logger.warn(`Skipping second move action because it uses cards we won't have: ${secondMoveAction.params.cardIds.join(', ')}`);
                    }
                  } else {
                    bot.logger.warn(`Skipping duplicate move action with path ${secondMoveAction.params.path.join(' -> ')}`);
                  }
                }
              }
            }
          }
          // Check if we should pick cards after a planned move action
          else if (moveAction.action === 'movePlayer' && !this._turnState.hasCollectedCards) {
            bot.logger.info(`Bot ${bot.name} planning to pick journey or travel cards after movement.`);
            const handSize = state.player.hand ? state.player.hand.length : 0;
            if (this._turnState.moveToCollectJourney) {
              // If we're moving to collect a journey card, don't pick cards
              // and instead collect the journey card
              const journeyAction = {
                action: 'collectJourney',
                params: {
                  journeyCardId: this._currentTarget.id,
                  journeyType: this._currentTarget.cardType
                }
              };

              bot.logger.info(`Collecting journey card ${this._currentTarget.id} of type ${this._currentTarget.cardType}`);
              turnActions.push(journeyAction);
              this._turnState.hasCollectedCards = true;
              bot.logger.info(`Added journey collection action after movement: ${journeyAction.action}`);
            }
            // Always try to pick cards if we have space in hand (following strategy rule 6.iii)
            else if (handSize < 4) {
              bot.logger.info(`Bot ${bot.name} planning to pick cards after movement. Current hand size: ${handSize}`);
              const cardAction = await this.evaluateCardPicks(bot);
              if (cardAction) {
                this._turnState.hasCollectedCards = true;
                if (cardAction.params.type === 'travel') {
                  this._turnState.justPickedCards = true;
                  this._turnState.pickedCardType = 'travel';
                  this._turnState.hasCollectedTravelCards = true;
                }
                turnActions.push(cardAction);
                bot.logger.info(`Added card picking action after movement: ${cardAction.action}`);
              }
            }
          }
        } else {
          // If we couldn't move and haven't collected cards, collect cards
          const cardAction = await this.evaluateCardPicks(bot, 'travel');
          if (cardAction) {
            this._turnState.hasCollectedCards = true;

            if (cardAction.params.type === 'travel') {
              this._turnState.justPickedCards = true;
              this._turnState.pickedCardType = 'travel';
              this._turnState.hasCollectedTravelCards = true;
            }

            turnActions.push(cardAction);

            // After picking travel cards, try to move again
            // But only if the player's hand was empty before
            if (cardAction.params.type === 'travel' && (!state.player.hand || state.player.hand.length === 0)) {
              // Get an updated state after hypothetically picking cards
              const updatedState = {...state};
              if (updatedState.player) {
                // Initialize hand if needed
                if (!updatedState.player.hand) {
                  updatedState.player.hand = [];
                }

                // Add the face-up cards we're picking to our hypothetical hand
                const faceUpCardIds = cardAction.params.pickFromFaceUp || [];
                if (faceUpCardIds.length > 0 && state.faceUpTravel) {
                  const faceUpCards = state.faceUpTravel.filter(c => faceUpCardIds.includes(c.id));
                  updatedState.player.hand = [...updatedState.player.hand, ...faceUpCards];
                }

                // If we're picking from the deck, add hypothetical average value cards
                if (cardAction.params.pickFromTop) {
                  // Add 1-2 generic travel cards with average value
                  const deckCardCount = faceUpCardIds.length < 2 ? 1 : 0;
                  for (let i = 0; i < deckCardCount; i++) {
                    updatedState.player.hand.push({
                      id: `T${Math.floor(Math.random() * 100)}`,
                      type: 'travel',
                      value: 2, // Average value
                      vehicle: 'unknown'
                    });
                  }
                }

                // Now try to plan a move with these hypothetical cards
                if (updatedState.player.hand.length > 0) {
                  const moveAfterPickAction = await this._planStrategicMove(bot, updatedState);
                  if (moveAfterPickAction && moveAfterPickAction.action === 'movePlayer') {
                    bot.logger.info(`Planning move after card picking: ${moveAfterPickAction.params.path.join(' -> ')}`);
                    // Don't actually add this to the turn actions, as it depends on cards we don't have yet
                    // This is just to inform the bot that a move will be possible next turn
                  }
                }
              }
            }
          }
        }
      } else if (!this._turnState.hasCollectedCards) {
        // If we've already moved but haven't collected cards, collect cards
        // Strategy rule 6.iii: Always ensure to pick 2 travel cards if hand permits
        const handSize = state.player.hand ? state.player.hand.length : 0;
        if (handSize < 4) {
          const cardAction = await this.evaluateCardPicks(bot);
          if (cardAction) {
            this._turnState.hasCollectedCards = true;

            if (cardAction.params.type === 'travel') {
              this._turnState.justPickedCards = true;
              this._turnState.pickedCardType = 'travel';
              this._turnState.hasCollectedTravelCards = true;
            }

            turnActions.push(cardAction);
          }
        }
      }

      // Check if we need to verify move success after actions are executed
      const hasMovement = turnActions.some(action => action.action === 'movePlayer');
      if (hasMovement) {
        // Add a flag to check position after move to verify it was successful
        const originalPosition = state.player.position;

        // Add a function to the turn actions to verify move success
        // This will be called by the Bot after the move action is executed
        this._verifyMoveSuccess = (newPosition) => {
          if (newPosition !== originalPosition) {
            // Move was successful
            this._turnState.hasMoved = true;
            bot.logger.info(`Bot ${bot.name} move verification: Success! Position changed from ${originalPosition} to ${newPosition}`);
          } else {
            // Move failed - position didn't change
            this._turnState.hasMoved = false;
            bot.logger.warn(`Bot ${bot.name} move verification: Failed! Position still at ${originalPosition}`);

            // If we failed to move and haven't drawn cards, we should try to draw cards next turn
            if (!this._turnState.hasCollectedCards) {
              bot.logger.info(`Bot ${bot.name} will attempt to draw cards next since movement failed`);

              // Instead of just logging, let's actually try to pick up cards now
              const handSize = state.player.hand ? state.player.hand.length : 0;
              if (handSize < 4) {
                bot.logger.info(`Bot ${bot.name} attempting to pick cards after failed movement`);
                this._handleFailedMove(bot, turnActions);
              }
            }
          }
        };
      }

      // If we have a move action but no card pick action, add a card pick action when allowed
      const hasMoveAction = turnActions.some(action => action.action === 'movePlayer');
      const hasCardPickAction = turnActions.some(action => action.action === 'pickCards');
      const handSize = state.player.hand ? state.player.hand.length : 0;

      if (hasMoveAction && !hasCardPickAction && !this._turnState.hasCollectedCards && handSize < 4) {
        bot.logger.info(`Bot ${bot.name} ensuring card pickup after movement (rule 6.iii)`);
        const cardAction = await this.evaluateCardPicks(bot, 'travel');
        if (cardAction) {
          // Insert before the end action
          turnActions.splice(turnActions.length - 1, 0, cardAction);
          this._turnState.hasCollectedCards = true;
          bot.logger.info(`Added card picking action to enforce rule 6.iii`);
        }
      }

      // Always end the turn as the final action
      turnActions.push({ action: 'endTurn', params: {} });

      // Log the state at the end of planning
      bot.logger.info(`Bot ${bot.name} planning turn with ${turnActions.length} actions (moved=${this._turnState.hasMoved}, cards=${this._turnState.hasCollectedCards})`);

      // Track key actions to help debug
      const actionTypes = turnActions.map(a => a.action).join(', ');
      bot.logger.info(`RETURNING ${turnActions.length} ACTIONS: ${actionTypes}`);

      return turnActions;
    } catch (error) {
      bot.logger.error(`Error planning turn: ${error.message} ${error.stack}`);
      return [{ action: 'endTurn', params: {} }];
    }
  }

  /**
   * Handle failed move by attempting to pick cards
   * @param {Bot} bot - The bot
   * @param {Array} turnActions - The actions for this turn
   * @private
   */
  _handleFailedMove(bot, turnActions) {
    try {
      // Ensure turnActions is initialized
      if (!turnActions || !Array.isArray(turnActions)) {
        bot.logger.warn(`Bot ${bot.name} _handleFailedMove called with invalid turnActions, creating new array`);
        turnActions = [];
      }

      // Get a fresh state
      const state = bot.getPlayerState();
      if (!state || !state.player) return;

      const handSize = state.player.hand ? state.player.hand.length : 0;
      if (handSize >= 4) return; // Hand is full, can't pick cards

      // Find any existing pickCards actions and remove them
      const pickCardIndices = [];
      for (let i = 0; i < turnActions.length; i++) {
        if (turnActions[i].action === 'pickCards') {
          pickCardIndices.push(i);
        }
      }

      // Remove existing card pick actions in reverse order to avoid index shifting
      for (let i = pickCardIndices.length - 1; i >= 0; i--) {
        turnActions.splice(pickCardIndices[i], 1);
      }

      // Find the endTurn action
      const endTurnIndex = turnActions.findIndex(action => action.action === 'endTurn');

      // Evaluate card picks synchronously to avoid timing issues
      const faceUpCards = state.faceUpTravel || [];
      if (faceUpCards.length > 0) {
        // Sort by value to pick the highest-value cards
        const sortedCards = [...faceUpCards].sort((a, b) => (b.value || 0) - (a.value || 0));

        // Pick up to 2 cards based on hand space
        const maxPickCount = Math.min(4 - handSize, 2, sortedCards.length);
        const cardsToPick = sortedCards.slice(0, maxPickCount);
        const cardIds = cardsToPick.map(card => card.id);

        bot.logger.info(`Bot ${bot.name} picking ${cardIds.length} travel cards after failed move: ${cardIds.join(', ')}`);

        const cardAction = {
          action: 'pickCards',
          params: {
            type: 'travel',
            pickFromFaceUp: cardIds
          }
        };

        bot.logger.info(`Bot ${bot.name} created pickCards action with ${cardIds.length} travel cards`);

        // Insert the pickCards action before the endTurn action or at the end
        if (endTurnIndex !== -1) {
          turnActions.splice(endTurnIndex, 0, cardAction);
        } else {
          turnActions.push(cardAction);
        }

        // Update turn state flags
        this._turnState.hasCollectedCards = true;
        this._turnState.hasCollectedTravelCards = true;
        bot.logger.info(`Bot ${bot.name} added pickCards action to turnActions after failed move`);
      } else {
        // No face up cards, pick from deck
        const cardAction = {
          action: 'pickCards',
          params: {
            type: 'travel',
            pickFromTop: true
          }
        };

        // Insert before the end turn action
        if (endTurnIndex !== -1) {
          turnActions.splice(endTurnIndex, 0, cardAction);
        } else {
          // If no end turn action, just add it to the end
          turnActions.push(cardAction);
        }

        // Mark that we've collected cards
        this._turnState.hasCollectedCards = true;
        this._turnState.hasCollectedTravelCards = true;
        bot.logger.info(`Bot ${bot.name} picking travel cards from deck after failed move`);
      }
    } catch (error) {
      bot.logger.error(`Error handling failed move: ${error.message}`);
    }
  }

  /**
   * Handle global event effects
   * @param {Bot} bot - The bot
   * @param {Object} globalEvent - The active global event
   * @returns {Object|null} - An action to take in response to the global event, or null
   * @private
   */
  _handleGlobalEvent(bot, globalEvent) {
    if (!globalEvent) return null;

    const state = bot.getPlayerState();
    if (!state || !state.player) return null;

    bot.logger.info(`Bot ${bot.name} handling global event: ${JSON.stringify(globalEvent)}`);

    // Make sure we have a valid effect property
    if (!globalEvent.effect) {
      bot.logger.warn(`Bot ${bot.name} received global event without effect property: ${JSON.stringify(globalEvent)}`);
      return null;
    }

    // Different global events may require different responses
    switch (globalEvent.effect) {
      case 'gain_5_inner_no_cube_pickup':
        // Diwali Distraction: All gain +5 inner pts but no cube pickup
        // This is a passive effect and points are added automatically
        bot.logger.info(`Bot ${bot.name} adapting to Diwali Distraction - no cube pickup available`);

        // Mark in our resource plan to prioritize OM tokens instead of energy cubes
        if (this._resourcePlan) {
          // Set a flag to indicate we should prioritize OM tokens during Diwali Distraction
          this._resourcePlan.prioritizeOmTokens = true;
          bot.logger.info(`Bot ${bot.name} will prioritize OM token collection during Diwali Distraction`);

          // Find a suitable Jyotirlinga if we don't already have one targeted
          if (!this._resourcePlan.targetJyotirlinga) {
            const jyotirlingaIds = this._getJyotirlingas(state);
            if (jyotirlingaIds && jyotirlingaIds.length > 0) {
              // Find closest Jyotirlinga
              let closestJyotirlinga = null;
              let shortestDistance = Infinity;

              const currentPosition = state.player.position || state.player.currentNodeId;

              for (const jyotirlinga of jyotirlingaIds) {
                const distance = this._estimateTravelDistance(state, currentPosition, jyotirlinga.id);
                if (distance < shortestDistance) {
                  shortestDistance = distance;
                  closestJyotirlinga = jyotirlinga;
                }
              }

              if (closestJyotirlinga) {
                this._resourcePlan.targetJyotirlinga = closestJyotirlinga;
                bot.logger.info(`Bot ${bot.name} targeting Jyotirlinga ${closestJyotirlinga.id} during Diwali Distraction`);
              }
            }
          }
        }

        // No direct action needed - we'll adjust movement priorities
        break;

      case 'max_moves_2_and_cost_artha_north_east':
        // Drizzle of Delay: Max 2 moves; ending in North or East costs 1 Artha
        // The move limitation is handled by the server, but we need to consider the Artha cost
        bot.logger.info(`Bot ${bot.name} adapting to Drizzle of Delay - considering Artha cost for North/East`);

        // Check if we have Artha cubes - if not, we should avoid ending in North or East regions
        const hasArthaCube = this._hasArthaCube(state);

        if (!hasArthaCube) {
          bot.logger.info(`Bot ${bot.name} has no Artha cubes, will try to avoid ending in North or East regions`);
          // We'll use this flag in path planning to avoid North/East if possible
          this._turnState.avoidNorthEast = true;
        } else {
          bot.logger.info(`Bot ${bot.name} has Artha cubes, can end in North or East if needed`);
        }

        // No direct action needed - we'll adjust movement priorities
        break;

      case 'no_airport_travel':
        // Turbulent Skies: No airport travel this round
        // This is a passive effect that will be enforced by the game rules
        // We need to adjust our path planning to avoid airports
        bot.logger.info(`Bot ${bot.name} adapting to Turbulent Skies - avoiding airport travel`);

        // No direct action needed, but we'll mark in our state to avoid planning airport routes
        this._turnState.avoidAirports = true;

        // No action to return as this is a passive adaptation
        break;

      case 'double_trade_no_travel':
        // Election Campaigns: All trade yield 2x. No travel allowed this round
        bot.logger.info(`Bot ${bot.name} adapting to Election Campaigns - no travel, focusing on trading`);

        // Since we can't travel, prioritize trading for better resources
        if (state.player.energyCubes) {
          const cubeTypes = Object.keys(state.player.energyCubes).filter(
            type => state.player.energyCubes[type] > 0
          );

          if (cubeTypes.length > 0) {
            bot.logger.info(`Bot ${bot.name} trading energy cubes for 2x yield during Election Campaigns`);
            return {
              action: 'tradeEnergyCubes',
              params: {
                selectedCubes: [cubeTypes[0]]
              }
            };
          }
        }

        // If we can't trade, just end the turn since travel is not allowed
        bot.logger.info(`Bot ${bot.name} ending turn due to Election Campaigns (no travel allowed)`);
        return {
          action: 'endTurn',
          params: {
            reason: 'globalEvent'
          }
        };

      case 'discard':
        // Need to discard a card
        if (Array.isArray(state.player.hand) && state.player.hand.length > 0) {
          // Find the least valuable card to discard
          let leastValuableIndex = 0;
          let leastValuableScore = 999;

          for (let i = 0; i < state.player.hand.length; i++) {
            const card = state.player.hand[i];
            if (!card) continue;

            // Score the card based on its usefulness
            let cardScore = 999;

            if (card.type === 'travel') {
              cardScore = card.value || 0;
            } else if (card.type === 'event') {
              // Event cards are generally more valuable
              cardScore = 100 + (card.value || 0);
            }

            if (cardScore < leastValuableScore) {
              leastValuableIndex = i;
              leastValuableScore = cardScore;
            }
          }

          bot.logger.info(`Bot ${bot.name} discarding card at index ${leastValuableIndex} due to global event`);

          return {
            action: 'discardCard',
            params: {
              cardIndex: leastValuableIndex,
              reason: 'globalEvent'
            }
          };
        }
        break;

      case 'skipTurn':
        // Have to skip this turn
        bot.logger.info(`Bot ${bot.name} skipping turn due to global event`);
        return {
          action: 'endTurn',
          params: {
            reason: 'globalEvent'
          }
        };

      case 'loseResource':
        // Lose a specific resource
        if (globalEvent.resourceType === 'om' && state.player.omTokens > 0) {
          bot.logger.info(`Bot ${bot.name} losing OM token due to global event`);
          return {
            action: 'loseResource',
            params: {
              resourceType: 'om',
              amount: 1,
              reason: 'globalEvent'
            }
          };
        } else if (globalEvent.resourceType === 'energy' && state.player.energyCubes) {
          // Find a non-zero energy cube to lose
          const cubeTypes = Object.keys(state.player.energyCubes).filter(
            type => state.player.energyCubes[type] > 0
          );

          if (cubeTypes.length > 0) {
            const cubeToLose = cubeTypes[0];
            bot.logger.info(`Bot ${bot.name} losing ${cubeToLose} cube due to global event`);
            return {
              action: 'loseResource',
              params: {
                resourceType: 'energy',
                energyType: cubeToLose,
                amount: 1,
                reason: 'globalEvent'
              }
            };
          }
        }
        break;

      default:
        // Other global event types may have passive effects that don't require actions
        bot.logger.info(`Bot ${bot.name} no action needed for global event: ${globalEvent.name}`);
        break;
    }

    return null;
  }

  /**
   * Consider whether to use a vehicle this turn
   * @param {Bot} bot - The bot
   * @returns {Object|null} - A vehicle action, or null
   * @private
   */
  _considerVehicleUsage(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !state.player.vehicles || state.player.vehicles.length === 0) {
      return null;
    }

    // Check if we have an available vehicle that would be useful
    for (const vehicle of state.player.vehicles) {
      if (!vehicle.used && this._isVehicleUsefulNow(bot, vehicle)) {
        bot.logger.info(`Bot ${bot.name} using vehicle: ${vehicle.name}`);

        return {
          action: 'useVehicle',
          params: {
            vehicleId: vehicle.id
          }
        };
      }
    }

    return null;
  }

  /**
   * Determine if a vehicle would be useful right now
   * @param {Bot} bot - The bot
   * @param {Object} vehicle - The vehicle to evaluate
   * @returns {boolean} - True if the vehicle would be useful
   * @private
   */
  _isVehicleUsefulNow(bot, vehicle) {
    const state = bot.getPlayerState();
    if (!state || !state.player) return false;

    // Vehicles that give extra movement are always useful if we have a target journey card
    if (vehicle.type === 'movement' && this._currentTarget) {
      return true;
    }

    // Vehicles that provide resource bonuses are useful if we need resources
    if (vehicle.type === 'resource' && this._resourcePlan.needsEnergyCubes) {
      return true;
    }

    // Vehicles that provide OM token bonuses are useful if we need OM tokens
    if (vehicle.type === 'om' && this._resourcePlan.needsOmTokens) {
      return true;
    }

    return false;
  }

  /**
     * Select a journey card to target
     * @param {Bot} bot - The bot to select a journey card for
     * @private
     */
  async _selectTargetJourneyCard(bot) {
    const state = bot.getPlayerState();
    if (!state) {
      bot.logger.warn(`Bot ${bot.name} missing state for journey card selection`);
      return;
    }

    // Add detailed logging to debug what's missing
    if (!state.player) {
      bot.logger.warn(`Bot ${bot.name} missing player data in state for journey card selection`);
      return;
    }

    if (!state.faceUpJourneyInner) {
      bot.logger.warn(`Bot ${bot.name} missing faceUpJourneyInner in state for journey card selection`);
      // Initialize with empty array instead of returning
      state.faceUpJourneyInner = [];
    }

    if (!state.faceUpJourneyOuter) {
      bot.logger.warn(`Bot ${bot.name} missing faceUpJourneyOuter in state for journey card selection`);
      // Initialize with empty array instead of returning
      state.faceUpJourneyOuter = [];
    }

    // Log the journey cards for debugging
    bot.logger.info(`Bot ${bot.name} journey card selection - Inner: ${state.faceUpJourneyInner.length}, Outer: ${state.faceUpJourneyOuter.length}`);
    if (state.faceUpJourneyInner.length > 0 || state.faceUpJourneyOuter.length > 0) {
      bot.logger.info(`Inner journey cards: ${JSON.stringify(state.faceUpJourneyInner)}`);
      bot.logger.info(`Outer journey cards: ${JSON.stringify(state.faceUpJourneyOuter)}`);
    }

    // Get the current round number from state or estimate it based on turnNumber
    const currentRound = state.roundCount || Math.ceil(bot.turnsPlayed / 4);
    // Early game priority: under round 5 with no collected journey cards yet
    const hasCollectedJourneys = state.player.collectedJourneys && state.player.collectedJourneys.length > 0;
    const isEarlyGame = currentRound < 5 && !hasCollectedJourneys;

    bot.logger.info(`Early game check: round=${currentRound}, hasCollectedJourneys=${hasCollectedJourneys}, isEarlyGame=${isEarlyGame}`);

    // Combine inner and outer journey cards that are available
    const availableJourneyCards = [
      ...state.faceUpJourneyInner.map(card => ({ ...card, cardType: 'inner' })),
      ...state.faceUpJourneyOuter.map(card => ({ ...card, cardType: 'outer' }))
    ];

    if (availableJourneyCards.length === 0) {
      bot.logger.info(`Bot ${bot.name} couldn't find any journey cards to target`);
      return;
    }

    // Check if we already have a target and it's still valid
    if (this._currentTarget) {
      const targetStillExists = availableJourneyCards.some(
        card => card.id === this._currentTarget.id
      );

      if (targetStillExists) {
        // Keep the current target if it's still a valid choice
        return;
      }
    }

    // Find all journey cards with their details from board data
    const enrichedJourneyCards = availableJourneyCards.map(card => {
      const isInner = card.cardType === 'inner';
      const deck = isInner ? journeyDeckInner : journeyDeckOuter;
      const journeyData = deck.find(j => j.locationId === card.locationId);

      if (!journeyData) {
        return null;
      }

      // Find the required cubes for this journey
      const requiredCubes = journeyData.required || {};

      // Calculate total cube requirements to help with evaluation
      const totalRequiredCubes = Object.values(requiredCubes).reduce((sum, val) => sum + val, 0);

      return {
        ...card,
        requiredCubes,
        journeyData,
        totalRequiredCubes
      };
    }).filter(Boolean); // Remove any null entries

    // Prioritize journey cards by cube requirements and distance
    const playerPosition = state.player.position || state.player.currentNodeId;
    if (!playerPosition) {
      // If we have no position, just pick the first one
      this._currentTarget = enrichedJourneyCards[0];
      bot.logger.info(`Bot ${bot.name} selected journey target: ${this._currentTarget.id} at location ${this._currentTarget.locationId}`);
      return;
    }

    // Count player cubes by type
    const playerCubesByType = {};
    if (Array.isArray(state.player.energyCubes)) {
      for (const cube of state.player.energyCubes) {
        playerCubesByType[cube] = (playerCubesByType[cube] || 0) + 1;
      }
    } else if (typeof state.player.energyCubes === 'object') {
      Object.assign(playerCubesByType, state.player.energyCubes);
    }

    // Calculate total energy cubes the player has
    const totalEnergyCubes = this._countEnergyCubes(state.player);
    const isAtMaxEnergyCubes = totalEnergyCubes >= 5;

    // Log if player has reached max energy cube capacity
    if (isAtMaxEnergyCubes) {
      bot.logger.info(`Bot ${bot.name} has reached maximum energy cube capacity (${totalEnergyCubes}/5). Will only target journey cards that can be collected with existing cubes.`);
    }

    // Helper function to get the journey count for a specific type
    const getJourneyCount = (journeyType) => {
      const collectedJourneys = state.player.collectedJourneys || [];
      return journeyType === 'inner' ?
        collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length :
        collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;
    };

    const journeysWithScores = enrichedJourneyCards.map(journey => {
      // Calculate cube difference
      let cubeDifference = 0;
      let hasAllRequiredCubes = true;

      Object.entries(journey.requiredCubes).forEach(([type, amount]) => {
        const playerAmount = playerCubesByType[type] || 0;
        const missing = Math.max(0, amount - playerAmount);
        if (missing > 0) {
          hasAllRequiredCubes = false;
        }
        cubeDifference += missing;
      });

      // Calculate distance
      const distance = this._estimateTravelDistance(state, playerPosition, journey.locationId);

      console.log(`journey details: ${JSON.stringify(journey)}`);

      // Calculate reward value
      const rewardValue = journey.cardType === 'inner' ?
        (journey.journeyData.reward?.inner || 0) :
        (journey.journeyData.reward?.outer || 0);

      // Calculate OM token requirement and gap
      const journeyCount = getJourneyCount(journey.cardType);
      const requiredOmTokens = calculateOmCostForNextCard(journeyCount);
      const currentOmTemp = getOmTempCount(state.player);
      const omTokenGap = Math.max(0, requiredOmTokens - currentOmTemp);

      // In early game, prioritize:
      // 1. Cards we already have all cubes for
      // 2. Cards requiring fewer cubes
      // 3. Cards at closer locations
      // 4. Cards with higher rewards
      // 5. Cards requiring fewer OM tokens

      let score;

      // If at max energy cubes, heavily penalize cards that need cubes we don't have
      if (isAtMaxEnergyCubes && !hasAllRequiredCubes) {
        // Give a very high score (low priority) to cards that need cubes we don't have
        // when we're already at max cube capacity, since we can't collect more cubes
        score = 10000 + cubeDifference * 100 + distance;
      } else if (isEarlyGame) {
        // In early game, heavily prioritize cards we can collect immediately
        if (hasAllRequiredCubes && omTokenGap === 0 && distance === 0) {
          score = -1000; // Extremely high priority if we're at location with all resources
        } else if (hasAllRequiredCubes && omTokenGap === 0) {
          score = distance * 5; // High priority if we have all resources but need to travel
        } else {
          // Include OM token gap in the score calculation
          score = 100 + cubeDifference * 10 + distance * 5 + omTokenGap * 15 - rewardValue;
        }
      } else {
        // Normal scoring for later in the game - include OM token gap
        score = cubeDifference * 3 + distance * 2 + omTokenGap * 8 - rewardValue;
      }

      return {
        ...journey,
        cubeDifference,
        distance,
        hasAllRequiredCubes,
        requiredOmTokens,
        omTokenGap,
        score // Lower score is better
      };
    });

    // Sort by score (lower is better)
    journeysWithScores.sort((a, b) => a.score - b.score);

    // Select the best journey card
    this._currentTarget = journeysWithScores[0];

    // Detailed logging
    bot.logger.info(`Bot ${bot.name} selected journey target: ${this._currentTarget.id} at location ${this._currentTarget.locationId} (${this._currentTarget.cardType})`);
    bot.logger.info(`Journey selection metrics: distance=${this._currentTarget.distance}, cubeDifference=${this._currentTarget.cubeDifference}, hasAllCubes=${this._currentTarget.hasAllRequiredCubes}, requiredOmTokens=${this._currentTarget.requiredOmTokens}, omTokenGap=${this._currentTarget.omTokenGap}, score=${this._currentTarget.score}`);

    // Log all candidate journeys in early game for debugging
    if (isEarlyGame) {
      bot.logger.info(`EARLY GAME JOURNEY CANDIDATES (round ${currentRound}):`);
      journeysWithScores.forEach((j, idx) => {
        bot.logger.info(`  ${idx + 1}. ID: ${j.id}, Location: ${j.locationId}, Type: ${j.cardType}, Score: ${j.score}, Distance: ${j.distance}, CubeDiff: ${j.cubeDifference}, HasCubes: ${j.hasAllRequiredCubes}, OmReq: ${j.requiredOmTokens}, OmGap: ${j.omTokenGap}`);
      });
    }

    // Additional logging if at max energy cube capacity
    if (isAtMaxEnergyCubes) {
      if (this._currentTarget.hasAllRequiredCubes) {
        bot.logger.info(`Bot has max energy cubes (${totalEnergyCubes}/5) and selected a journey card that can be collected with existing cubes.`);
      } else {
        bot.logger.warn(`Bot has max energy cubes (${totalEnergyCubes}/5) but no collectable journey cards available. Selected best option but will need to discard cubes first.`);
      }
    }
  }


  /**
   * Get all Jyotirlinga locations from the game state and board data
   * @param {Object} state - The game state
   * @returns {Array} - Array of Jyotirlinga location IDs that have Om tokens available
   * @private
   */
  _getJyotirlingas(state) {
    if (!state) {
      throw new Error('Game state is missing in _getJyotirlingas');
    }

    // In the game's board data, Jyotirlingas are at locations 49-60
    const jyotirlingaIds = Array.from({length: 12}, (_, i) => i + 49);

    // Check if locationOm exists in state
    if (!state.locationOm) {
      throw new Error('locationOm is not defined in state - simulation must use real server data');
    }

    // Filter for locations that have Om tokens (locationOm[id] === true)
    // Return array of objects with id property instead of just IDs
    const jyotirlingasWithOm = jyotirlingaIds
      .filter(id => state.locationOm[id] === true)
      .map(id => ({ id }));

    if (jyotirlingasWithOm.length === 0) {
      // Don't throw here - there might actually be no OM tokens on jyotirlingas at this point in the game
      console.log('Warning: No Jyotirlinga locations have OM tokens available');
    }

    // Return array of location objects with id property
    return jyotirlingasWithOm;
  }

  /**
   * Estimates the travel distance between two nodes in the game graph.
   * Takes into account special rules like airport-to-airport travel.
   * @param {Object} state - The current game state
   * @param {number|string} startNodeId - The ID of the starting node
   * @param {number|string} endNodeId - The ID of the ending node
   * @returns {number} - The estimated distance in hops
   * @private
   */
  _estimateTravelDistance(state, startNodeId, endNodeId) {
    if (!state) {
      console.error('Game state is missing in _estimateTravelDistance');
      return Infinity; // Return Infinity instead of throwing
    }

    // Handle object inputs
    if (typeof startNodeId === 'object') {
      startNodeId = startNodeId.id || startNodeId.locationId;
      console.warn(`Converted start node object to ID: ${startNodeId}`);
    }

    if (typeof endNodeId === 'object') {
      endNodeId = endNodeId.id || endNodeId.locationId;
      console.warn(`Converted end node object to ID: ${endNodeId}`);
    }

    // Extra defensive checks for undefined/null values
    if (startNodeId === undefined || startNodeId === null) {
      console.error('Start node ID is undefined or null in _estimateTravelDistance');
      return Infinity;
    }

    if (endNodeId === undefined || endNodeId === null) {
      console.error('End node ID is undefined or null in _estimateTravelDistance');
      return Infinity;
    }

    // Convert to numbers to ensure consistent comparisons
    startNodeId = parseInt(startNodeId, 10);
    endNodeId = parseInt(endNodeId, 10);

    // Validate after conversion
    if (isNaN(startNodeId) || isNaN(endNodeId)) {
      const stack = new Error().stack;
      const callerInfo = stack.split('\n')[2] || 'unknown caller';
      console.error(`Invalid node IDs in _estimateTravelDistance: start=${startNodeId}, end=${endNodeId} (called from: ${callerInfo})`);
      return Infinity; // Return Infinity instead of throwing
    }

    // If start and end are the same, distance is 0
    if (startNodeId === endNodeId) {
      return 0;
    }

    // Check if both nodes are airports (special case)
    const isStartAirport = startNodeId >= 61 && startNodeId <= 66; // Airport IDs 61-66
    const isEndAirport = endNodeId >= 61 && endNodeId <= 66; // Airport IDs 61-66

    // Direct travel between airports counts as 1 hop
    if (isStartAirport && isEndAirport) {
      return 1;
    }

    // Use the PathCalculator's adjacencyMap for better performance
    // This is more efficient than manually checking state.edges every time

    try {
      // Check if pathCalculator is initialized
      if (!this.pathCalculator || !this.pathCalculator.adjacencyMap) {
        console.error('PathCalculator not initialized or missing adjacencyMap');
        return Infinity;
      }

      // Use BFS to find the shortest path length
      const visited = new Set([startNodeId]);
      const queue = [{ node: startNodeId, distance: 0 }];

      while (queue.length > 0) {
        const { node, distance } = queue.shift();

        // If we've reached the destination
        if (node === endNodeId) {
          return distance;
        }

        // Get all neighboring nodes using the adjacencyMap from PathCalculator
        const neighbors = this.pathCalculator.adjacencyMap.get(node) || new Set();

        for (const neighbor of neighbors) {
          if (!visited.has(neighbor)) {
            visited.add(neighbor);
            queue.push({ node: neighbor, distance: distance + 1 });
          }
        }
      }

      // If no path found, return a large number
      console.warn(`No path found from ${startNodeId} to ${endNodeId}`);
      return Infinity;
    } catch (error) {
      console.error(`Error estimating travel distance: ${error.message}`);
      return Infinity; // Return Infinity instead of throwing
    }
  }

  /**
   * Update the resource collection plan
   * @param {Bot} bot - The bot
   * @returns {Promise<void>}
   * @private
   */
  async _updateResourcePlan(bot) {
    const state = bot.getPlayerState();
    if (!state || !this._currentTarget) {
      this._resourcePlan = {
        needsOmTokens: false,
        needsEnergyCubes: false,
        targetJyotirlinga: null,
        targetEnergyLocation: null,
        targetEnergyDistance: null,
        fallbackEnergyLocation: null,
        fallbackEnergyDistance: null,
        targetVehicle: null,
        prioritizeOmTokens: this._resourcePlan?.prioritizeOmTokens || false // Preserve prioritization flag
      };
      return;
    }

    const target = this._currentTarget;
    const player = state.player;

    // Debug the locationCubes data structure from state
    if (!state.locationCubes) {
      bot.logger.error(`WARNING: state.locationCubes is not defined in state for bot ${bot.name}`);
      // always hard crash so that developer is alerted
      process.exit(1);
    }

    // Preserve prioritization flag if it was set
    const prioritizeOmTokens = this._resourcePlan?.prioritizeOmTokens || false;

    // First priority: Check if we need OM tokens based on journey slot requirements
    const isInnerJourney = target.cardType === 'inner';

    // Calculate how many Om tokens are required based on how many journey cards already collected
    const collectedJourneys = player.collectedJourneys || [];
    const innerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length;
    const outerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;

    bot.logger.info(`Collected journeys: ${JSON.stringify(collectedJourneys)}`);
    bot.logger.info(`Journey counts: inner=${innerJourneyCount}, outer=${outerJourneyCount}`);

    // Calculate the required OM tokens using the helper function
    const requiredOmTokens = calculateOmCostForNextCard(isInnerJourney ? innerJourneyCount : outerJourneyCount, state.numPlayers);

    // Use the top-level getOmTempCount function for consistency
    // This avoids defining a local version that might behave differently

    // Current OM tokens in temporary storage
    const currentOmTemp = getOmTempCount(player);

    bot.logger.info(`Bot ${bot.name} needs ${requiredOmTokens} Om tokens for the next ${isInnerJourney ? 'inner' : 'outer'} journey card. Currently has ${currentOmTemp} in omTemp.`);

    // Check if we need OM tokens - respect prioritization flag
    const needsOmTokens = prioritizeOmTokens || (currentOmTemp < requiredOmTokens);

    // If we're prioritizing Om tokens, log it
    if (prioritizeOmTokens && currentOmTemp >= requiredOmTokens) {
      bot.logger.info(`Bot ${bot.name} has enough Om tokens for the next journey card but is prioritizing Om token collection due to competitive situation`);
    }

    // Second priority: Calculate energy cube requirements
    const requiredCubes = target.requiredCubes || {};

    // Count player cubes by type
    const playerCubesByType = { bhakti: 0, gnana: 0, karma: 0, artha: 0 };

    // Handle both array and object formats for energy cubes
    if (Array.isArray(player.energyCubes)) {
      // If energyCubes is an array of strings (e.g., ['karma', 'bhakti'])
      player.energyCubes.forEach(cube => {
        if (playerCubesByType[cube] !== undefined) {
          playerCubesByType[cube]++;
        }
      });
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      // If energyCubes is an object (e.g., {bhakti: 1, karma: 1})
      Object.assign(playerCubesByType, player.energyCubes);
    }

    // Log the actual cube counts for debugging
    bot.logger.info(`Bot ${bot.name} actual energy cubes: Bhakti=${playerCubesByType.bhakti}, Gnana=${playerCubesByType.gnana}, Karma=${playerCubesByType.karma}, Artha=${playerCubesByType.artha}`);

    // Calculate missing energy cubes
    const missingCubes = {};
    let totalMissingCubes = 0;

    Object.entries(requiredCubes).forEach(([type, amount]) => {
      const playerAmount = playerCubesByType[type] || 0;
      const missing = Math.max(0, amount - playerAmount);

      if (missing > 0) {
        missingCubes[type] = missing;
        totalMissingCubes += missing;
      }
    });

    // Update resource plan
    this._resourcePlan.needsOmTokens = needsOmTokens;
    this._resourcePlan.needsEnergyCubes = totalMissingCubes > 0;
    this._resourcePlan.missingCubes = missingCubes;
    this._resourcePlan.totalMissingCubes = totalMissingCubes;
    this._resourcePlan.prioritizeOmTokens = prioritizeOmTokens; // Preserve prioritization flag

    bot.logger.info(`Resource needs: OM tokens=${this._resourcePlan.needsOmTokens}, Energy cubes=${this._resourcePlan.needsEnergyCubes} (total missing: ${totalMissingCubes}), Prioritize OM=${this._resourcePlan.prioritizeOmTokens}`);

    // Find closest Jyotirlinga if we need OM tokens (first priority)
    if (this._resourcePlan.needsOmTokens) {
      try {
        // Check if locationOm exists
        if (!state.locationOm) {
          throw new Error('locationOm is not defined in state - simulation must use real server data');
        }

        // Get jyotirlingas with OM tokens
        const jyotirlingaIds = this._getJyotirlingas(state);
        const currentPosition = player.position || player.currentNodeId;

        // Log how many Jyotirlingas we found
        bot.logger.info(`Found ${jyotirlingaIds.length} Jyotirlingas with available OM tokens: ${jyotirlingaIds.join(', ')}`);

        if (!currentPosition) {
          throw new Error(`Bot ${bot.name} has undefined position, cannot find closest Jyotirlinga`);
        }

        if (jyotirlingaIds.length === 0) {
          // No Jyotirlingas with OM tokens available right now
          bot.logger.warn('No Jyotirlingas have OM tokens available right now - will need to wait for replenishment');
          // Don't throw an error since this is a valid game state
          // Only unset needsOmTokens if we're not prioritizing
          if (!this._resourcePlan.prioritizeOmTokens) {
            this._resourcePlan.needsOmTokens = false;
          }
        } else {
          let closestJyotirlinga = null;
          let shortestDistance = Infinity;

          for (const jId of jyotirlingaIds) {
            const distance = this._estimateTravelDistance(state, currentPosition, jId.id);
            bot.logger.debug(`Distance from ${currentPosition} to jyotirlinga ${jId.id}: ${distance} hops`);
            if (distance < shortestDistance) {
              shortestDistance = distance;
              closestJyotirlinga = jId;
            }
          }

          if (closestJyotirlinga) {
            this._resourcePlan.targetJyotirlinga = closestJyotirlinga;
            if (this._resourcePlan.prioritizeOmTokens) {
              bot.logger.info(`PRIORITIZING OM TOKENS: Bot ${bot.name} targeting Jyotirlinga at ${closestJyotirlinga.id}`);
            } else {
              bot.logger.info(`Bot ${bot.name} needs ${requiredOmTokens - currentOmTemp} more Om tokens, targeting Jyotirlinga at ${closestJyotirlinga.id}`);
            }
          } else {
            // This shouldn't happen if we have jyotirlingaIds, but just in case
            bot.logger.error('Could not find closest Jyotirlinga with OM token');
            process.exit(1);
          }
        }
      } catch (error) {
        bot.logger.error(`Error finding Jyotirlingas: ${error.message}`);
        // Set needsOmTokens to false to prevent further issues, unless prioritizing
        if (!this._resourcePlan.prioritizeOmTokens) {
          this._resourcePlan.needsOmTokens = false;
        }
      }
    }

    // If we don't need OM tokens (or couldn't get any), find energy cube locations
    if (!this._resourcePlan.needsOmTokens && this._resourcePlan.needsEnergyCubes) {
      const energyLocations = this._findBestEnergyLocation(state, state.player, bot, missingCubes);
      if (energyLocations) {
        const primaryLocation = energyLocations.primary;
        this._resourcePlan.targetEnergyLocation = primaryLocation;
        this._resourcePlan.targetEnergyDistance = primaryLocation.distance;

        // Store fallback location if available
        if (energyLocations.fallback) {
          this._resourcePlan.fallbackEnergyLocation = energyLocations.fallback;
          this._resourcePlan.fallbackEnergyDistance = energyLocations.fallback.distance;
        }

        // Get the actual cubes at this location from the state
        const locationCubes = state.locationCubes[primaryLocation.id];
        const cubesStr = Array.isArray(locationCubes) ? locationCubes.join(', ') : locationCubes;

        bot.logger.info(`Bot ${bot.name} targeting energy location at ${primaryLocation.id} with ${primaryLocation.color} cube (distance: ${primaryLocation.distance} hops, available: ${cubesStr})`);
        
        // Log fallback location if available
        if (energyLocations.fallback) {
          const fallbackCubes = state.locationCubes[energyLocations.fallback.id];
          const fallbackCubesStr = Array.isArray(fallbackCubes) ? fallbackCubes.join(', ') : fallbackCubes;
          bot.logger.info(`Bot ${bot.name} has fallback energy location at ${energyLocations.fallback.id} with ${energyLocations.fallback.color} cube (distance: ${energyLocations.fallback.distance} hops, available: ${fallbackCubesStr})`);
        }
      } else {
        bot.logger.warn(`No suitable energy locations found for missing cubes: ${Object.keys(missingCubes).join(', ')}`);
      }
    }
  }

  /**
   * Find the best energy location based on missing cubes
   * @param {Object} state - The game state
   * @param {Object} player - The player
   * @param {Object} bot - The bot
   * @param {Object} missingCubes - Object mapping cube colors to quantities needed
   * @returns {Object|null} - Object containing the best and fallback energy locations, or null if none found
   * @private
   */
  _findBestEnergyLocation(state, player, bot, missingCubes) {
    if (!state || !player) {
      throw new Error('Invalid state or player in _findBestEnergyLocation');
    }

    if (!missingCubes || typeof missingCubes !== 'object' || Object.keys(missingCubes).length === 0) {
      bot.logger.info('No missing cubes to search for');
      return null;
    }

    const currentPosition = player.position || player.currentNodeId;
    if (!currentPosition) {
      throw new Error(`Bot ${bot.name} has undefined position, cannot find energy location`);
    }

    try {
      // In the game, energy cubes are at locations 1-48
      const energyLocationIds = Array.from({length: 48}, (_, i) => i + 1);

      // Check if locationCubes exists in state
      if (!state.locationCubes) {
        throw new Error('locationCubes is not defined in state - simulation must use real server data');
      }

      // Find energy locations with cubes we need
      const validEnergyLocations = [];
      const neededColors = Object.keys(missingCubes);

      bot.logger.info(`Looking for energy locations with: ${neededColors.join(', ')}`);

      for (const locationId of energyLocationIds) {
        // Skip if this location doesn't have cubes
        if (!state.locationCubes[locationId]) continue;

        // Check if this location has one of our missing cube colors
        const locationCubes = state.locationCubes[locationId];

        for (const color of neededColors) {
          if (locationCubes.includes(color)) {
            // This location has a cube we need
            validEnergyLocations.push({
              id: locationId,
              distance: this._estimateTravelDistance(state, currentPosition, locationId),
              color
            });
            // Only count a location once even if it has multiple colors we need
            break;
          }
        }
      }

      // Sort by distance (ascending)
      validEnergyLocations.sort((a, b) => a.distance - b.distance);

      if (validEnergyLocations.length > 0) {
        const bestLocation = validEnergyLocations[0];
        bot.logger.info(`Found energy location ${bestLocation.id} with ${bestLocation.color} cube at distance ${bestLocation.distance}`);
        
        // If we have multiple locations, set the second one as fallback
        let fallbackLocation = null;
        if (validEnergyLocations.length > 1) {
          fallbackLocation = validEnergyLocations[1];
          bot.logger.info(`Found fallback energy location ${fallbackLocation.id} with ${fallbackLocation.color} cube at distance ${fallbackLocation.distance}`);
        }
        
        return {
          primary: bestLocation,
          fallback: fallbackLocation
        };
      } else {
        bot.logger.warn(`No energy locations found with needed cubes: ${neededColors.join(', ')}`);
        return null;
      }
    } catch (error) {
      bot.logger.error(`Error finding energy location: ${error.message}`);
      return null;
    }
  }

  /**
   * Plan a strategic move based on current goals
   * @param {Bot} bot - The bot to plan movement for
   * @param {Object} [overrideState] - Optional state override for card picking planning
   * @returns {Object|null} A movement action, or null if no valid move can be planned
   * @private
   */
  async _planStrategicMove(bot, overrideState) {
    const state = overrideState || bot.getPlayerState();
    if (!state || !state.player) {
      throw new Error('Invalid player state in _planStrategicMove');
    }

    // Get current position - ALWAYS use position first, then fall back to currentNodeId
    // This ensures we use the most up-to-date position after any recent moves
    const currentNodeId = state.player.position || state.player.currentNodeId;
    if (!currentNodeId) {
      throw new Error("Player has no position");
    }

    // Log the current position for debugging
    bot.logger.info(`Planning strategic move from current position: ${currentNodeId}`);

    // Get travel cards - this will filter out any cards that are not actually in the player's hand
    const travelCards = this._getTravelCards(bot, overrideState);
    if (!travelCards || travelCards.length === 0) {
      bot.logger.info(`No travel cards available for movement from position ${currentNodeId}`);
      return null;
    }

    // Log available travel cards for debugging
    bot.logger.info(`Available travel cards for movement: ${travelCards.map(c => `${c.id}(${c.value})`).join(', ')}`);

    // Check if we have a vehicle that can be used for movement
    let vehicleId = null;
    if (state.player.vehicles && Array.isArray(state.player.vehicles)) {
      const movementVehicle = state.player.vehicles.find(v => v.type === 'movement' && !v.used);
      if (movementVehicle) {
        vehicleId = movementVehicle.id;
        bot.logger.info(`Using vehicle ${movementVehicle.name} for movement`);
      }
    }

    // Determine primary and fallback destinations based on priorities
    let primaryTargetNodeId = null;
    let primaryTargetType = null;
    let fallbackTargetNodeId = null;
    let fallbackTargetType = null;
    let chosenPath = null;

    // Calculate resource needs for our current journey target
    const resourceNeeds = await this._calculateResourceNeed(bot);

    // Check for active global events that affect our strategy
    const globalEvent = this._getCurrentGlobalEvent(bot);
    const isDiwaliDistraction = globalEvent && globalEvent.effect === 'gain_5_inner_no_cube_pickup';

    // Check if we should prioritize OM tokens (due to Diwali Distraction)
    const shouldPrioritizeOmTokens = isDiwaliDistraction || (this._resourcePlan && this._resourcePlan.prioritizeOmTokens);

    if (shouldPrioritizeOmTokens) {
      bot.logger.info(`Prioritizing OM token collection during Diwali Distraction`);
    }

    // Priority 1: During Diwali Distraction, prioritize OM tokens (even if we don't strictly "need" them)
    // OR normal priority: Go to nearest Jyotirlinga if we need OM tokens
    if ((shouldPrioritizeOmTokens && this._resourcePlan.targetJyotirlinga) ||
        (resourceNeeds.needsOmTokens && this._resourcePlan.targetJyotirlinga)) {
      bot.logger.info(`Priority 1: ${shouldPrioritizeOmTokens ? 'Diwali Distraction active - ' : ''}Targeting Jyotirlinga: ${this._resourcePlan.targetJyotirlinga}`);

      // Get the Jyotirlinga ID - this could be an object or a direct ID
      const jyotirlingaId = typeof this._resourcePlan.targetJyotirlinga === 'object'
        ? this._resourcePlan.targetJyotirlinga.id
        : this._resourcePlan.targetJyotirlinga;

      primaryTargetNodeId = jyotirlingaId;
      primaryTargetType = 'jyotirlinga';

      // Try to find path using optimal cards
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);
      bot.logger.info(`Looking for path to Jyotirlinga ${primaryTargetNodeId} (exact distance: ${exactDistanceNeeded})`);

      try {
        const pathToJyotirlinga = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);
        if (pathToJyotirlinga) {
          bot.logger.info(`Found path to Jyotirlinga using ${pathToJyotirlinga.cards.length} cards`);
          chosenPath = pathToJyotirlinga;
        } else {
          bot.logger.info(`No valid path found to Jyotirlinga ${primaryTargetNodeId} with current hand cards. Checking if face-up travel cards could help...`);
          
          // Check if any face-up travel cards would help - this returns an action object, not just card indices
          const helpfulCardsAction = await this._checkForHelpfulTravelCardsForTarget(bot, primaryTargetNodeId, exactDistanceNeeded);
          
          if (helpfulCardsAction) {
            bot.logger.info(`Found face-up cards that would help reach Jyotirlinga ${primaryTargetNodeId}`);
            
            // Return the action directly since _checkForHelpfulTravelCardsForTarget already returns a properly formatted action
            return helpfulCardsAction;
          } else {
            bot.logger.info(`No helpful face-up travel cards for reaching Jyotirlinga ${primaryTargetNodeId}`);
          }
        }
      } catch (error) {
        bot.logger.error(`Error calculating path to Jyotirlinga: ${error.message}`);
      }
    }

    // Priority 2: Go to nearest location with needed energy cube
    // Skip if Diwali Distraction is active (no cube pickup allowed)
    if (!chosenPath && resourceNeeds.needsEnergyCubes && !isDiwaliDistraction) {
      bot.logger.info(`Priority 2: Need energy cubes: ${Object.keys(resourceNeeds.missingCubes).join(', ')}`);

      // Get the target energy location from resource plan if available
      if (this._resourcePlan.targetEnergyLocation) {
        bot.logger.info(`Have target energy location: ${typeof this._resourcePlan.targetEnergyLocation === 'object'
          ? this._resourcePlan.targetEnergyLocation.id
          : this._resourcePlan.targetEnergyLocation}`);

        // Get the energy location ID - this could be an object or a direct ID
        const energyLocationId = typeof this._resourcePlan.targetEnergyLocation === 'object'
          ? this._resourcePlan.targetEnergyLocation.id
          : this._resourcePlan.targetEnergyLocation;

        primaryTargetNodeId = energyLocationId;
        primaryTargetType = 'energy';

        // Try to find path using optimal cards
        const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);
        bot.logger.info(`Looking for path to energy location ${primaryTargetNodeId} (exact distance: ${exactDistanceNeeded})`);

        try {
          const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);
          if (pathToEnergy) {
            bot.logger.info(`Found path to energy location using ${pathToEnergy.cards.length} cards`);
            chosenPath = pathToEnergy;
          } else {
            bot.logger.info(`No valid path found to primary energy location ${primaryTargetNodeId}`);
            
            // Try the fallback location if available
            if (this._resourcePlan.fallbackEnergyLocation) {
              const fallbackLocationId = typeof this._resourcePlan.fallbackEnergyLocation === 'object'
                ? this._resourcePlan.fallbackEnergyLocation.id
                : this._resourcePlan.fallbackEnergyLocation;
              
              bot.logger.info(`Trying fallback energy location ${fallbackLocationId}`);
              
              fallbackTargetNodeId = fallbackLocationId;
              fallbackTargetType = 'energy';
              
              try {
                const pathToFallbackEnergy = this._calculatePathToTarget(state, currentNodeId, fallbackLocationId, travelCards);
                if (pathToFallbackEnergy) {
                  bot.logger.info(`Found path to fallback energy location using ${pathToFallbackEnergy.cards.length} cards`);
                  chosenPath = pathToFallbackEnergy;
                  // Update the target to the fallback since we're going there
                  primaryTargetNodeId = fallbackLocationId;
                  primaryTargetType = 'energy';
                } else {
                  bot.logger.info(`No valid path found to fallback energy location ${fallbackLocationId} either`);
                }
              } catch (error) {
                bot.logger.error(`Error calculating path to fallback energy location: ${error.message}`);
              }
            }
          }
        } catch (error) {
          bot.logger.error(`Error calculating path to energy location: ${error.message}`);
        }
      } else {
        // Find the best energy location first
        bot.logger.info(`Looking for best energy location with needed cubes`);
        const neededColors = Object.keys(resourceNeeds.missingCubes);
        const energyLocations = this._findBestEnergyLocation(state, state.player, bot, resourceNeeds.missingCubes);

        if (energyLocations) {
          const primaryLocation = energyLocations.primary;
          primaryTargetNodeId = primaryLocation.id;
          primaryTargetType = 'energy';

          // Store this in the resource plan for future reference
          this._resourcePlan.targetEnergyLocation = primaryLocation;
          
          // Also store the fallback if available
          if (energyLocations.fallback) {
            this._resourcePlan.fallbackEnergyLocation = energyLocations.fallback;
            fallbackTargetNodeId = energyLocations.fallback.id;
            fallbackTargetType = 'energy';
          }

          // Try to find path using optimal cards
          const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);
          bot.logger.info(`Looking for path to energy location ${primaryTargetNodeId} (exact distance: ${exactDistanceNeeded})`);

          try {
            const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);
            if (pathToEnergy) {
              bot.logger.info(`Found path to energy location using ${pathToEnergy.cards.length} cards`);
              chosenPath = pathToEnergy;
            } else {
              bot.logger.info(`No valid path found to primary energy location ${primaryTargetNodeId}`);
              
              // Try the fallback if available
              if (energyLocations.fallback) {
                bot.logger.info(`Trying fallback energy location ${energyLocations.fallback.id}`);
                
                try {
                  const pathToFallbackEnergy = this._calculatePathToTarget(state, currentNodeId, energyLocations.fallback.id, travelCards);
                  if (pathToFallbackEnergy) {
                    bot.logger.info(`Found path to fallback energy location using ${pathToFallbackEnergy.cards.length} cards`);
                    chosenPath = pathToFallbackEnergy;
                    // Update the target to the fallback since we're going there
                    primaryTargetNodeId = energyLocations.fallback.id;
                    primaryTargetType = 'energy';
                  } else {
                    bot.logger.info(`No valid path found to fallback energy location ${energyLocations.fallback.id} either`);
                  }
                } catch (error) {
                  bot.logger.error(`Error calculating path to fallback energy location: ${error.message}`);
                }
              }
            }
          } catch (error) {
            bot.logger.error(`Error calculating path to energy location: ${error.message}`);
          }
        } else {
          bot.logger.warn(`No suitable energy location found with needed cubes: ${neededColors.join(', ')}`);
        }
      }
    } else if (!chosenPath && resourceNeeds.needsEnergyCubes && isDiwaliDistraction) {
      // During Diwali Distraction, try to get OM tokens instead of energy cubes
      bot.logger.info(`Skipping energy cube location targeting due to Diwali Distraction - will try to target OM tokens instead`);

      // If we don't already have a Jyotirlinga target during Diwali Distraction, try to find one
      if (!this._resourcePlan.targetJyotirlinga) {
        const jyotirlingaIds = this._getJyotirlingas(state);
        if (jyotirlingaIds && jyotirlingaIds.length > 0) {
          // Find closest Jyotirlinga
          let closestJyotirlinga = null;
          let shortestDistance = Infinity;

          for (const jyotirlinga of jyotirlingaIds) {
            const distance = this._estimateTravelDistance(state, currentNodeId, jyotirlinga.id);
            if (distance < shortestDistance) {
              shortestDistance = distance;
              closestJyotirlinga = jyotirlinga;
            }
          }

          if (closestJyotirlinga) {
            // Try to move to this Jyotirlinga
            bot.logger.info(`During Diwali Distraction, targeting Jyotirlinga ${closestJyotirlinga.id} instead of energy cubes`);

            const jyotirlingaId = closestJyotirlinga.id;
            primaryTargetNodeId = jyotirlingaId;
            primaryTargetType = 'jyotirlinga';

            // Store for future reference
            this._resourcePlan.targetJyotirlinga = closestJyotirlinga;

            // Try to find path
            const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, jyotirlingaId);
            try {
              const pathToJyotirlinga = this._calculatePathToTarget(state, currentNodeId, jyotirlingaId, travelCards);
              if (pathToJyotirlinga) {
                bot.logger.info(`Found path to Jyotirlinga using ${pathToJyotirlinga.cards.length} cards during Diwali Distraction`);
                chosenPath = pathToJyotirlinga;
              }
            } catch (error) {
              bot.logger.error(`Error calculating path to Jyotirlinga during Diwali Distraction: ${error.message}`);
            }
          }
        }
      }
    }

    // Priority 3: Go to nearest location with needed energy cube
    // Skip if Diwali Distraction is active (no cube pickup allowed)
    if (!chosenPath && resourceNeeds.needsEnergyCubes && !isDiwaliDistraction) {
      bot.logger.info(`Priority 3: Need energy cubes: ${Object.keys(resourceNeeds.missingCubes).join(', ')}`);

      // Get the target energy location from resource plan if available
      if (this._resourcePlan.targetEnergyLocation) {
        bot.logger.info(`Have target energy location: ${typeof this._resourcePlan.targetEnergyLocation === 'object'
          ? this._resourcePlan.targetEnergyLocation.id
          : this._resourcePlan.targetEnergyLocation}`);

        // Get the energy location ID - this could be an object or a direct ID
        const energyLocationId = typeof this._resourcePlan.targetEnergyLocation === 'object'
          ? this._resourcePlan.targetEnergyLocation.id
          : this._resourcePlan.targetEnergyLocation;

        primaryTargetNodeId = energyLocationId;
        primaryTargetType = 'energy';

        // Try to find path using optimal cards
        const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);
        bot.logger.info(`Looking for path to energy location ${primaryTargetNodeId} (exact distance: ${exactDistanceNeeded})`);

        try {
          const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);
          if (pathToEnergy) {
            bot.logger.info(`Found path to energy location using ${pathToEnergy.cards.length} cards`);
            chosenPath = pathToEnergy;
          } else {
            bot.logger.info(`No valid path found to energy location ${primaryTargetNodeId}`);
          }
        } catch (error) {
          bot.logger.error(`Error calculating path to energy location: ${error.message}`);
        }
      } else {
        // Find the best energy location first
        bot.logger.info(`Looking for best energy location with needed cubes`);
        const neededColors = Object.keys(resourceNeeds.missingCubes);
        const energyLocations = this._findBestEnergyLocation(state, state.player, bot, resourceNeeds.missingCubes);

        if (energyLocations) {
          const primaryLocation = energyLocations.primary;
          primaryTargetNodeId = primaryLocation.id;
          primaryTargetType = 'energy';

          // Store this in the resource plan for future reference
          this._resourcePlan.targetEnergyLocation = primaryLocation;
          
          // Also store the fallback if available
          if (energyLocations.fallback) {
            this._resourcePlan.fallbackEnergyLocation = energyLocations.fallback;
            fallbackTargetNodeId = energyLocations.fallback.id;
            fallbackTargetType = 'energy';
          }

          // Try to find path using optimal cards
          const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);
          bot.logger.info(`Looking for path to energy location ${primaryTargetNodeId} (exact distance: ${exactDistanceNeeded})`);

          try {
            const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);
            if (pathToEnergy) {
              bot.logger.info(`Found path to energy location using ${pathToEnergy.cards.length} cards`);
              chosenPath = pathToEnergy;
            } else {
              bot.logger.info(`No valid path found to primary energy location ${primaryTargetNodeId}`);
              
              // Try the fallback if available
              if (energyLocations.fallback) {
                bot.logger.info(`Trying fallback energy location ${energyLocations.fallback.id}`);
                
                try {
                  const pathToFallbackEnergy = this._calculatePathToTarget(state, currentNodeId, energyLocations.fallback.id, travelCards);
                  if (pathToFallbackEnergy) {
                    bot.logger.info(`Found path to fallback energy location using ${pathToFallbackEnergy.cards.length} cards`);
                    chosenPath = pathToFallbackEnergy;
                    // Update the target to the fallback since we're going there
                    primaryTargetNodeId = energyLocations.fallback.id;
                    primaryTargetType = 'energy';
                  } else {
                    bot.logger.info(`No valid path found to fallback energy location ${energyLocations.fallback.id} either`);
                  }
                } catch (error) {
                  bot.logger.error(`Error calculating path to fallback energy location: ${error.message}`);
                }
              }
            }
          } catch (error) {
            bot.logger.error(`Error calculating path to energy location: ${error.message}`);
          }
        } else {
          bot.logger.warn(`No suitable energy location found with needed cubes: ${neededColors.join(', ')}`);
        }
      }
    } else if (!chosenPath && resourceNeeds.needsEnergyCubes && isDiwaliDistraction) {
      bot.logger.info(`Skipping energy cube location targeting due to Diwali Distraction global event (no cube pickup allowed)`);
    }

    // Priority 4: Go to journey card location only if we have all required resources
    // Double check with hasResourcesForTargetJourney to ensure we have resources
    // WITHOUT checking if we're at the location
    const hasJourneyResources = this._hasResourcesForTargetJourney(bot);
    
    // Add detailed logging to debug the resource state
    if (this._currentTarget) {
      const player = state.player;
      const isInnerJourney = this._currentTarget.cardType === 'inner';
      const collectedJourneys = player.collectedJourneys || [];
      const innerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length;
      const outerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;
      const requiredOmTokens = calculateOmCostForNextCard(isInnerJourney ? innerJourneyCount : outerJourneyCount, state.numPlayers);
      const currentOmTemp = getOmTempCount(player);
      
      bot.logger.info(`Journey target resource check: Om tokens required=${requiredOmTokens}, have=${currentOmTemp}, resourceNeeds.needsOmTokens=${resourceNeeds.needsOmTokens}, hasResources=${hasJourneyResources}`);
    }
    
    if (this._currentTarget && !resourceNeeds.needsOmTokens && !resourceNeeds.needsEnergyCubes && !this._turnState.hasCollectedTravelCards) {
      if (hasJourneyResources) {
        primaryTargetNodeId = this._currentTarget.locationId;
        primaryTargetType = 'journey';
        bot.logger.info(`Primary target: Journey card location ${this._currentTarget.locationId} (have all required resources)`);
      } else {
        bot.logger.warn(`Resource needs calculation says we have all resources but resource check disagrees - likely Om token calculation issue`);
        // Instead of trying to reassign resourceNeeds (which is a const), 
        // we'll force the bot to not target the journey card location
        bot.logger.info(`Skipping journey target due to resource mismatch`);
        // Do not set primaryTargetNodeId for journey collection
      }

      // Check if we can travel exactly to the journey location
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, this._currentTarget.locationId);

      try {
        // Check if we're already at the journey location
        if (currentNodeId === this._currentTarget.locationId) {
          bot.logger.info(`Already at journey card location ${this._currentTarget.locationId} - no movement needed`);
          
          const journeyAction = {
            action: 'collectJourney',
            params: {
              journeyCardId: this._currentTarget.id,
              journeyType: this._currentTarget.cardType
            }
          };

          bot.logger.info(`Collecting journey card since already at location ${this._currentTarget.id} of type ${this._currentTarget.cardType}`);
          this._turnState.hasCollectedCards = true;
          return journeyAction;
        }

        const pathToJourney = this._calculatePathToTarget(state, currentNodeId, this._currentTarget.locationId, travelCards);

        // If we can travel exactly to the journey location with current cards,
        // let's do that immediately without considering other targets
        if (pathToJourney && pathToJourney.cardValue === exactDistanceNeeded) {
          chosenPath = pathToJourney;
          bot.logger.info(`Found exact path to journey card location ${this._currentTarget.locationId} - prioritizing journey collection`);
          this._turnState.moveToCollectJourney = true;

          // Create the move action with proper parameters and return it immediately
          const moveAction = {
            action: 'movePlayer',
            params: {
              path: chosenPath.path,
              cardIds: chosenPath.cards.map(c => c.id),
              extraHopCount: 0,
              isTriathlon: false
            }
          };

          return moveAction;
        }
      } catch (pathError) {
        bot.logger.error(`Error calculating path to journey target: ${pathError.message}`);
      }
    }

    // Priority 5: Go to nearest Jyotirlinga if we need OM tokens
    if (resourceNeeds.needsOmTokens && !this._resourcePlan.targetJyotirlinga) {
      // Get array of jyotirlinga IDs with OM tokens
      const jyotirlingaIds = this._getJyotirlingas(state);
      if (jyotirlingaIds.length > 0) {
        // Find closest Jyotirlinga
        let closestDist = Infinity;
        let closestId = null;

        for (const jId of jyotirlingaIds) {
          const dist = this._estimateTravelDistance(state, currentNodeId, jId.id);
          if (dist < closestDist) {
            closestDist = dist;
            closestId = jId;
          }
        }

        if (closestId) {
          primaryTargetNodeId = closestId.id;
          primaryTargetType = 'jyotirlinga';
          bot.logger.info(`Primary target: OM token at jyotirlinga ${closestId.id}`);
        }
      }
    }

    // Priority 6: Go to nearest location with needed energy cube
    if (resourceNeeds.needsEnergyCubes && !this._resourcePlan.targetEnergyLocation) {
      // Find all viable energy locations (not just the closest one)
      const allEnergyLocations = this._findAllEnergyLocations(state, state.player, bot, resourceNeeds.missingCubes);

      if (allEnergyLocations && allEnergyLocations.length > 0) {
        // Best location is first by default (sorted by distance)
        const bestEnergyLocation = allEnergyLocations[0];

        if (primaryTargetNodeId === null) {
          // If we don't have a primary target yet, use the closest energy location
          primaryTargetNodeId = bestEnergyLocation.id;
          primaryTargetType = 'energy';
          bot.logger.info(`Primary target: Energy cube at location ${bestEnergyLocation.id}`);
        } else {
          // Otherwise, use it as a fallback
          fallbackTargetNodeId = bestEnergyLocation.id;
          fallbackTargetType = 'energy';
          bot.logger.info(`Fallback target: Energy cube at location ${bestEnergyLocation.id}`);
        }

        // Store all energy locations for potential fallbacks
        this._allEnergyLocations = allEnergyLocations;
      }
    }

    // If we still don't have a target, try to select a journey target first
    if (primaryTargetNodeId === null) {
      // If we don't have a current journey target, try to select one now
      if (!this._currentTarget) {
        bot.logger.info(`No current journey target, attempting to select one now`);
        await this._selectTargetJourneyCard(bot);

        // If we successfully selected a journey target, use it
        if (this._currentTarget) {
          bot.logger.info(`Selected new journey target: ${this._currentTarget.id} at location ${this._currentTarget.locationId}`);
          primaryTargetNodeId = this._currentTarget.locationId;
          primaryTargetType = 'journey';
        } else {
          bot.logger.warn(`Failed to select a journey target`);
        }
      }

      // If we still don't have a target, find a random connected node as last resort
      if (primaryTargetNodeId === null) {
        const adjacent = this._getAdjacentNodes(state, currentNodeId);
        if (adjacent.length > 0) {
          const randomNode = adjacent[Math.floor(Math.random() * adjacent.length)];
          primaryTargetNodeId = randomNode.id;
          primaryTargetType = 'random';
          bot.logger.info(`No specific target, using random adjacent node ${primaryTargetNodeId}`);
        } else {
          throw new Error(`No adjacent nodes found for position ${currentNodeId}`);
        }
      }
    }

    // Try to find a path to the primary target first
    if (primaryTargetNodeId) {
      const cardValues = travelCards.map(card => card.value).sort((a, b) => a - b);
      const maxHops = cardValues.reduce((sum, val) => sum + val, 0);

      // Get the exact distance needed for the primary target
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);

      bot.logger.info(`Planning path to primary target ${primaryTargetNodeId} (${primaryTargetType}) with max ${maxHops} hops (exact distance needed: ${exactDistanceNeeded} hops)`);

      // Try to find a direct path to the target using our travel cards
      try {
        chosenPath = this._calculatePathToTarget(state, currentNodeId, primaryTargetNodeId, travelCards);

        // CRITICAL: Only use the path if the card values match EXACTLY with the needed distance
        if (chosenPath && chosenPath.cardValue !== exactDistanceNeeded) {
          bot.logger.warn(`Path to ${primaryTargetNodeId} found but card values (${chosenPath.cardValue}) don't match needed distance (${exactDistanceNeeded}) - cannot use`);
          chosenPath = null;
        }
      } catch (pathError) {
        bot.logger.error(`Error calculating direct path to primary target: ${pathError.message}`);
        chosenPath = null;
      }
    }

    // If we couldn't reach the primary target exactly, check if picking travel cards would help
    // Only do this for OM token or energy cube targets, not journey card targets
    if (!chosenPath && primaryTargetNodeId && (primaryTargetType === 'jyotirlinga' || primaryTargetType === 'energy')) {
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, primaryTargetNodeId);

      // Check if any face-up travel cards would help us reach the primary target
      const helpfulCardAction = await this._checkForHelpfulTravelCardsForTarget(bot, primaryTargetNodeId, exactDistanceNeeded);

      if (helpfulCardAction) {
        bot.logger.info(`Found helpful travel cards that would enable reaching primary target ${primaryTargetNodeId} (${primaryTargetType})`);
        // Return the action to pick these cards instead of moving to the fallback target
        return helpfulCardAction;
      } else {
        bot.logger.info(`No helpful travel cards found for primary target ${primaryTargetNodeId} (${primaryTargetType}), trying fallback`);
      }
    }

    // If we couldn't reach the primary target exactly and picking cards wouldn't help, try the fallback target
    if (!chosenPath && fallbackTargetNodeId) {
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, fallbackTargetNodeId);

      bot.logger.info(`Trying fallback target ${fallbackTargetNodeId} (${fallbackTargetType}) (exact distance needed: ${exactDistanceNeeded} hops)`);

      try {
        chosenPath = this._calculatePathToTarget(state, currentNodeId, fallbackTargetNodeId, travelCards);

        // CRITICAL: Only use the path if the card values match EXACTLY with the needed distance
        if (chosenPath && chosenPath.cardValue !== exactDistanceNeeded) {
          bot.logger.warn(`Path to fallback ${fallbackTargetNodeId} found but card values (${chosenPath.cardValue}) don't match needed distance (${exactDistanceNeeded}) - cannot use`);
          chosenPath = null;
        }
      } catch (pathError) {
        bot.logger.error(`Error calculating path to fallback target: ${pathError.message}`);
        chosenPath = null;
      }
    }

    // If we still don't have a path, try all energy locations if available
    if (!chosenPath && this._allEnergyLocations && this._allEnergyLocations.length > 0) {
      bot.logger.info(`Trying ${this._allEnergyLocations.length} energy locations as final fallback options`);

      // Try each energy location in order (already sorted by distance)
      for (const energyLocation of this._allEnergyLocations) {
        // Skip locations we've already tried
        if (energyLocation.id === primaryTargetNodeId || energyLocation.id === fallbackTargetNodeId) {
          continue;
        }

        const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, energyLocation.id);

        // Skip energy locations that would require more than 5 hops
        // (avoid spending too many travel cards on a single cube)
        if (exactDistanceNeeded > 5) {
          bot.logger.info(`Skipping energy location ${energyLocation.id} - distance is ${exactDistanceNeeded} which exceeds 5 hop limit`);
          continue;
        }

        bot.logger.info(`Trying energy location ${energyLocation.id} (${energyLocation.color}) (exact distance needed: ${exactDistanceNeeded} hops)`);

        try {
          const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, energyLocation.id, travelCards);

          // CRITICAL: Only use the path if the card values match EXACTLY with the needed distance
          if (pathToEnergy && pathToEnergy.cardValue === exactDistanceNeeded) {
            chosenPath = pathToEnergy;
            bot.logger.info(`Found exact path to alternative energy location ${energyLocation.id}`);
            break;
          }
        } catch (pathError) {
          // Continue to next energy location
          continue;
        }
      }

      // If we still couldn't find a path using the 5 hop limit,
      // try to find the nearest reachable energy location
      if (!chosenPath) {
        bot.logger.info(`No energy locations found within 5 hops, searching for nearest reachable location`);
        // Sort energy locations by distance (should already be sorted, but ensuring)
        const sortedEnergyLocations = [...this._allEnergyLocations].sort((a, b) => a.distance - b.distance);

        for (const energyLocation of sortedEnergyLocations) {
          // Skip locations we've already tried
          if (energyLocation.id === primaryTargetNodeId || energyLocation.id === fallbackTargetNodeId) {
            continue;
          }

          const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, energyLocation.id);
          bot.logger.info(`Checking nearest energy location ${energyLocation.id} (${energyLocation.color}) at distance ${exactDistanceNeeded}`);

          try {
            const pathToEnergy = this._calculatePathToTarget(state, currentNodeId, energyLocation.id, travelCards);

            if (pathToEnergy && pathToEnergy.cardValue === exactDistanceNeeded) {
              chosenPath = pathToEnergy;
              bot.logger.info(`Found exact path to nearest energy location ${energyLocation.id} at distance ${exactDistanceNeeded}`);
              break;
            }
          } catch (pathError) {
            continue;
          }
        }
      }
    }

    // If we still don't have a path, try a random adjacent node for last resort
    if (!chosenPath) {
      bot.logger.warn(`Could not find exact path to any strategic target, checking for random adjacent node`);

      // Try each adjacent node
      const adjacent = this._getAdjacentNodes(state, currentNodeId);
      if (adjacent.length > 0) {
        // Shuffle adjacent nodes for randomness
        const shuffledAdjacent = [...adjacent].sort(() => Math.random() - 0.5);

        for (const node of shuffledAdjacent) {
          const nodeId = node.id;
          const exactDistanceNeeded = 1; // Adjacent nodes are always 1 hop away

          try {
            const pathToNode = this._calculatePathToTarget(state, currentNodeId, nodeId, travelCards);

            // CRITICAL: Only use the path if the card values match EXACTLY with the needed distance
            if (pathToNode && pathToNode.cardValue === exactDistanceNeeded) {
              chosenPath = pathToNode;
              bot.logger.info(`Found exact path to adjacent node ${nodeId} as last resort`);
              break;
            }
          } catch (pathError) {
            // Continue to next node
            continue;
          }
        }
      }
    }

    // If we have a path (with EXACT card matches), create a move action
    if (chosenPath && chosenPath.path && chosenPath.path.length > 0 && chosenPath.cards && chosenPath.cards.length > 0) {
      // Check if we're trying to move to the same location (path with only one node or start=end)
      if (chosenPath.path.length === 1 || (chosenPath.path.length > 1 && chosenPath.path[0] === chosenPath.path[chosenPath.path.length - 1])) {
        bot.logger.warn(`Attempted to create a move action to the current location ${chosenPath.path[0]} - skipping`);
        return null;
      }

      bot.logger.info(`Found exact path: ${this._formatPath(chosenPath.path)} (distance: ${chosenPath.distance} hops, using cards worth ${chosenPath.cardValue} hops)`);

      this._turnState.hasMoved = true;

      // Create the move action with proper parameters
      const moveAction = {
        action: 'movePlayer',
        params: {
          path: chosenPath.path,
          cardIds: chosenPath.cards.map(c => c.id),
          extraHopCount: 0,  // Default to 0 extra hops
          isTriathlon: false // Default to false for triathlon
        }
      };

      return moveAction;
    }

    // If we couldn't find any valid path with exact card matches
    bot.logger.warn(`Could not find any valid path with exact card matches from ${currentNodeId}`);
    return null; // Don't throw error, just return null so the bot will try picking cards instead
  }

  /**
   * Find all energy locations sorted by distance
   * @param {Object} state - The game state
   * @param {Object} player - The player
   * @param {Object} bot - The bot
   * @param {Object} missingCubes - Object mapping cube colors to quantities needed
   * @returns {Array} - Array of energy locations sorted by distance
   * @private
   */
  _findAllEnergyLocations(state, player, bot, missingCubes) {
    if (!state || !player) {
      throw new Error('Invalid state or player in _findAllEnergyLocations');
    }

    if (!missingCubes || typeof missingCubes !== 'object' || Object.keys(missingCubes).length === 0) {
      bot.logger.info('No missing cubes to search for');
      return [];
    }

    const currentPosition = player.position || player.currentNodeId;
    if (!currentPosition) {
      throw new Error(`Bot ${bot.name} has undefined position, cannot find energy locations`);
    }

    try {
      // In the game, energy cubes are at locations 1-48
      const energyLocationIds = Array.from({length: 48}, (_, i) => i + 1);

      // Check if locationCubes exists in state
      if (!state.locationCubes) {
        throw new Error('locationCubes is not defined in state - simulation must use real server data');
      }

      // Find all energy locations with cubes we need
      const validEnergyLocations = [];
      const neededColors = Object.keys(missingCubes);

      bot.logger.info(`Looking for energy locations with: ${neededColors.join(', ')}`);

      for (const locationId of energyLocationIds) {
        // Skip if this location doesn't have cubes
        if (!state.locationCubes[locationId]) continue;

        // Check if this location has one of our missing cube colors
        const locationCubes = state.locationCubes[locationId];

        for (const color of neededColors) {
          if (locationCubes.includes(color)) {
            // This location has a cube we need
            // Get the actual cubes at this location
            const cubesAtLocation = state.locationCubes[locationId];
            validEnergyLocations.push({
              id: locationId,
              distance: this._estimateTravelDistance(state, currentPosition, locationId),
              color,
              availableCubes: cubesAtLocation
            });
            // Only count a location once even if it has multiple colors we need
            break;
          }
        }
      }

      // Sort by distance (ascending)
      validEnergyLocations.sort((a, b) => a.distance - b.distance);

      // Log detailed information about found energy locations
      bot.logger.info(`Found ${validEnergyLocations.length} energy locations with needed cubes`);

      // Log the first few locations with their available cubes
      const locationsToLog = validEnergyLocations.slice(0, 3); // Log up to 3 locations
      locationsToLog.forEach(loc => {
        const cubesStr = Array.isArray(loc.availableCubes) ? loc.availableCubes.join(', ') : loc.availableCubes;
        bot.logger.info(`Energy location ${loc.id}: distance=${loc.distance}, needed=${loc.color}, available=[${cubesStr}]`);
      });
      return validEnergyLocations;
    } catch (error) {
      bot.logger.error(`Error finding energy locations: ${error.message}`);
      return [];
    }
  }

  /**
   * Collect the target journey card if possible
   * @param {Bot} bot - The bot
   * @returns {Object|null} - Action to collect journey card, or null
   * @private
   */
  async _collectTargetJourneyCard(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !this._currentTarget) {
      return null;
    }

    // Log the journey card we're attempting to collect
    const target = this._currentTarget;
    const player = state.player;
    const isInnerJourney = target.cardType === 'inner';
    
    bot.logger.info(`\n========== ATTEMPTING JOURNEY CARD COLLECTION ==========`);
    bot.logger.info(`Bot ${bot.name} attempting to collect journey card ${target.id}`);
    bot.logger.info(`Journey location: ${target.locationId}, current position: ${player.position}`);
    bot.logger.info(`Journey type: ${target.cardType}, points: ${isInnerJourney ? target.reward.inner : target.reward.outer}`);
    
    // Log energy cube requirements in detail
    const requiredCubes = target.requiredCubes || {};
    bot.logger.info(`Required cubes: ${JSON.stringify(requiredCubes)}`);
    
    // Log the player's current energy cubes
    const playerCubesByType = { bhakti: 0, gnana: 0, karma: 0, artha: 0 };
    
    // Handle both array and object formats for energy cubes
    if (Array.isArray(player.energyCubes)) {
      player.energyCubes.forEach(cube => {
        if (playerCubesByType[cube] !== undefined) {
          playerCubesByType[cube]++;
        }
      });
      bot.logger.info(`Player energy cubes (array format): [${player.energyCubes.join(', ')}]`);
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      Object.assign(playerCubesByType, player.energyCubes);
      bot.logger.info(`Player energy cubes (object format): ${JSON.stringify(player.energyCubes)}`);
    }
    
    bot.logger.info(`Summarized player cubes: ${JSON.stringify(playerCubesByType)}`);
    
    // Check if we can collect the journey card
    if (!this._canCollectCurrentTargetJourney(bot)) {
      bot.logger.info(`Cannot collect journey card - requirements not met`);
      bot.logger.info(`=======================================================\n`);
      return null;
    }

    // Check if we have all travel cards to reach the location
    const currentNodeId = state.player.position || state.player.currentNodeId;
    const targetNodeId = this._currentTarget.locationId;

    // Only attempt to collect if we're already at the target location
    if (currentNodeId !== targetNodeId) {
      // Calculate if we have exact travel cards to reach target
      const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, targetNodeId);
      const travelCards = this._getTravelCards(bot);
      let canTravelExactly = false;

      try {
        const path = this._calculatePathToTarget(state, currentNodeId, targetNodeId, travelCards);
        canTravelExactly = (path && path.cardValue === exactDistanceNeeded);
      } catch (error) {
        canTravelExactly = false;
      }

      // If we can travel exactly to the target and all requirements are met,
      // don't pick up travel cards but prioritize collecting the journey card
      if (canTravelExactly) {
        bot.logger.info(`Bot ${bot.name} has all travel cards to reach journey location ${targetNodeId} and can collect it - prioritizing journey collection`);
      }
    }

    bot.logger.info(`All requirements met, proceeding with journey card collection`);
    bot.logger.info(`=======================================================\n`);

    // Create the collectJourney action
    return {
      action: 'collectJourney',
      params: {
        journeyCardId: target.id,
        journeyType: target.cardType
      }
    };
  }

  /**
   * Evaluate possible card picks
   * @param {Bot} bot - Bot to evaluate card picks for
   * @param {string} cardType - Type of card to evaluate ('travel' or 'event')
   * @returns {Object|null} - Card picking action or null if no cards to pick
   */
  async evaluateCardPicks(bot, cardType = 'travel') {
    try {
      const state = bot.getPlayerState();
      if (!state || !state.player) return null;

      // Check hand size to determine if we can pick cards
      const handSize = state.player.hand ? state.player.hand.length : 0;
      if (handSize >= 4) {
        bot.logger.info(`Bot ${bot.name} hand is full (${handSize}/4 cards), cannot pick more cards`);
        return null;
      }

      // Get face-up cards of the requested type
      const faceUpCards = cardType === 'travel'
        ? (state.faceUpTravel || [])
        : (state.faceUpEvent || []);

      // If there are no face-up cards, pick from the top of the deck
      if (!faceUpCards || faceUpCards.length === 0) {
        bot.logger.info(`No face-up ${cardType} cards available, picking from top of deck`);
        const pickAction = {
          action: 'pickCards',
          params: {
            type: cardType,
            pickFromTop: true
          }
        };
        
        // Set the appropriate flag based on card type
        this._turnState.hasCollectedCards = true;
        if (cardType === 'travel') {
          this._turnState.hasCollectedTravelCards = true;
        }
        
        return pickAction;
      }

      // Get current global event to check for vehicle-specific rewards
      const globalEvent = this._getCurrentGlobalEvent(bot);
      const rewardedVehicles = this._getRewardedVehicleFromGlobalEvent(globalEvent);
      
      // Log rewarded vehicles info
      if (Array.isArray(rewardedVehicles)) {
        bot.logger.info(`Rewarded vehicles: ${rewardedVehicles.join(', ')}`);
      } else if (rewardedVehicles) {
        bot.logger.info(`Rewarded vehicle: ${rewardedVehicles}`);
      }
      
      // STRATEGY POINT iv: Always prioritize cards that provide additional points due to current global event
      // Check this first before any other card selection strategy when a vehicle is rewarded by the global event
      if (cardType === 'travel' && rewardedVehicles) {
        const rewardedVehiclesList = Array.isArray(rewardedVehicles) ? rewardedVehicles : [rewardedVehicles];
        
        bot.logger.info(`STRATEGY POINT iv: Current global event rewards using the ${rewardedVehiclesList.join(' or ')} vehicle - prioritizing these cards`);
        
        // Check if we already have a rewarded vehicle card in hand
        const currentTravelCards = this._getTravelCards(bot);
        const alreadyHasRewardedVehicle = currentTravelCards.some(card => 
          rewardedVehiclesList.includes(card.vehicle)
        );
        
        if (alreadyHasRewardedVehicle) {
          bot.logger.info(`STRATEGY POINT iv: Bot already has one of the rewarded vehicle cards in hand, will not prioritize picking another one`);
        }
        // ... rest of the method continues here ...
        
        // Existing code continues below...
      }

      // SPECIAL CASE: Bot has a target journey card, has all resources, has max energy cubes, but lacks exact travel cards
      // Check if we're in the special case scenario described in planTurn
      if (cardType === 'travel' && this._currentTarget) {
        try {
          const resourceNeeds = await this._calculateResourceNeed(bot);
          if (!resourceNeeds.needsOmTokens && !resourceNeeds.needsEnergyCubes) {
            const totalEnergyCubes = this._countEnergyCubes(state.player);
            if (totalEnergyCubes === 5) {
              const currentTravelCards = this._getTravelCards(bot);
              const targetNodeId = this._currentTarget.locationId;
              const currentNodeId = state.player.position || state.player.currentNodeId;
              const exactDistanceNeeded = this._estimateTravelDistance(state, currentNodeId, targetNodeId);

              // Check if any face-up travel cards would help reach the target with exact distance
              const helpfulCardAction = await this._checkForHelpfulTravelCardsForTarget(
                bot,
                targetNodeId,
                exactDistanceNeeded,
                currentNodeId
              );

              if (helpfulCardAction) {
                bot.logger.info(`SPECIAL STRATEGY: Found helpful travel cards for exact travel to journey target at ${targetNodeId} (${exactDistanceNeeded} steps)`);
                return helpfulCardAction;
              } else {
                bot.logger.info(`SPECIAL STRATEGY: No helpful face-up travel cards for exact travel to journey target. Will pick from deck.`);
                return {
                  action: 'pickCards',
                  params: {
                    type: cardType,
                    pickFromTop: true
                  }
                };
              }
            }
          }
        } catch (error) {
          bot.logger.error(`Error in special case evaluation: ${error.message}`);
          // Continue with normal evaluation if special case fails
        }
      }

      // STRATEGY POINT 6.ii: If we've traveled during step 3 or 4, choose travel cards for the next turn target
      if (cardType === 'travel' && this._turnState && this._turnState.hasMoved) {
        // Get the next turn target based on current travel
        const nextTurnTarget = this._getNextTurnTarget(bot);

        if (nextTurnTarget) {
          bot.logger.info(`Bot ${bot.name} implementing strategy point 6.ii: Choosing travel cards for next turn target at location ${nextTurnTarget.locationId}`);

          // Determine the position the bot will be at after the current move
          // If the bot is moving this turn, use the destination of the move as the starting point
          let startingNodeId;

          // Check if we're in the middle of a move action by looking at the turnState
          // If we've already moved this turn, we need to check if we're targeting a resource location
          if (this._turnState && this._turnState.hasMoved) {
            // We've moved this turn, so we need to determine where we're moving to
            // Check if we have a resource plan with a target location
            if (this._resourcePlan) {
              if (this._resourcePlan.needsOmTokens && this._resourcePlan.targetJyotirlinga) {
                // We're moving to a jyotirlinga to collect OM tokens
                // Make sure we're using the ID, not the object
                startingNodeId = typeof this._resourcePlan.targetJyotirlinga === 'object' ?
                  this._resourcePlan.targetJyotirlinga.id || this._resourcePlan.targetJyotirlinga.locationId :
                  this._resourcePlan.targetJyotirlinga;

                bot.logger.info(`Bot ${bot.name} will be at jyotirlinga ${startingNodeId} after current move (not current position ${state.player.position || state.player.currentNodeId})`);
              } else if (this._resourcePlan.needsEnergyCubes && this._resourcePlan.targetEnergyLocation) {
                // We're moving to an energy location to collect cubes
                startingNodeId = this._resourcePlan.targetEnergyLocation.id;
                bot.logger.info(`Bot ${bot.name} will be at energy location ${startingNodeId} after current move (not current position ${state.player.position || state.player.currentNodeId})`);
              } else if (this._currentTarget && this._currentTarget.locationId) {
                // We're moving to the journey card location
                startingNodeId = this._currentTarget.locationId;
                bot.logger.info(`Bot ${bot.name} will be at journey location ${startingNodeId} after current move (not current position ${state.player.position || state.player.currentNodeId})`);
              } else {
                // If we can't determine the target, use current position
                startingNodeId = state.player.position || state.player.currentNodeId;
                bot.logger.info(`Bot ${bot.name} using current position ${startingNodeId} as no target location found`);
              }
            } else {
              // If no resource plan, use current position
              startingNodeId = state.player.position || state.player.currentNodeId;
              bot.logger.info(`Bot ${bot.name} using current position ${startingNodeId} as no resource plan found`);
            }
          } else {
            // If we haven't moved this turn, use current position
            startingNodeId = state.player.position || state.player.currentNodeId;
            bot.logger.info(`Bot ${bot.name} using current position ${startingNodeId} as no move has occurred this turn`);
          }

          // Calculate the exact distance needed to reach the next turn target from the new position
          const exactDistanceNeeded = this._estimateTravelDistance(state, startingNodeId, nextTurnTarget.locationId);

          // Determine which cards will be used for movement in the current turn
          let cardsUsedForMovement = [];

          // If we're in the middle of a move, try to determine which cards will be used
          if (this._turnState && this._turnState.hasMoved) {
            // Try to find the cards that will be used for movement based on the distance
            const currentTravelCards = this._getTravelCards(bot);
            const currentNodeId = state.player.position || state.player.currentNodeId;

            // If we're moving to a different location, calculate the cards needed
            if (currentNodeId !== startingNodeId) {
              try {
                // Calculate the path to the target
                // We don't need to calculate the distance separately as _calculatePathToTarget will do it
                const pathToTarget = this._calculatePathToTarget(state, currentNodeId, startingNodeId, currentTravelCards);

                if (pathToTarget && pathToTarget.cards) {
                  cardsUsedForMovement = pathToTarget.cards;
                  bot.logger.info(`Determined ${cardsUsedForMovement.length} cards will be used for movement: ${cardsUsedForMovement.map(c => `${c.id}(${c.value})`).join(', ')}`);
                }
              } catch (error) {
                bot.logger.warn(`Could not determine cards for movement: ${error.message}`);
              }
            }
          }

          // Check if any face-up travel cards would help reach the next turn target
          const helpfulCardAction = await this._checkForHelpfulTravelCardsForTarget(
            bot,
            nextTurnTarget.locationId,
            exactDistanceNeeded,
            startingNodeId,
            cardsUsedForMovement
          );

          if (helpfulCardAction) {
            bot.logger.info(`Bot ${bot.name} found helpful travel cards for next turn target at ${nextTurnTarget.locationId}`);
            return helpfulCardAction;
          } else {
            bot.logger.info(`Bot ${bot.name} couldn't find helpful travel cards for next turn target, falling back to highest value cards`);
          }
        }
      }

      // FALLBACK: Always ensure to pick 2 travel cards if hand permits (rule 6.iii)
      // If we've reached this point and haven't found a strategic reason to pick specific cards,
      // but we still have hand space, pick the lowest missing value cards from 1, 2, 3
      if (cardType === 'travel' && handSize < 4 && faceUpCards.length > 0) {
        bot.logger.info(`Bot ${bot.name} enforcing rule 6.iii: Always picking travel cards when hand permits`);

        // Get unique values in the bot's current hand
        const currentHandValues = new Set();
        const travelCardsInHand = this._getTravelCards(bot);
        travelCardsInHand.forEach(card => {
          if (card.value) {
            currentHandValues.add(card.value);
          }
        });
        
        // Find the lowest missing value from 1, 2, 3
        const lowValues = [1, 2, 3];
        const missingLowValues = lowValues.filter(value => !currentHandValues.has(value));
        
        // First, prioritize cards with missing low values
        const cardsWithMissingLowValues = faceUpCards.filter(card => 
          missingLowValues.includes(card.value));
          
        // Sort by value ascending to get the lowest missing values first
        let sortedCards;
        if (cardsWithMissingLowValues.length > 0) {
          sortedCards = [...cardsWithMissingLowValues].sort((a, b) => 
            missingLowValues.indexOf(a.value) - missingLowValues.indexOf(b.value));
          bot.logger.info(`Bot ${bot.name} prioritizing missing low values: ${missingLowValues.join(', ')}`);
        } else {
          // Fallback to highest value if no missing low values are available
          sortedCards = [...faceUpCards].sort((a, b) => (b.value || 0) - (a.value || 0));
          bot.logger.info(`Bot ${bot.name} no missing low values found, falling back to highest value cards`);
        }

        // Pick up to 2 cards based on hand space
        const maxPickCount = Math.min(4 - handSize, 2, sortedCards.length);
        const cardsToPick = sortedCards.slice(0, maxPickCount);
        const cardIds = cardsToPick.map(card => card.id);

        bot.logger.info(`Bot ${bot.name} picking ${cardIds.length} travel cards: ${cardIds.join(', ')}`);

        // Set the state flags for collecting cards
        this._turnState.hasCollectedCards = true;
        this._turnState.hasCollectedTravelCards = true;

        return {
          action: 'pickCards',
          params: {
            type: 'travel',
            pickFromFaceUp: cardIds
          }
        };
      }

      // If we got here and it's not a travel card request or no cards available, return null
      return null;
    } catch (error) {
      bot.logger.error(`Error evaluating card picks: ${error.message} ${error.stack}`);
      return null;
    }
  }

  /**
   * Find a path between two nodes using PathCalculator
   * @param {Object} state - The game state
   * @param {string|number} startNodeId - Starting node ID
   * @param {string|number} targetNodeId - Target node ID
   * @returns {Array|null} - Array of node IDs in the path, or null if no path found
   * @private
   */
  _findPath(state, startNodeId, targetNodeId) {
    if (!state) {
      throw new Error('Game state is missing in _findPath');
    }

    if (!state.edges) {
      throw new Error('Game state does not contain edges data - simulation must use real server data');
    }

    if (!startNodeId || !targetNodeId) {
      throw new Error(`Invalid node IDs: start=${startNodeId}, target=${targetNodeId}`);
    }

    // Convert node IDs to numbers for the PathCalculator
    const startId = parseInt(startNodeId);
    const targetId = parseInt(targetNodeId);

    // If start and target are the same, return single-node path
    if (startId === targetId) {
      return [startId];
    }

    // Use the existing PathCalculator to find the shortest path
    // We pass a simulated large travel card to ensure we find any path
    try {
      const paths = this.pathCalculator.findPathsToDestination(
        startId,
        targetId,
        [{ value: 100 }], // Simulated large travel card to ensure we find any path
        0
      );

      if (!paths) {
        throw new Error(`PathCalculator returned null paths for ${startId} to ${targetId}`);
      }

      if (!Array.isArray(paths)) {
        throw new Error(`PathCalculator returned non-array: ${typeof paths}`);
      }

      if (paths.length === 0) {
        return null;
      }

      // Return the first (shortest) path
      return paths[0];
    } catch (error) {
      // Rethrow with more context instead of silently handling
      throw new Error(`Path calculation failed: ${error.message}`);
    }
  }

  /**
   * Calculate a path to a target using specific travel cards
   * @param {Object} state - The game state
   * @param {string|number} startNodeId - Starting node ID
   * @param {string|number} targetNodeId - Target node ID
   * @param {Array} travelCards - Array of travel cards available
   * @param {boolean} [allowFaceUpCards=false] - Whether to allow face-up cards
   * @returns {Object|null} - Path object with path array and card value, or null if no path
   * @private
   */
  _calculatePathToTarget(state, startNodeId, targetNodeId, travelCards, allowFaceUpCards = false) {
    if (!state) {
      throw new Error('Game state is missing in _calculatePathToTarget');
    }

    if (!travelCards || !Array.isArray(travelCards)) {
      throw new Error('Travel cards must be an array');
    }

    // Special case: If already at target location, return null to indicate no movement needed
    if (startNodeId === targetNodeId) {
      console.log(`Already at target location ${targetNodeId}, no movement needed`);
      return null; // Return null instead of a path with no cards
    }

    // Check if Turbulent Skies global event is active (no airport travel)
    // This needs to be checked even if avoidAirports flag isn't set yet
    const globalEvent = this._getCurrentGlobalEvent({ getPlayerState: () => state });
    const turbulentSkiesActive = globalEvent && globalEvent.effect === 'no_airport_travel';
    const avoidAirports = turbulentSkiesActive || (this._turnState && this._turnState.avoidAirports);

    // Get the rewarded vehicle from the current global event
    const rewardedVehicle = this._getRewardedVehicleFromGlobalEvent(globalEvent);
    
    // Verify that all cards exist in player's hand
    const playerHand = state.player?.hand || [];
    const playerCardIds = new Set(playerHand.map(card => card.id));
    const validTravelCards = travelCards.filter(card => playerCardIds.has(card.id));

    // If some cards aren't in the player's hand anymore, log a warning and use only valid cards
    if (validTravelCards.length < travelCards.length && !allowFaceUpCards) {
      console.log(`Warning: Some travel cards no longer in player hand. Original: ${travelCards.map(c => c.id).join(',')}, Valid: ${validTravelCards.map(c => c.id).join(',')}`);
      // Use only the valid cards for path calculation
      travelCards = validTravelCards;

      // If no valid cards remain, return null
      if (travelCards.length === 0) {
        console.log('No valid travel cards in hand, cannot calculate path');
        return null;
      }
    }

    // Get the shortest path first to know the path to follow
    let shortestPath = this._findPath(state, startNodeId, targetNodeId);
    if (!shortestPath) {
      return null; // No path exists between these points
    }

    // Check if Drizzle of Delay global event is active (max 2 moves)
    const isDrizzleOfDelay = globalEvent && globalEvent.effect === 'max_moves_2_and_cost_artha_north_east';

    // Check if we need to avoid airport travel (during Turbulent Skies global event)
    if (avoidAirports) {
      // Check if the path includes airports (nodes 61-66)
      const hasAirports = shortestPath.some(nodeId => {
        const id = parseInt(nodeId);
        return id >= 61 && id <= 66;
      });

      if (hasAirports) {
        console.log(`Path from ${startNodeId} to ${targetNodeId} includes airports, but we must avoid airports due to Turbulent Skies event`);
        return null; // Cannot use this path during Turbulent Skies
      }
    }

    // Check if we need to avoid ending in North or East regions (during Drizzle of Delay)
    if (isDrizzleOfDelay && !this._hasArthaCube(state)) {
      // Check if the path ends in North (nodes 1-12) or East (nodes 25-36) regions
      const lastNodeId = parseInt(shortestPath[shortestPath.length - 1]);
      const endsInNorth = lastNodeId >= 1 && lastNodeId <= 12;
      const endsInEast = lastNodeId >= 25 && lastNodeId <= 36;

      if (endsInNorth || endsInEast) {
        console.log(`Path from ${startNodeId} to ${targetNodeId} ends in ${endsInNorth ? 'North' : 'East'}, but we have no Artha cubes during Drizzle of Delay event`);
        // Don't return null here, but reduce the priority of this path
        // We'll still use it if no better options are available
        return { cards: [], cardValue: Infinity, penalty: 10 };
      }
    }

    // Calculate the exact distance needed based on the path
    // If Drizzle of Delay is active and path is longer than 2 hops, truncate it
    if (isDrizzleOfDelay && shortestPath.length > 3) { // path.length - 1 = hops
      console.log(`Limiting path from ${startNodeId} to ${targetNodeId} to 2 hops due to Drizzle of Delay global event`);
      shortestPath = shortestPath.slice(0, 3); // Limit to start point + 2 more locations
    }

    // Need to account for airports which count as 1 hop regardless of real distance
    let exactDistanceNeeded = 0;
    for (let i = 0; i < shortestPath.length - 1; i++) {
      const currentNode = parseInt(shortestPath[i]);
      const nextNode = parseInt(shortestPath[i+1]);

      // Check if this is airport-to-airport travel (always 1 hop)
      const isCurrentAirport = currentNode >= 61 && currentNode <= 66;
      const isNextAirport = nextNode >= 61 && nextNode <= 66;

      // Skip airport travel if we need to avoid it
      if (avoidAirports && (isCurrentAirport || isNextAirport)) {
        console.log(`Cannot travel via airports due to Turbulent Skies global event`);
        return null;
      }

      if (isCurrentAirport && isNextAirport) {
        // Direct airport travel counts as 1 hop
        exactDistanceNeeded += 1;
      } else {
        // Regular travel counts as 1 hop per connection
        exactDistanceNeeded += 1;
      }
    }

    // Check for rewarded vehicle cards that match the exact distance
    if (rewardedVehicle) {
      // Create array of rewarded vehicles
      const rewardedVehicles = Array.isArray(rewardedVehicle) ? rewardedVehicle : [rewardedVehicle];
      
      // Filter for cards with any of the rewarded vehicle types
      const rewardedCards = travelCards.filter(card => rewardedVehicles.includes(card.vehicle));
      
      if (rewardedCards.length > 0) {
        console.log(`Found ${rewardedCards.length} cards with rewarded vehicle types: ${rewardedVehicles.join(', ')}`);
        
        // Only consider the first rewarded vehicle card since the reward only applies once
        if (rewardedCards.length > 1) {
          console.log(`Only using the first rewarded vehicle card since the reward only applies once`);
          rewardedCards.splice(1); // Keep only the first card
        }
        
        // First priority: Single rewarded vehicle card that matches exactly
        for (const card of rewardedCards) {
          if ((card.value || 0) === exactDistanceNeeded) {
            console.log(`STRATEGY POINT iv: Using rewarded ${card.vehicle} card ${card.id} with exact value ${card.value} to get global event bonus`);
            return {
              path: shortestPath,
              cards: [card],
              cardValue: exactDistanceNeeded,
              distance: exactDistanceNeeded
            };
          }
        }
        
        // Second priority: Try combinations that include at least one rewarded vehicle card
        const cardValues = travelCards.map(card => card.value || 0);
        const possibleCombos = this._findExactValueCombinations(travelCards, cardValues, exactDistanceNeeded);
        
        if (possibleCombos.length > 0) {
          // Filter for combinations that include at least one rewarded vehicle card
          const combosWithRewardedVehicle = possibleCombos.filter(combo => 
            combo.cards.some(card => rewardedVehicles.includes(card.vehicle))
          );
          
          if (combosWithRewardedVehicle.length > 0) {
            // Sort by which combinations use the rewarded vehicle card efficiently
            combosWithRewardedVehicle.sort((a, b) => {
              // Prefer combinations with fewer total cards
              return a.cards.length - b.cards.length;
            });
            
            const bestCombo = combosWithRewardedVehicle[0];
            // Find which rewarded vehicle is in this combo
            const usedRewardedVehicle = bestCombo.cards.find(card => rewardedVehicles.includes(card.vehicle)).vehicle;
            
            console.log(`STRATEGY POINT iv: Using combination with ${usedRewardedVehicle} card to get global event bonus: ${bestCombo.values.join(' + ')} = ${bestCombo.sum}`);
            
            return {
              path: shortestPath,
              cards: bestCombo.cards,
              cardValue: exactDistanceNeeded,
              distance: exactDistanceNeeded
            };
          }
        }
      }
    }

    // Fall back to previous logic if no rewarded vehicle cards can be used
    // First, check if we have a single card that exactly matches the distance needed
    for (const card of travelCards) {
      if ((card.value || 0) === exactDistanceNeeded) {
        console.log(`Found single card ${card.id} with exact value ${card.value} matching needed distance ${exactDistanceNeeded}`);
        return {
          path: shortestPath,
          cards: [card],
          cardValue: exactDistanceNeeded,
          distance: exactDistanceNeeded
        };
      }
    }

    // If no single card matches, try all possible card combinations
    const cardValues = travelCards.map(card => card.value || 0);
    const possibleCombos = this._findExactValueCombinations(travelCards, cardValues, exactDistanceNeeded);

    if (possibleCombos.length === 0) {
      console.log(`Could not find any valid path with exact card matches from ${startNodeId} to ${targetNodeId} (distance: ${exactDistanceNeeded})`);
      console.log(`Available cards: ${travelCards.map(c => `${c.id}(${c.value})`).join(', ')}`);
      return null; // No card combination matches exactly
    }

    // Sort combinations by number of cards (prefer using fewer cards)
    possibleCombos.sort((a, b) => a.cards.length - b.cards.length);

    // Use the first (best) combination
    const bestCombo = possibleCombos[0];
    console.log(`Found card combination for distance ${exactDistanceNeeded}: ${bestCombo.values.join(' + ')} = ${bestCombo.sum}`);

    return {
      path: shortestPath,
      cards: bestCombo.cards,
      cardValue: exactDistanceNeeded,
      distance: exactDistanceNeeded
    };
  }

  /**
   * Find all combinations of cards that sum to exactly targetValue
   * @param {Array} cards - Array of card objects
   * @param {Array} values - Array of card values
   * @param {number} targetValue - Target sum to reach
   * @returns {Array} - Array of valid combinations
   * @private
   */
  _findExactValueCombinations(cards, values, targetValue) {
    const results = [];

    // Helper function to find all combinations recursively
    const findCombinations = (index, currentSum, currentCards) => {
      // If we've reached the target value exactly, add this combination
      if (currentSum === targetValue) {
        // Get the values of the cards in this combination
        const cardValues = currentCards.map(card => card.value || 0);
        results.push({
          sum: currentSum,
          cards: [...currentCards],
          values: cardValues // Add values array for easier logging
        });
        return;
      }

      // If we've gone over or reached the end, stop
      if (currentSum > targetValue || index >= values.length) {
        return;
      }

      // Try adding the current card
      currentCards.push(cards[index]);
      findCombinations(index + 1, currentSum + values[index], currentCards);
      currentCards.pop();

      // Skip the current card
      findCombinations(index + 1, currentSum, currentCards);
    };

    // Start the recursive search
    findCombinations(0, 0, []);
    return results;
  }

  /**
   * Checks if any face-up travel cards would help reach a target destination with EXACT matches
   * Specifically for use in the _planStrategicMove method when checking primary targets
   * @param {Bot} bot - The bot
   * @param {number} targetNodeId - The target node ID
   * @param {number} exactDistanceNeeded - The exact distance needed to reach the target
   * @param {number|string} [overrideStartNodeId] - Optional override for the starting node ID
   * @param {Array} [cardsUsedForMovement=[]] - Cards that will be used for movement in the current turn
   * @returns {Object|null} - A card picking action, or null
   * @private
   */
  async _checkForHelpfulTravelCardsForTarget(bot, targetNodeId, exactDistanceNeeded, overrideStartNodeId, cardsUsedForMovement = []) {
    // Validate targetNodeId
    if (typeof targetNodeId === 'object') {
      // If it's an object, try to get the id or locationId property
      targetNodeId = targetNodeId.id || targetNodeId.locationId;
      bot.logger.warn(`Converted target object to ID: ${targetNodeId}`);
    }

    // Convert to number if it's a string
    if (typeof targetNodeId === 'string') {
      targetNodeId = parseInt(targetNodeId, 10);
    }

    // Final validation for target
    if (!targetNodeId || isNaN(targetNodeId)) {
      bot.logger.error(`Invalid target node ID: ${targetNodeId}, cannot check for helpful cards`);
      return null;
    }

    // Validate exactDistanceNeeded
    if (typeof exactDistanceNeeded !== 'number' || isNaN(exactDistanceNeeded)) {
      bot.logger.error(`Invalid exact distance needed: ${exactDistanceNeeded}, cannot check for helpful cards`);
      return null;
    }
    const state = bot.getPlayerState();
    if (!state || !state.player) {
      return null;
    }

    // Get current global event to check for vehicle-specific rewards
    const globalEvent = this._getCurrentGlobalEvent(bot);
    const rewardedVehicle = this._getRewardedVehicleFromGlobalEvent(globalEvent);
    if (rewardedVehicle) {
      if (Array.isArray(rewardedVehicle)) {
        bot.logger.info(`Current global event rewards using any of these vehicles: ${rewardedVehicle.join(', ')}`);
      } else {
        bot.logger.info(`Current global event rewards using the ${rewardedVehicle} vehicle`);
      }
    }

    // Get resource needs for special case detection
    let resourceNeeds = null;
    if (this._currentTarget && this._countEnergyCubes(state.player) === 5) {
      // Only calculate resource needs if we might be in the special case
      resourceNeeds = await this._calculateResourceNeed(bot);
    }

    // Check if we're in the special strategy case (target journey, all resources, max energy, no exact travel cards)
    const isSpecialCase = this._currentTarget &&
                        this._countEnergyCubes(state.player) === 5 &&
                        state.player &&
                        resourceNeeds &&
                        resourceNeeds.needsOmTokens === false &&
                        resourceNeeds.needsEnergyCubes === false;

    if (isSpecialCase) {
      bot.logger.info(`SPECIAL STRATEGY: Checking for travel cards for exact ${exactDistanceNeeded}-step journey to target at ${targetNodeId}`);
    }

    // Get starting position - use override if provided, otherwise use current position
    let currentNodeId = overrideStartNodeId || state.player.position || state.player.currentNodeId;

    // Ensure currentNodeId is a valid number
    if (typeof currentNodeId === 'object') {
      // If it's an object, try to get the id or locationId property
      currentNodeId = currentNodeId.id || currentNodeId.locationId;
      bot.logger.warn(`Converted object to ID: ${currentNodeId}`);
    }

    // Convert to number if it's a string
    if (typeof currentNodeId === 'string') {
      currentNodeId = parseInt(currentNodeId, 10);
    }

    // Final validation
    if (!currentNodeId || isNaN(currentNodeId)) {
      bot.logger.error(`Invalid starting node ID: ${currentNodeId}, falling back to current position`);
      currentNodeId = state.player.position || state.player.currentNodeId;

      // If still invalid, we can't proceed
      if (!currentNodeId || isNaN(currentNodeId)) {
        bot.logger.error(`Cannot determine valid starting position, aborting card check`);
        return null;
      }
    }

    // Log if we're using an override starting position
    if (overrideStartNodeId) {
      bot.logger.info(`Using override starting position ${currentNodeId} instead of current position ${state.player.position || state.player.currentNodeId}`);
    }

    // Get current travel cards in hand
    const currentTravelCards = this._getTravelCards(bot);

    // Check if we can already reach the target EXACTLY with current cards
    // (Following strategy point 3.ii - only pick cards if current cards don't enable exact travel)
    try {
      const pathWithCurrentCards = this._calculatePathToTarget(
        state,
        currentNodeId,
        targetNodeId,
        currentTravelCards
      );

      // Check if the current global event rewards a specific vehicle
      if (rewardedVehicle && pathWithCurrentCards) {
        // Even if we can reach the target exactly, check if we can do it using the rewarded vehicle
        const rewardedVehicles = Array.isArray(rewardedVehicle) ? rewardedVehicle : [rewardedVehicle];
        const usesRewardedVehicle = pathWithCurrentCards.cards.some(card => 
          rewardedVehicles.includes(card.vehicle)
        );

        if (!usesRewardedVehicle) {
          bot.logger.info(`Already have cards to reach target exactly, but none use any of the rewarded vehicles`);
          // Continue with the rest of the function to check face-up cards
        } else {
          // We already have cards that reach target exactly and use the rewarded vehicle
          const usedRewardedVehicle = pathWithCurrentCards.cards.find(card => 
            rewardedVehicles.includes(card.vehicle)
          ).vehicle;
          bot.logger.info(`Already have cards to reach target using the rewarded ${usedRewardedVehicle} vehicle, no need to pick more`);
          return null;
        }
      } else if (pathWithCurrentCards && pathWithCurrentCards.cardValue === exactDistanceNeeded) {
        // If no specific vehicle is rewarded and we can reach the target exactly, don't pick more cards
        bot.logger.info(`Already have cards to reach target ${targetNodeId} exactly (${exactDistanceNeeded} hops), no need to pick more`);
        return null;
      }
    } catch (error) {
      // If path calculation fails, continue checking for helpful cards
      bot.logger.warn(`Path calculation with current cards failed: ${error.message}`);
    }

    // Get face-up travel cards
    const faceUpTravel = state.faceUpTravel || [];
    if (faceUpTravel.length === 0) {
      return null;
    }

    // Track which face-up cards would help
    const helpfulCardIndices = [];
    const helpfulCardReason = {};

    // First priority: If there's a rewarded vehicle in face up cards, check if it can be used
    if (rewardedVehicle) {
      // Create array of rewarded vehicles
      const rewardedVehicles = Array.isArray(rewardedVehicle) ? rewardedVehicle : [rewardedVehicle];
      
      // Check face-up cards for any of the rewarded vehicles
      const rewardedVehicleIndices = [];

      for (let i = 0; i < faceUpTravel.length; i++) {
        const card = faceUpTravel[i];
        if (rewardedVehicles.includes(card.vehicle)) {
          rewardedVehicleIndices.push(i);
          bot.logger.info(`Found face-up ${card.vehicle} card at index ${i}: ${card.id} (value: ${card.value}) - rewarded by current global event`);
        }
      }

      // If we found any cards with the rewarded vehicles, check if they can be used
      if (rewardedVehicleIndices.length > 0) {
        // Try each rewarded vehicle card individually
        for (const idx of rewardedVehicleIndices) {
          const vehicleCard = faceUpTravel[idx];

          // Create a temporary array with our current cards plus this vehicle card
          const combinedCards = [...currentTravelCards, vehicleCard];

          try {
            const pathWithVehicleCard = this._calculatePathToTarget(
              state,
              currentNodeId,
              targetNodeId,
              combinedCards,
              true
            );

            if (pathWithVehicleCard) {
              // The vehicle card enables travel to the target (not necessarily exact but that's okay)
              helpfulCardIndices.push(idx);
              helpfulCardReason[idx] = `Enables travel using ${vehicleCard.vehicle} for global event reward`;
              bot.logger.info(`Face-up ${vehicleCard.vehicle} card ${vehicleCard.id} (value: ${vehicleCard.value}) enables travel to target ${targetNodeId} for global event bonus`);

              // Check if we need additional cards to reach the target exactly
              if (pathWithVehicleCard.cardValue !== exactDistanceNeeded) {
                bot.logger.info(`Need additional cards with the ${vehicleCard.vehicle} card to reach target exactly. Current distance: ${pathWithVehicleCard.cardValue}, needed: ${exactDistanceNeeded}`);

                // Try combining with other face-up cards
                for (let j = 0; j < faceUpTravel.length; j++) {
                  if (j === idx) continue; // Skip the vehicle card itself

                  const additionalCard = faceUpTravel[j];
                  const combinedWithExtra = [...currentTravelCards, vehicleCard, additionalCard];

                  try {
                    const pathWithBothCards = this._calculatePathToTarget(
                      state,
                      currentNodeId,
                      targetNodeId,
                      combinedWithExtra,
                      true
                    );

                    if (pathWithBothCards && pathWithBothCards.cardValue === exactDistanceNeeded) {
                      helpfulCardIndices.push(j);
                      helpfulCardReason[j] = `Completes exact travel with ${vehicleCard.vehicle} card for global event reward`;
                      bot.logger.info(`Face-up card ${additionalCard.id} (value: ${additionalCard.value}) combined with ${vehicleCard.vehicle} card enables EXACT travel to target ${targetNodeId}`);
                      break;
                    }
                  } catch (error) {
                    // Skip this combination if there's an error
                    continue;
                  }
                }
              }

              // If we found helpful cards including the rewarded vehicle, stop searching
              if (helpfulCardIndices.length > 0) {
                break;
              }
            }
          } catch (error) {
            bot.logger.warn(`Path calculation with ${vehicleCard.vehicle} card failed: ${error.message}`);
            continue;
          }
        }
      }
    }

    // If we didn't find any rewarded vehicle cards that help, continue with the original logic
    if (helpfulCardIndices.length === 0) {
      // Check each face-up travel card individually
      for (let i = 0; i < faceUpTravel.length; i++) {
        const potentialCard = faceUpTravel[i];
        if (!potentialCard.value) continue;

        // Create a temporary array with our current cards plus this new card
        const combinedCards = [...currentTravelCards, potentialCard];

        try {
          const pathWithNewCard = this._calculatePathToTarget(
            state,
            currentNodeId,
            targetNodeId,
            combinedCards,
            true
          );

          // Check if this card enables EXACT travel to the target
          if (pathWithNewCard && pathWithNewCard.cardValue === exactDistanceNeeded && potentialCard.value === exactDistanceNeeded) {
            helpfulCardIndices.push(i);
            helpfulCardReason[i] = `Enables exact ${exactDistanceNeeded}-hop travel with value ${potentialCard.value}`;
            bot.logger.info(`Face-up travel card ${potentialCard.id} (value: ${potentialCard.value}) enables EXACT travel to primary target ${targetNodeId}`);

            break;
          }
        } catch (error) {
          // If path calculation fails, this card doesn't help - continue to next card
          bot.logger.warn(`Path calculation with card ${potentialCard.id} failed: ${error.message}`);
          continue;
        }
      }

      // If we didn't find any single card that helps, try pairs of face-up cards (if we can pick 2)
      if (helpfulCardIndices.length === 0) {
        for (let i = 0; i < faceUpTravel.length - 1; i++) {
          const card1 = faceUpTravel[i];
          if (!card1.value) continue;

          for (let j = i + 1; j < faceUpTravel.length; j++) {
            const card2 = faceUpTravel[j];
            if (!card2.value) continue;

            // Create a temporary array with our current cards plus these two new cards
            const combinedCards = [...currentTravelCards, card1, card2];

            try {
              const pathWithNewCards = this._calculatePathToTarget(
                state,
                currentNodeId,
                targetNodeId,
                combinedCards,
                true
              );

              // Check if these cards enable EXACT travel to the target
              if (pathWithNewCards && pathWithNewCards.cardValue === exactDistanceNeeded) {
                helpfulCardIndices.push(i, j);
                helpfulCardReason[i] = `Part of pair enabling exact ${exactDistanceNeeded}-hop travel`;
                helpfulCardReason[j] = `Part of pair enabling exact ${exactDistanceNeeded}-hop travel`;
                bot.logger.info(`Face-up travel cards ${card1.id}(${card1.value}) and ${card2.id}(${card2.value}) together enable EXACT travel to primary target ${targetNodeId}`);
                break;
              }
            } catch (error) {
              // If path calculation fails, this pair doesn't help - continue to next pair
              continue;
            }
          }

          if (helpfulCardIndices.length > 0) {
            break; // We found a helpful pair, no need to check more
          }
        }
      }
    }

    // If we found helpful cards, create a card picking action
    if (helpfulCardIndices.length > 0) {
      const cardDetails = helpfulCardIndices.map(idx => {
        const card = faceUpTravel[idx];
        return `${card.id}(${card.value}): ${helpfulCardReason[idx] || 'Enables exact travel'}`;
      }).join(', ');

      if (isSpecialCase) {
        bot.logger.info(`SPECIAL STRATEGY: Found ${helpfulCardIndices.length} travel cards that enable exact ${exactDistanceNeeded}-step journey to target at ${targetNodeId}`);
        bot.logger.info(`SPECIAL STRATEGY: Cards to pick: ${cardDetails}`);
      } else {
        bot.logger.info(`Found ${helpfulCardIndices.length} helpful travel cards to pick for EXACT travel: ${cardDetails}`);
      }

      // Get the actual card IDs from the indices
      const cardIds = helpfulCardIndices.map(idx => faceUpTravel[idx].id);

      // Calculate current hand size
      const currentHandSize = state.player.hand ? state.player.hand.length : 0;

      // Calculate the hand size after movement (accounting for cards that will be used)
      const cardsUsedCount = cardsUsedForMovement.length;
      const handSizeAfterMovement = Math.max(0, currentHandSize - cardsUsedCount);

      bot.logger.info(`Hand size calculation: current=${currentHandSize}, cards used for movement=${cardsUsedCount}, after movement=${handSizeAfterMovement}`);

      // Use the hand size after movement to determine if we can pick from deck
      const canPickFromDeck = handSizeAfterMovement + cardIds.length < 4 && cardIds.length < 2;

      // Always try to pick 2 cards if possible
      if (cardIds.length === 1 && faceUpTravel.length > 1 && handSizeAfterMovement < 3) {
        // We only found 1 helpful card, but we should pick 2 if possible

        // Get the next turn target
        const nextTurnTarget = this._getNextTurnTarget(bot);

        if (nextTurnTarget) {
          // We have a next turn target, so try to find a card that would help reach it
          bot.logger.info(`Finding second travel card suitable for next turn target at ${nextTurnTarget.locationId}`);

          // Calculate the exact distance needed to reach the next turn target
          // We need to use the target node as our starting position since we'll be there after using the first card
          const exactDistanceToNextTarget = this._estimateTravelDistance(state, targetNodeId, nextTurnTarget.locationId);
          bot.logger.info(`Distance to next turn target: ${exactDistanceToNextTarget} hops`);

          let bestSecondCardIndex = -1;
          let bestSecondCardMatch = -1;  // How close the card value is to the exact distance needed

          for (let i = 0; i < faceUpTravel.length; i++) {
            // Skip the card we already selected
            if (helpfulCardIndices.includes(i)) continue;

            const secondCard = faceUpTravel[i];
            if (!secondCard.value) continue;

            // Calculate how well this card matches the distance needed
            // A value of 0 means exact match, lower is better
            const distanceMatch = Math.abs(secondCard.value - exactDistanceToNextTarget);

            // If this is a better match than what we've seen so far, or it's the first valid card
            if (bestSecondCardIndex === -1 || distanceMatch < bestSecondCardMatch) {
              bestSecondCardMatch = distanceMatch;
              bestSecondCardIndex = i;
            }

            // If we found an exact match, stop looking
            if (distanceMatch === 0) {
              break;
            }
          }

          if (bestSecondCardIndex >= 0) {
            cardIds.push(faceUpTravel[bestSecondCardIndex].id);
            bot.logger.info(`Also picking second travel card ${faceUpTravel[bestSecondCardIndex].id} (value: ${faceUpTravel[bestSecondCardIndex].value}) suitable for next turn target (distance: ${exactDistanceToNextTarget})`);
          }
        } else {
          // No next turn target, fall back to maximizing value
          let bestSecondCardIndex = -1;
          let bestSecondCardValue = -1;

          for (let i = 0; i < faceUpTravel.length; i++) {
            // Skip the card we already selected
            if (helpfulCardIndices.includes(i)) continue;

            const secondCard = faceUpTravel[i];
            if (secondCard.value > bestSecondCardValue) {
              bestSecondCardValue = secondCard.value;
              bestSecondCardIndex = i;
            }
          }

          if (bestSecondCardIndex >= 0) {
            cardIds.push(faceUpTravel[bestSecondCardIndex].id);
            bot.logger.info(`Also picking second travel card ${faceUpTravel[bestSecondCardIndex].id} (value: ${faceUpTravel[bestSecondCardIndex].value}) to maximize card collection`);
          }
        }
      }

      bot.logger.info(`Bot ${bot.name} picking ${cardIds.length} helpful travel cards${canPickFromDeck ? ' and will also pick from deck' : ''}`);

      // Set the state flags for collecting cards
      this._turnState.hasCollectedCards = true;
      this._turnState.hasCollectedTravelCards = true;

      return {
        action: 'pickCards',
        params: {
          type: 'travel',
          pickFromFaceUp: cardIds,
          pickFromTop: canPickFromDeck // Also pick from deck if we have room for more cards
        }
      };
    }

    return null; // No helpful cards found
  }

  /**
   * Extract the rewarded vehicle type from a global event
   * @param {Object} globalEvent - The global event object
   * @returns {string|null} - The vehicle type or null if no vehicle is rewarded
   * @private
   */
  _getRewardedVehicleFromGlobalEvent(globalEvent) {
    console.log(`Getting rewarded vehicle from global event: ${JSON.stringify(globalEvent)}`);
    if (!globalEvent || !globalEvent.effect) return null;

    // Map of global event effects to vehicle types
    const vehicleRewardMap = {
      // Original individual rewards
      'pedal_power_reward': 'cycle',
      'footpath_reverie_reward': 'trek',
      'steed_of_valor_reward': 'horse',
      'desert_caravan_reward': 'camel',
      'biker_gang_reward': 'motorbike',
      'rickshaw_rhapsody_reward': 'rickshaw',
      'top_gear_reward': 'car',
      'hop_on_hop_off_reward': 'bus',
      'bullet_train_reward': 'train',
      'scenic_cruise_reward': 'boat',
      'heavy_haul_reward': 'truck',
      'up_and_over_reward': 'helicopter',
      
      // New consolidated rewards
      'eco_trail_reward': ['cycle', 'trek'],  // Both cycle and trek
      'rajput_caravans_reward': ['horse', 'camel'],  // Both horse and camel
      'urban_ride_reward': ['motorbike', 'rickshaw'],  // Both motorbike and rickshaw
      'road_warriors_reward': ['car', 'bus'],  // Both car and bus
      'rails_and_sails_reward': ['train', 'boat']  // Both train and boat
    };

    const reward = vehicleRewardMap[globalEvent.effect];
    
    // If reward is an array, we need to check which vehicle the bot has
    if (Array.isArray(reward)) {
      // This will be handled at a higher level, where we'll check if the bot has any of these vehicles
      return reward;
    }
    
    return reward || null;
  }

  /**
   * Get travel cards from the bot's hand
   * @param {Bot} bot - The bot instance
   * @param {Object} [overrideState] - Optional state override
   * @returns {Array} Array of travel cards
   * @private
   */
  _getTravelCards(bot, overrideState) {
    const state = overrideState || bot.getPlayerState();
    if (!state || !state.player || !Array.isArray(state.player.hand)) {
      return [];
    }

    // Get all travel cards from the hand
    const travelCards = state.player.hand.filter(card =>
      card && (card.type === 'travel' || card.cardType === 'travel')
    );

    // Compare with the actual IDs in player's hand
    const playerCardIds = new Set(state.player.hand.map(card => card.id));
    const validTravelCards = travelCards.filter(card => playerCardIds.has(card.id));

    // If there's a discrepancy, log it
    if (validTravelCards.length < travelCards.length) {
      console.log(`Warning in _getTravelCards: Found cards not in player's hand. Original: ${travelCards.map(c => c.id).join(',')}, Valid: ${validTravelCards.map(c => c.id).join(',')}`);
      return validTravelCards;
    }

    return validTravelCards; // Changed from travelCards to validTravelCards to ensure only valid cards are returned
  }

  /**
   * Format a path for logging
   * @param {Array<number|string>} path - Path of node IDs
   * @returns {string} Formatted path
   * @private
   */
  _formatPath(path) {
    if (!path || !Array.isArray(path)) return 'invalid path';

    return path.join(' -> ');
  }

  /**
   * Check if the bot can collect the current target journey card
   * @param {Bot} bot - The bot
   * @returns {boolean} - True if the bot can collect the journey
   * @private
   */
  _canCollectCurrentTargetJourney(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !this._currentTarget) {
      console.log(`_canCollectCurrentTargetJourney: missing state or target`);
      return false;
    }

    const player = state.player;
    const target = this._currentTarget;

    // Check if the bot is at the right location
    // Get player position, preferring position over currentNodeId
    const playerPosition = player.position || player.currentNodeId;

    // Check if player is at journey card location
    if (playerPosition !== target.locationId) {
      console.log(`_canCollectCurrentTargetJourney: player not at target location`);
      return false;
    }

    // Check if the bot has enough OM tokens
    const isInnerJourney = target.cardType === 'inner';
    const collectedJourneys = player.collectedJourneys || [];
    const innerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length;
    const outerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;

    bot.logger.info(`Checking journey collection requirements: inner=${innerJourneyCount}, outer=${outerJourneyCount}, isInnerJourney=${isInnerJourney}`);

    // Calculate the required OM tokens using the helper function
    const requiredOmTokens = calculateOmCostForNextCard(isInnerJourney ? innerJourneyCount : outerJourneyCount, state.numPlayers);
    // Use the top-level function to ensure consistent Om temp counting
    const currentOmTemp = getOmTempCount(player);
    
    bot.logger.info(`Journey collection Om check: have ${currentOmTemp}, need ${requiredOmTokens}`);

    if (currentOmTemp < requiredOmTokens) {
      bot.logger.info(`Cannot collect journey: insufficient Om tokens (have ${currentOmTemp}, need ${requiredOmTokens})`);
      return false;
    }

    // Check if the bot has enough energy cubes
    const requiredCubes = target.requiredCubes || {};

    // Count player cubes by type
    const playerCubesByType = { bhakti: 0, gnana: 0, karma: 0, artha: 0 };

    // Handle both array and object formats for energy cubes
    if (Array.isArray(player.energyCubes)) {
      // If energyCubes is an array of strings (e.g., ['karma', 'bhakti'])
      player.energyCubes.forEach(cube => {
        if (playerCubesByType[cube] !== undefined) {
          playerCubesByType[cube]++;
        }
      });
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      // If energyCubes is an object (e.g., {bhakti: 1, karma: 1})
      Object.assign(playerCubesByType, player.energyCubes);
    }

    // Log detailed cube requirements and available cubes
    bot.logger.info(`------- JOURNEY CARD COLLECTION CHECK -------`);
    bot.logger.info(`Bot ${bot.name} checking journey requirements for ${target.id} at location ${target.locationId}`);
    bot.logger.info(`Journey type: ${target.cardType}, points: ${isInnerJourney ? target.reward.inner : target.reward.outer}`);
    bot.logger.info(`Required cubes: ${JSON.stringify(requiredCubes)}`);
    bot.logger.info(`Available cubes: ${JSON.stringify(playerCubesByType)}`);
    bot.logger.info(`Raw player.energyCubes: ${JSON.stringify(player.energyCubes)}`);

    // Check each required cube type
    let canAfford = true;
    for (const [type, amount] of Object.entries(requiredCubes)) {
      const playerAmount = playerCubesByType[type] || 0;
      const hasEnough = playerAmount >= amount;
      bot.logger.info(`${type.toUpperCase()} check: need ${amount}, have ${playerAmount}, sufficient: ${hasEnough}`);
      
      if (!hasEnough) {
        canAfford = false;
        bot.logger.info(`Cannot collect journey: insufficient ${type} cubes (have ${playerAmount}, need ${amount})`);
      }
    }
    
    bot.logger.info(`Final collection check result: ${canAfford ? 'CAN collect' : 'CANNOT collect'}`);
    bot.logger.info(`-------------------------------------------`);

    return canAfford;
  }

  /**
   * Get adjacent nodes to the given node
   * @param {Object} state - The game state
   * @param {string|number} nodeId - Node ID
   * @returns {Array} - Array of adjacent nodes
   * @private
   */
  _getAdjacentNodes(state, nodeId) {
    if (!state || !state.edges || !nodeId) {
      return [];
    }

    // Convert nodeId to number for the PathCalculator
    const currentId = parseInt(nodeId);

    // Use PathCalculator's adjacency map directly for better performance
    const adjacentNodeIds = this.pathCalculator.adjacencyMap.get(currentId);

    if (!adjacentNodeIds) {
      return [];
    }

    // Convert Set to Array
    const adjacentIds = Array.from(adjacentNodeIds);

    // Get full node objects from state
    return state.locations.filter(location =>
      location && adjacentIds.includes(parseInt(location.id))
    );
  }

  /**
   * Calculate resource needs for the bot
   * @param {Bot} bot - The bot
   * @returns {Promise<object>} - Resource needs assessment
   * @private
   */
  async _calculateResourceNeed(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player) return { needsOmTokens: false, needsEnergyCubes: false };

    const player = state.player;
    // Use the fixed getTotalOmCount which will use our fixed getOmTempCount
    const currentOmCount = getTotalOmCount(player);
    
    // Determine if the bot needs to improve Om track position
    this._updateOmTrackStrategy(bot, state);
    
    // Also check if we need Om tokens for a journey card
    let needOmForJourney = false;
    let requiredOmForJourney = 0;
    
    if (this._currentTarget) {
      const isInnerJourney = this._currentTarget.cardType === 'inner';
      const collectedJourneys = player.collectedJourneys || [];
      const innerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length;
      const outerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;
      requiredOmForJourney = calculateOmCostForNextCard(isInnerJourney ? innerJourneyCount : outerJourneyCount, state.numPlayers);
      const currentOmTemp = getOmTempCount(player);
      needOmForJourney = currentOmTemp < requiredOmForJourney;
    }

    // Check if we need Om tokens for journey cards or Om track advancement
    const needsOmTokens = 
        (this._omTrackStrategy.shouldImprovePosition && 
         currentOmCount < this._omTrackStrategy.minimumRequiredOmTokens) || 
        needOmForJourney;
        
    // Add logging for Om token counts
    bot.logger.info(`Resource needs calculation: current Om count=${currentOmCount}, need for track=${this._omTrackStrategy.minimumRequiredOmTokens}, need for journey=${requiredOmForJourney}, needsOmTokens=${needsOmTokens}`);

    // Find missing energy cubes
    let missingCubes = {};
    let totalMissingCubes = 0;

    // Check for target journey card requirements
    if (this._currentTarget && this._currentTarget.requiredCubes) {
      const reqCubes = this._currentTarget.requiredCubes;
      // Count player cubes by type
      const playerCubesByType = { bhakti: 0, gnana: 0, karma: 0, artha: 0 };

      // Handle both array and object formats for energy cubes
      if (Array.isArray(player.energyCubes)) {
        // If energyCubes is an array of strings (e.g., ['karma', 'bhakti'])
        player.energyCubes.forEach(cube => {
          if (playerCubesByType[cube] !== undefined) {
            playerCubesByType[cube]++;
          }
        });
      } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
        // If energyCubes is an object (e.g., {bhakti: 1, karma: 1})
        Object.assign(playerCubesByType, player.energyCubes);
      }

      // For each type of cube, calculate how many we're missing
      ['bhakti', 'gnana', 'karma', 'artha'].forEach(type => {
        if (reqCubes[type] && reqCubes[type] > playerCubesByType[type]) {
          missingCubes[type] = reqCubes[type] - playerCubesByType[type];
          totalMissingCubes += missingCubes[type];
        }
      });
    }

    // Log missing cubes
    bot.logger.info(`Missing energy cubes: ${JSON.stringify(missingCubes)}`);

    const needsEnergyCubes = totalMissingCubes > 0;

    return {
      needsOmTokens,
      needsEnergyCubes,
      missingCubes,
      totalMissingCubes
    };
  }
  
  /**
   * Update the Om track strategy based on current game state
   * @param {Bot} bot - The bot instance
   * @param {object} state - Current game state
   * @private
   */
  _updateOmTrackStrategy(bot, state) {
    if (!state || !state.player) return;
    
    const player = state.player;
    const currentOmCount = getTotalOmCount(player);
    const currentOmSpace = bot.omSpace || 0;
    
    // Check if we're lagging behind other players by 2 or more spaces
    let shouldImprovePosition = false;
    let maxOpponentOmSpace = 0;
    
    // Find the highest Om space among opponents
    if (state.opponents && state.opponents.length > 0) {
      for (const opponent of state.opponents) {
        // Try to get opponent's Om space - this could be directly on the opponent 
        // or in the omTrack data structure depending on implementation
        let opponentOmSpace = 0;
        
        // First try to get from opponent directly if it has omSpace property
        if (opponent.omSpace !== undefined) {
          opponentOmSpace = opponent.omSpace;
        }
        // Fall back to searching in omTrack if available
        else if (state.omTrack) {
          // Search through omTrack to find opponent position
          for (let space = state.omTrack.length - 1; space >= 0; space--) {
            const stack = state.omTrack[space] || [];
            if (stack.includes(opponent.id)) {
              opponentOmSpace = space;
              break;
            }
          }
        }
        
        // Update the max opponent Om space
        if (opponentOmSpace > maxOpponentOmSpace) {
          maxOpponentOmSpace = opponentOmSpace;
        }
      }
      
      // Only improve position if lagging by 2 or more spaces
      shouldImprovePosition = (maxOpponentOmSpace - currentOmSpace) >= 2 && currentOmSpace < 7;
      
      bot.logger.info(`Om track comparison: bot=${currentOmSpace}, max_opponent=${maxOpponentOmSpace}, lag=${maxOpponentOmSpace - currentOmSpace}`);
    } else {
      // If no opponents data available, fall back to simple token count check
      // Only improve position if the bot has less than 1 Om token
      shouldImprovePosition = currentOmCount < 1;
    }
    
    // Calculate minimum Om tokens needed for next Om space
    // This is an estimation - actual game logic might vary
    const minimumRequiredOmTokens = currentOmSpace + 1;
    
    // Set the target Om space (aim for next level or higher if we have enough tokens)
    const targetOmSpace = Math.min(currentOmSpace + Math.floor(currentOmCount / minimumRequiredOmTokens), 7);
    
    this._omTrackStrategy = {
      shouldImprovePosition,
      targetOmSpace,
      minimumRequiredOmTokens
    };
    
    // Add more detailed logging about the Om track decision
    if (shouldImprovePosition) {
      if (state.opponents && state.opponents.length > 0) {
        bot.logger.info(`Om Track Strategy: IMPROVING position because lagging by ${maxOpponentOmSpace - currentOmSpace} spaces behind max opponent (${maxOpponentOmSpace})`);
      } else {
        bot.logger.info(`Om Track Strategy: IMPROVING position because bot has less than 1 Om token (${currentOmCount})`);
      }
    } else {
      if (state.opponents && state.opponents.length > 0) {
        bot.logger.info(`Om Track Strategy: NOT improving position because only lagging by ${maxOpponentOmSpace - currentOmSpace} spaces (< 2) behind max opponent (${maxOpponentOmSpace})`);
      } else {
        bot.logger.info(`Om Track Strategy: NOT improving position because bot has ${currentOmCount} Om tokens (>= 1)`);
      }
    }
    
    bot.logger.info(`Updated Om track strategy: shouldImprove=${shouldImprovePosition}, target=${targetOmSpace}, required tokens=${minimumRequiredOmTokens}, current tokens=${currentOmCount}`);
  }

  /**
   * Get the target for the next turn based on current targets and travel history
   * Implements strategy point 6.ii:
   * - If traveled during step 3 (OM token collection), target energy cube location next turn
   * - If traveled during step 4 (energy cube collection), target journey card location next turn
   * @param {Bot} bot - The bot instance
   * @returns {Object|null} - The next turn target location or null
   * @private
   */
  _getNextTurnTarget(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player) {
      bot.logger.info(`No state or player found, no next target`);
      return null;
    }

    const currentNodeId = state.player.currentNodeId || state.player.position;

    // Check if we moved for OM tokens this turn (step 3)
    // We consider this true if we needed OM tokens at the start of the turn
    if (this._resourcePlan && this._resourcePlan.needsOmTokens) {
      bot.logger.info(`Bot ${bot.name} traveled during OM token collection phase, next turn will target energy location`);

      // Next turn target should be energy cube location
      if (this._resourcePlan.needsEnergyCubes) {
        // Find all energy locations with cubes we need
        const missingCubes = this._resourcePlan.missingCubes || {};
        const energyLocations = this._findAllEnergyLocations(state, state.player, bot, missingCubes);

        if (energyLocations && energyLocations.length > 0) {
          // Find closest energy location
          let bestLocation = null;
          let minDistance = Infinity;

          for (const location of energyLocations) {
            const distance = this._estimateTravelDistance(state, currentNodeId, location.id);
            if (distance < minDistance) {
              minDistance = distance;
              bestLocation = location;
            }
          }

          if (bestLocation) {
            bot.logger.info(`Next turn target: energy location at ${bestLocation.id}`);
            return {
              type: 'energy',
              locationId: bestLocation.id
            };
          }
        }
      }
      // If we don't need energy cubes, target the journey card directly
      else if (this._currentTarget && this._currentTarget.locationId) {
        bot.logger.info(`Next turn target: journey card at ${this._currentTarget.locationId} (no energy cubes needed)`);
        return {
          type: 'journey',
          locationId: this._currentTarget.locationId
        };
      }
    }
    // Check if we moved for energy cubes this turn (step 4)
    // We consider this true if we needed energy cubes at the start of the turn
    else if (this._resourcePlan && this._resourcePlan.needsEnergyCubes) {
      bot.logger.info(`Bot ${bot.name} traveled during energy cube collection phase, next turn will target journey card`);

      // Next turn target should be journey card location
      if (this._currentTarget && this._currentTarget.locationId) {
        bot.logger.info(`Next turn target: journey card at ${this._currentTarget.locationId}`);
        return {
          type: 'journey',
          locationId: this._currentTarget.locationId
        };
      }
    }
    // If we've collected a journey card this turn, target resources for the next journey card
    else if (this._turnState.justPickedCards && this._turnState.pickedCardType === 'journey') {
      bot.logger.info(`Bot ${bot.name} collected a journey card this turn, targeting resources for next journey`);

      // Check if we need OM tokens first
      if (this._resourcePlan && this._resourcePlan.needsOmTokens) {
        // Find closest jyotirlinga with OM tokens
        const jyotirlingaIds = this._getJyotirlingas(state);
        if (jyotirlingaIds && jyotirlingaIds.length > 0) {
          let closestJyotirlinga = null;
          let shortestDistance = Infinity;

          for (const jId of jyotirlingaIds) {
            const distance = this._estimateTravelDistance(state, currentNodeId, jId.id);
            if (distance < shortestDistance) {
              shortestDistance = distance;
              closestJyotirlinga = jId;
            }
          }

          if (closestJyotirlinga) {
            bot.logger.info(`Next turn target: OM token at jyotirlinga ${closestJyotirlinga.id}`);
            return {
              type: 'jyotirlinga',
              locationId: closestJyotirlinga.id
            };
          }
        }
      }
      // If we don't need OM tokens, check if we need energy cubes
      else if (this._resourcePlan && this._resourcePlan.needsEnergyCubes) {
        const missingCubes = this._resourcePlan.missingCubes || {};
        const energyLocations = this._findAllEnergyLocations(state, state.player, bot, missingCubes);

        if (energyLocations && energyLocations.length > 0) {
          const bestLocation = energyLocations[0]; // Already sorted by distance
          bot.logger.info(`Next turn target: energy location at ${bestLocation.id}`);
          return {
            type: 'energy',
            locationId: bestLocation.id
          };
        }
      }
    }

    // If we didn't travel for resources or can't determine next target
    // First check if we have all resources needed for journey card collection
    if (this._resourcePlan && !this._resourcePlan.needsOmTokens && !this._resourcePlan.needsEnergyCubes
        && this._currentTarget && this._currentTarget.locationId) {
      bot.logger.info(`Bot ${bot.name} has all resources (OM tokens and energy cubes) needed for journey card collection`);
      bot.logger.info(`Next turn target: journey card at ${this._currentTarget.locationId}`);
      return {
        type: 'journey',
        locationId: this._currentTarget.locationId
      };
    } else {
      bot.logger.info(`currentTarget: ${JSON.stringify(this._currentTarget)}`);
    }

    // If we didn't travel for resources or can't determine next target
    bot.logger.info("No next turn target found");
    return null;
  }

  /**
   * Check if the player has Artha energy cubes
   * @param {Object} state - The player state
   * @returns {boolean} - True if the player has at least one Artha cube
   * @private
   */
  _hasArthaCube(state) {
    if (!state || !state.player || !state.player.energyCubes) return false;

    // Handle both object and array formats for energy cubes
    if (typeof state.player.energyCubes === 'object' && !Array.isArray(state.player.energyCubes)) {
      return (state.player.energyCubes.artha || 0) > 0;
    } else if (Array.isArray(state.player.energyCubes)) {
      return state.player.energyCubes.includes('artha');
    }

    return false;
  }

  /**
   * Get the current global event - tries different methods to access it
   * @param {Bot} bot - The bot
   * @returns {Object|null} - The global event or null if not found
   * @private
   */
  _getCurrentGlobalEvent(bot) {
    // Get the player state
    const state = bot.getPlayerState();
    if (!state) return null;

    // Try to get the global event from various properties
    if (state.currentGlobalEvent) {
      return state.currentGlobalEvent;
    }

    if (state.globalEvent) {
      return state.globalEvent;
    }

    if (state.activeGlobalEvent) {
      return state.activeGlobalEvent;
    }

    // If the bot has access to the full game state, try to get it from there
    if (bot.gameState && bot.gameState.currentGlobalEvent) {
      return bot.gameState.currentGlobalEvent;
    }

    // Check if there's a getState method on the game state
    if (bot.gameState && typeof bot.gameState.getState === 'function') {
      const fullState = bot.gameState.getState();
      if (fullState && fullState.currentGlobalEvent) {
        return fullState.currentGlobalEvent;
      }
    }

    // No global event found
    return null;
  }

  /**
   * Count total energy cubes a player has
   * @param {Object} player - The player object
   * @returns {number} - Total number of energy cubes
   * @private
   */
  _countEnergyCubes(player) {
    if (!player || !player.energyCubes) {
      return 0;
    }

    // Handle both array and object formats for energy cubes
    if (Array.isArray(player.energyCubes)) {
      return player.energyCubes.length;
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      return Object.values(player.energyCubes).reduce((sum, count) => sum + count, 0);
    }

    return 0;
  }

  /**
   * Check if the bot has the resources (energy cubes and Om tokens) needed to collect 
   * the current target journey card, but WITHOUT checking location
   * @param {Bot} bot - The bot
   * @returns {boolean} - True if the bot has all required resources
   * @private
   */
  _hasResourcesForTargetJourney(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !this._currentTarget) {
      console.log(`_hasResourcesForTargetJourney: missing state or target`);
      return false;
    }

    const player = state.player;
    const target = this._currentTarget;

    // Check if the bot has enough OM tokens
    const isInnerJourney = target.cardType === 'inner';
    const collectedJourneys = player.collectedJourneys || [];
    const innerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.inner !== undefined).length;
    const outerJourneyCount = collectedJourneys.filter(journey => journey.reward && journey.reward.outer !== undefined).length;

    bot.logger.info(`Checking journey resource requirements: inner=${innerJourneyCount}, outer=${outerJourneyCount}, isInnerJourney=${isInnerJourney}`);

    // Calculate the required OM tokens using the helper function
    const requiredOmTokens = calculateOmCostForNextCard(isInnerJourney ? innerJourneyCount : outerJourneyCount, state.numPlayers);
    // Use the top-level function to ensure consistent Om temp counting
    const currentOmTemp = getOmTempCount(player);
    
    bot.logger.info(`Journey collection Om check: have ${currentOmTemp}, need ${requiredOmTokens}`);

    if (currentOmTemp < requiredOmTokens) {
      bot.logger.info(`Cannot collect journey: insufficient Om tokens (have ${currentOmTemp}, need ${requiredOmTokens})`);
      return false;
    }

    // Check if the bot has enough energy cubes
    const requiredCubes = target.requiredCubes || {};

    // Count player cubes by type
    const playerCubesByType = { bhakti: 0, gnana: 0, karma: 0, artha: 0 };

    // Handle both array and object formats for energy cubes
    if (Array.isArray(player.energyCubes)) {
      // If energyCubes is an array of strings (e.g., ['karma', 'bhakti'])
      player.energyCubes.forEach(cube => {
        if (playerCubesByType[cube] !== undefined) {
          playerCubesByType[cube]++;
        }
      });
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      // If energyCubes is an object (e.g., {bhakti: 1, karma: 1})
      Object.assign(playerCubesByType, player.energyCubes);
    }

    // Log detailed cube requirements and available cubes
    bot.logger.info(`------- JOURNEY CARD RESOURCE CHECK -------`);
    bot.logger.info(`Bot ${bot.name} checking journey resources for ${target.id} at location ${target.locationId}`);
    bot.logger.info(`Journey type: ${target.cardType}, points: ${isInnerJourney ? target.reward?.inner : target.reward?.outer}`);
    bot.logger.info(`Required cubes: ${JSON.stringify(requiredCubes)}`);
    bot.logger.info(`Available cubes: ${JSON.stringify(playerCubesByType)}`);

    // Check each required cube type
    let canAfford = true;
    for (const [type, amount] of Object.entries(requiredCubes)) {
      const playerAmount = playerCubesByType[type] || 0;
      const hasEnough = playerAmount >= amount;
      bot.logger.info(`${type.toUpperCase()} check: need ${amount}, have ${playerAmount}, sufficient: ${hasEnough}`);
      
      if (!hasEnough) {
        canAfford = false;
        bot.logger.info(`Cannot collect journey: insufficient ${type} cubes (have ${playerAmount}, need ${amount})`);
      }
    }
    
    bot.logger.info(`Final resource check result: ${canAfford ? 'HAS all resources' : 'MISSING resources'}`);
    bot.logger.info(`-------------------------------------------`);

    return canAfford;
  }
}

module.exports = DefensiveStrategy;