/**
 * RandomStrategy.js
 * A simple strategy that makes completely random decisions.
 * This serves as both an example strategy implementation and a baseline for comparison.
 * 
 * !!! NOTICE: This strategy will be deprecated soon. 
 * !!! It has been configured to hard-crash when issues are encountered to alert of any problems.
 */

const BaseStrategy = require('./BaseStrategy');
const PathCalculator = require('../PathCalculator');

class RandomStrategy extends BaseStrategy {
  /**
   * Create a new random strategy
   * @param {object} config - Strategy configuration
   */
  constructor(config = {}) {
    super({
      name: 'random',
      config
    });
    
    this.pathCalculator = new PathCalculator();
    
    // Track failed attempts to avoid infinite loops
    this._failedAttempts = 0;
    
    // Memory management for move history
    this.moveHistoryLimit = config.moveHistoryLimit || 50;
    
    // Track turn state
    this._turnState = {
      hasMoved: false,
      hasCollectedCards: false,
      justPickedCards: false,
      pickedCardType: null,
      turnNumber: 0
    };
  }
  
  /**
   * Plan the next turn for a bot
   * @param {Bot} bot - The bot this strategy is planning for
   * @returns {Promise<object>} Action plan with 'action' and 'params'
   */
  async planTurn(bot) {
    try {
      // Reset failed attempts counter at the start of each turn
      this._failedAttempts = 0;
      
      const state = bot.getPlayerState();
      if (!state || !state.player) {
        const errorMsg = `Invalid player state for bot ${bot.name}`;
        bot.logger.error(errorMsg);
        throw new Error(errorMsg);
      }
      
      if (!state.isMyTurn) {
        return null;
      }
      
      // Check if this is a new turn
      const isNewTurn = bot.turnsPlayed > this._turnState.turnNumber;
      if (isNewTurn) {
        // Log hand state at the beginning of each turn for debugging
        this._logHandState(bot);
        
        // Compare to last known state if we have one - this helps detect card disappearance
        if (this._previousHandState) {
          const prevTravelCount = this._previousHandState.travelCount || 0;
          const currentTravelCount = this._countTravelCards(bot);
          
          if (prevTravelCount > 0 && currentTravelCount === 0 && !this._previousHandState.usedForMove) {
            const errorMsg = `CRITICAL: Bot ${bot.name} had ${prevTravelCount} travel cards last turn but now has 0 without using them for movement. Card loss detected.`;
            bot.logger.error(errorMsg);
            throw new Error(errorMsg);
          }
        }
        
        // Reset turn state for the new turn
        this._turnState = {
          hasMoved: false,
          hasCollectedCards: false,
          justPickedCards: false,
          pickedCardType: null,
          turnNumber: bot.turnsPlayed
        };
      }
      
      // Track previous hand state for next turn
      this._previousHandState = {
        handSize: Array.isArray(state.player.hand) ? state.player.hand.length : 0,
        travelCount: this._countTravelCards(bot),
        usedForMove: false
      };
      
      // Check if the bot has travel cards
      const hasTravelCards = this._hasTravelCards(bot);
      const handSize = Array.isArray(state.player.hand) ? state.player.hand.length : 0;
      
      // Debug logging for each turn
      if (bot.turnsPlayed % 3 === 0 || !hasTravelCards) {
        this._logHandState(bot);
      }
      
      // If we already moved and collected cards in this turn, end the turn
      if (this._turnState.hasMoved && this._turnState.hasCollectedCards) {
        return { action: 'endTurn', params: {} };
      }
      
      // If the bot has no travel cards and hasn't collected cards yet, prioritize picking travel cards
      if (!hasTravelCards && !this._turnState.hasCollectedCards) {
        // Check if hand is full - this should not happen with our improved strategy
        // but handle it gracefully just in case
        if (handSize >= 4) {
          // Log the full hand contents for debugging
          const handCards = Array.isArray(state.player.hand) ? state.player.hand : [];
          const cardTypes = handCards.map(card => {
            if (!card) return 'null';
            return `${card.type}${card.type === 'travel' ? ':' + card.value : ''}`;
          }).join(', ');
          
          const errorMsg = `CRITICAL: Bot ${bot.name} has a full hand with no travel cards. Hand contains: [${cardTypes}]`;
          bot.logger.error(errorMsg);
          throw new Error(errorMsg);
        } else {
          // DETAILED PRE-PICK LOGGING
          const preFaceUpTravel = Array.isArray(state.faceUpTravel) ? state.faceUpTravel.slice() : [];
          bot.logger.info(`Bot ${bot.name} pre-pick state: Hand=${handSize} cards, Face-up travel cards: ${JSON.stringify(preFaceUpTravel.map(c => c.id || c))}`);
          
          // Try to pick travel cards - this should ALWAYS work unless hand is full
          // since we can draw from the deck even if no face-up cards are available
          const pickAction = {
            action: 'pickCards',
            params: {
              type: 'travel',
              pickFromFaceUp: []
            }
          };
          
          bot.logger.info(`Bot ${bot.name} has no travel cards with ${handSize} cards in hand. Picking travel cards from DECK.`);
          
          // Always pick from the deck if we have no travel cards - guaranteed to work
          this._turnState.hasCollectedCards = true;
          this._turnState.justPickedCards = true;
          this._turnState.pickedCardType = 'travel';
          
          // Record this action
          this.recordMove({
            type: pickAction.action,
            params: this._minimizeParams(pickAction.params)
          });
          
          // Set flag to check result of this pick in next turn
          this._justPickedTravelCard = true;
          
          return pickAction;
        }
      }
      
      // If we just picked travel cards in the last action, try to move now
      if (this._turnState.justPickedCards && this._turnState.pickedCardType === 'travel' && !this._turnState.hasMoved) {
        // Reset the "just picked" flag
        this._turnState.justPickedCards = false;
        
        // Check if we actually got travel cards
        if (!hasTravelCards) {
          bot.logger.warn(`Bot ${bot.name} just picked travel cards but still has none. Possible server issue.`);
        }
        
        // Try to move using the newly acquired cards
        const moveAction = await this.analyzeMoves(bot);
        if (moveAction && moveAction.action) {
          this._turnState.hasMoved = true;
          
          // Record this action
          this.recordMove({
            type: moveAction.action,
            params: this._minimizeParams(moveAction.params)
          });
          
          // Mark that we used travel cards for movement
          this._previousHandState.usedForMove = true;
          
          return moveAction;
        }
      }
      
      // Decide randomly whether to prioritize movement or collection if we haven't done both
      const moveFirst = Math.random() < 0.5;
      
      // If we haven't moved yet and we prioritize movement (or have already collected cards)
      if (!this._turnState.hasMoved && (moveFirst || this._turnState.hasCollectedCards) && hasTravelCards) {
        const moveAction = await this.analyzeMoves(bot);
        if (moveAction && moveAction.action) {
          this._turnState.hasMoved = true;
          
          // Record this action
          this.recordMove({
            type: moveAction.action,
            params: this._minimizeParams(moveAction.params)
          });
          
          return moveAction;
        }
      }
      
      // If we haven't collected cards yet and we prioritize collection (or already moved)
      if (!this._turnState.hasCollectedCards && (!moveFirst || this._turnState.hasMoved)) {
        // Randomly decide which type of card to collect
        const cardTypeToCollect = this._chooseRandomCardType(bot);
        let collectionAction;
        
        if (cardTypeToCollect === 'journey') {
          collectionAction = await this.evaluateJourneyCollection(bot);
        } else {
          // Travel or event
          collectionAction = await this.evaluateCardPicks(bot, cardTypeToCollect);
        }
        
        if (collectionAction && collectionAction.action) {
          this._turnState.hasCollectedCards = true;
          
          if (cardTypeToCollect === 'travel') {
            this._turnState.justPickedCards = true;
            this._turnState.pickedCardType = 'travel';
          }
          
          // Record this action
          this.recordMove({
            type: collectionAction.action,
            params: this._minimizeParams(collectionAction.params)
          });
          
          return collectionAction;
        }
      }
      
      // If we've already collected cards but not moved, and we have travel cards, try to move
      if (this._turnState.hasCollectedCards && !this._turnState.hasMoved && hasTravelCards) {
        const moveAction = await this.analyzeMoves(bot);
        if (moveAction && moveAction.action) {
          this._turnState.hasMoved = true;
          
          // Record this action
          this.recordMove({
            type: moveAction.action,
            params: this._minimizeParams(moveAction.params)
          });
          
          return moveAction;
        }
      }
      
      // If we've already moved but not collected cards, try to collect cards
      if (this._turnState.hasMoved && !this._turnState.hasCollectedCards) {
        // Randomly decide which type of card to collect
        const cardTypeToCollect = this._chooseRandomCardType(bot);
        let collectionAction;
        
        if (cardTypeToCollect === 'journey') {
          collectionAction = await this.evaluateJourneyCollection(bot);
        } else {
          // Travel or event
          collectionAction = await this.evaluateCardPicks(bot, cardTypeToCollect);
        }
        
        if (collectionAction && collectionAction.action) {
          this._turnState.hasCollectedCards = true;
          
          // Record this action
          this.recordMove({
            type: collectionAction.action,
            params: this._minimizeParams(collectionAction.params)
          });
          
          return collectionAction;
        }
      }
      
      // If we get here, we couldn't perform any actions and haven't done both actions
      // Just end the turn
      bot.logger.warn(`Bot ${bot.name} couldn't find any valid actions. Ending turn.`);
      return {
        action: 'endTurn',
        params: {}
      };
    } catch (error) {
      bot.logger.error(`CRITICAL ERROR in RandomStrategy.planTurn: ${error.message}`);
      
      // Instead of safely ending the turn, rethrow the error to hard-crash
      throw error;
    }
  }
  
  /**
   * Choose a random card type to collect based on bot's current state
   * @param {Bot} bot - The bot to choose a card type for
   * @returns {string} The card type to collect ('travel', 'event', or 'journey')
   * @private
   */
  _chooseRandomCardType(bot) {
    // Weight the options based on the bot's current state
    const weights = {
      travel: 0.5,  // Generally prefer travel cards
      event: 0.3,   // Sometimes get event cards
      journey: 0.2  // Occasionally try for journey cards
    };
    
    // If the hand is nearly full, prefer journey cards
    const state = bot.getPlayerState();
    if (state && state.player && Array.isArray(state.player.hand) && state.player.hand.length >= 4) {
      weights.travel = 0.2;
      weights.event = 0.2;
      weights.journey = 0.6;
    }
    
    // If the bot has very few travel cards, prioritize them even more
    const travelCardCount = this._countTravelCards(bot);
    if (travelCardCount <= 1) {
      weights.travel = 0.8;
      weights.event = 0.1;
      weights.journey = 0.1;
    }
    
    // If the bot has NO travel cards, ALWAYS pick travel cards
    if (travelCardCount === 0) {
      weights.travel = 1.0;
      weights.event = 0.0;
      weights.journey = 0.0;
    }
    
    // Random weighted selection
    const random = Math.random();
    if (random < weights.travel) return 'travel';
    if (random < weights.travel + weights.event) return 'event';
    return 'journey';
  }
  
  /**
   * Check if bot has travel cards
   * @param {Bot} bot - The bot to check
   * @returns {boolean} Whether the bot has travel cards
   * @private
   */
  _hasTravelCards(bot) {
    return this._countTravelCards(bot) > 0;
  }
  
  /**
   * Count the number of travel cards in the bot's hand
   * @param {Bot} bot - The bot to count travel cards for
   * @returns {number} The number of travel cards
   * @private
   */
  _countTravelCards(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player) return 0;
    
    // Only look at travel cards (not event cards)
    const travelCards = Array.isArray(state.player.hand) ? 
      state.player.hand.filter(card => card && card.type === 'travel' && card.value) : [];
    
    return travelCards.length;
  }
  
  /**
   * Create a memory-efficient version of parameters for recording
   * @param {object} params - The original parameters
   * @returns {object} Minimized parameters
   * @private
   */
  _minimizeParams(params) {
    // Just create a simple summary for recording purposes
    if (!params) return {};
    
    const result = {};
    
    if (params.path) {
      result.pathLength = params.path.length;
    }
    
    if (params.travelCardIds) {
      result.cardCount = params.travelCardIds.length;
    }
    
    if (params.type) {
      result.type = params.type;
    }
    
    if (params.journeyType) {
      result.journeyType = params.journeyType;
    }
    
    if (params.actions) {
      result.actionCount = params.actions.length;
    }
    
    return result;
  }
  
  /**
   * Override recordMove to limit the history size
   * @param {object} move - The move to record
   */
  recordMove(move) {
    // Call the parent method
    super.recordMove(move);
    
    // Limit the history size to prevent memory growth
    if (this.moveHistory.length > this.moveHistoryLimit) {
      this.moveHistory = this.moveHistory.slice(-this.moveHistoryLimit);
    }
  }
  
  /**
   * Analyze possible moves
   * @param {Bot} bot - The bot to analyze moves for
   * @returns {Promise<object>} Best move action
   */
  async analyzeMoves(bot) {
    try {
      // Log memory state at the start of analyzeMoves, especially for turn 4
      if (bot.turnsPlayed === 4) {
        global.isTurn4 = true; // Set global flag for other components
        
        const memUsage = process.memoryUsage();
        console.log(`[MEMORY-DEBUG] RandomStrategy.analyzeMoves - Turn ${bot.turnsPlayed} - Bot ${bot.name}`);
        console.log(`[MEMORY-DEBUG] Memory before path analysis: Heap=${Math.round(memUsage.heapUsed/1024/1024)}MB, RSS=${Math.round(memUsage.rss/1024/1024)}MB`);
      }
      
      const state = bot.getPlayerState();
      const player = state.player;
      
      if (!player) {
        throw new Error('Invalid player state');
      }
      
      // Only look at travel cards (not event cards)
      const travelCards = Array.isArray(player.hand) ? 
        player.hand.filter(card => card && card.type === 'travel' && card.value) : [];
      
      // Count extra hop cards
      const extraHopCards = Array.isArray(player.hand) ? 
        player.hand.filter(card => card && card.type === 'extraHop') : [];
      const extraHops = extraHopCards.length;
      
      // If we have no travel cards, we can't move
      if (travelCards.length === 0) {
        if (bot.turnsPlayed === 4) {
          console.log(`[MEMORY-DEBUG] No travel cards available, cannot move.`);
          global.isTurn4 = false;
        }
        return null;
      }
      
      // Get all possible paths
      let possiblePaths;
      try {
        // Memory checkpoint before path calculation
        if (bot.turnsPlayed === 4) {
          const memBefore = process.memoryUsage();
          console.log(`[MEMORY-DEBUG] Before pathCalculator.findPossiblePaths: Heap=${Math.round(memBefore.heapUsed/1024/1024)}MB`);
        }
        
        possiblePaths = this.pathCalculator.findPossiblePaths(
          player.position,
          travelCards,
          extraHops
        );
        
        // Memory checkpoint after path calculation
        if (bot.turnsPlayed === 4) {
          const memAfter = process.memoryUsage();
          console.log(`[MEMORY-DEBUG] After pathCalculator.findPossiblePaths: Heap=${Math.round(memAfter.heapUsed/1024/1024)}MB`);
          console.log(`[MEMORY-DEBUG] Found ${possiblePaths ? possiblePaths.length : 0} possible paths`);
        }
      } catch (error) {
        bot.logger.error(`Error finding possible paths: ${error.message}`);
        
        if (bot.turnsPlayed === 4) {
          console.log(`[MEMORY-DEBUG] Error in path calculation: ${error.message}`);
          global.isTurn4 = false;
        }
        return null;
      }
      
      // If no paths are available, we can't move
      if (!Array.isArray(possiblePaths) || possiblePaths.length === 0) {
        return null;
      }
      
      // Memory optimization: Limit the number of paths considered
      const maxPathsToConsider = 50; // Reasonable limit
      if (possiblePaths.length > maxPathsToConsider) {
        possiblePaths = possiblePaths.slice(0, maxPathsToConsider);
      }
      
      // Choose a random path
      const randomPath = possiblePaths[Math.floor(Math.random() * possiblePaths.length)];
      
      // If the path is just our current position or invalid, we can't move
      if (!Array.isArray(randomPath) || randomPath.length <= 1) {
        return null;
      }
      
      // Calculate the distance we need to travel
      const distance = randomPath.length - 1;
      
      // Choose random cards to cover the distance
      let remainingDistance = distance;
      const cardsToUse = [];
      let extraHopsToUse = 0;
      
      // Randomly select cards until we cover the distance
      const shuffledCards = [...travelCards].sort(() => Math.random() - 0.5);
      
      for (const card of shuffledCards) {
        if (remainingDistance > 0) {
          cardsToUse.push(card);
          remainingDistance -= card.value;
        }
      }
      
      // If we still need distance and have extra hops, use them
      if (remainingDistance > 0 && extraHops > 0) {
        extraHopsToUse = Math.min(extraHops, remainingDistance);
        remainingDistance -= extraHopsToUse;
      }
      
      // If we still can't cover the distance, pick a shorter path or return null
      if (remainingDistance > 0) {
        return null;
      }
      
      // Create the action plan for moving
      return {
        action: 'movePlayer',
        params: {
          path: randomPath,
          cardIds: cardsToUse.map(card => card.id),
          extraHopCount: extraHopsToUse,
          isTriathlon: false
        }
      };
    } catch (error) {
      bot.logger.error(`Error in RandomStrategy.analyzeMoves: ${error.message}`);
      return null;
    } finally {
      // Ensure we clear the global flag 
      if (bot.turnsPlayed === 4) {
        global.isTurn4 = false;
      }
    }
  }
  
  /**
   * Evaluate possible card picks
   * @param {Bot} bot - The bot to evaluate card picks for
   * @param {string} preferredType - The preferred card type to pick ('travel' or 'event')
   * @returns {Promise<object>} Best card pick action
   */
  async evaluateCardPicks(bot, preferredType = null) {
    try {
      const state = bot.getPlayerState();
      const player = state.player;
      
      if (!player) {
        throw new Error('Invalid player state');
      }
      
      // If the hand is already full, we can't pick cards
      if (Array.isArray(player.hand) && player.hand.length >= 4) {
        return null;
      }
      
      // If bot has no travel cards, ALWAYS force travel card pick regardless of preferred type
      const travelCardCount = this._countTravelCards(bot);
      if (travelCardCount === 0) {
        bot.logger.info(`Bot ${bot.name} has no travel cards, forcing travel card pick`);
        preferredType = 'travel';
      }
      // If bot has only 1 travel card, always prioritize getting more
      else if (travelCardCount === 1) {
        bot.logger.info(`Bot ${bot.name} has only 1 travel card, prioritizing travel card pick`);
        preferredType = 'travel';
      }
      // Even with 2 travel cards, usually prioritize more travel cards
      else if (travelCardCount <= 2 && Math.random() < 0.8) {
        bot.logger.info(`Bot ${bot.name} has only ${travelCardCount} travel cards, likely prioritizing travel card pick`);
        preferredType = 'travel';
      }
      
      // Determine card type (prefer travel cards if no preference specified)
      const cardType = preferredType || (Math.random() < 0.7 ? 'travel' : 'event');
      
      // Check hand space
      const handSize = Array.isArray(player.hand) ? player.hand.length : 0;
      const handSpaceLeft = 4 - handSize;
      
      // If hand is almost full (1-2 spaces left) and we have few or no travel cards,
      // we absolutely must pick travel cards
      if (handSpaceLeft <= 2 && travelCardCount <= 1) {
        bot.logger.info(`Bot ${bot.name} has limited hand space (${handSpaceLeft}) and few travel cards (${travelCardCount}), forcing travel card pick`);
        preferredType = 'travel';
      }
      
      // For travel cards, randomly select 0-2 face-up cards
      if (cardType === 'travel') {
        const faceUpTravel = Array.isArray(state.faceUpTravel) ? state.faceUpTravel : [];
        const maxPickable = Math.min(2, 4 - (Array.isArray(player.hand) ? player.hand.length : 0));
        
        // Randomly decide how many face-up cards to pick
        const numToPick = Math.floor(Math.random() * (maxPickable + 1));
        
        if (numToPick === 0 || faceUpTravel.length === 0) {
          // Pick only from the deck
          return {
            action: 'pickCards',
            params: {
              type: 'travel',
              pickFromFaceUp: []
            }
          };
        } else {
          // Pick some face-up cards
          const shuffledFaceUpCards = [...faceUpTravel].sort(() => Math.random() - 0.5);
          const selectedCards = shuffledFaceUpCards.slice(0, Math.min(numToPick, faceUpTravel.length));
          
          if (!selectedCards.length) {
            return {
              action: 'pickCards',
              params: {
                type: 'travel',
                pickFromFaceUp: []
              }
            };
          }
          
          // Validate that all selected cards have IDs
          const validCardIds = selectedCards
            .filter(card => card && card.id)
            .map(card => card.id);
          
          return {
            action: 'pickCards',
            params: {
              type: 'travel',
              pickFromFaceUp: validCardIds
            }
          };
        }
      } else {
        // For event cards, randomly decide whether to pick face-up or from deck
        const faceUpEvent = Array.isArray(state.faceUpEvent) ? state.faceUpEvent : [];
        
        if (faceUpEvent.length > 0 && Math.random() < 0.5) {
          // Pick a random face-up event card
          const randomCard = faceUpEvent[Math.floor(Math.random() * faceUpEvent.length)];
          
          if (!randomCard || !randomCard.id) {
            // If the card is invalid, pick from the deck
            return {
              action: 'pickCards',
              params: {
                type: 'event',
                pickFromFaceUp: []
              }
            };
          }
          
          return {
            action: 'pickCards',
            params: {
              type: 'event',
              pickFromFaceUp: [randomCard.id]
            }
          };
        } else {
          // Pick from the deck
          return {
            action: 'pickCards',
            params: {
              type: 'event',
              pickFromFaceUp: []
            }
          };
        }
      }
    } catch (error) {
      bot.logger.error(`Error in RandomStrategy.evaluateCardPicks: ${error.message}`);
      return null;
    }
  }
  
  /**
   * Evaluate possible journey card collections
   * @param {Bot} bot - The bot to evaluate journey collections for
   * @returns {Promise<object>} Best journey collection action
   */
  async evaluateJourneyCollection(bot) {
    try {
      const state = bot.getPlayerState();
      const player = state.player;
      
      if (!player) {
        throw new Error('Invalid player state');
      }
      
      // Safety check: If bot has 0 or 1 travel cards, and the hand is almost full (4+),
      // avoid collecting a journey to ensure we can get more travel cards
      const travelCardCount = this._countTravelCards(bot);
      const handSize = Array.isArray(player.hand) ? player.hand.length : 0;
      if (travelCardCount <= 1 && handSize >= 4) {
        bot.logger.info(`Bot ${bot.name} skipping journey collection to ensure space for travel cards (travel=${travelCardCount}, hand=${handSize})`);
        return null;
      }
      
      // Randomly choose between inner and outer journey cards
      const journeyType = Math.random() < 0.5 ? 'inner' : 'outer';
      const faceUpJourney = journeyType === 'inner' ? 
        (Array.isArray(state.faceUpJourneyInner) ? state.faceUpJourneyInner : []) : 
        (Array.isArray(state.faceUpJourneyOuter) ? state.faceUpJourneyOuter : []);
      
      // Check if there are any journey cards available
      if (faceUpJourney.length === 0) {
        return null;
      }
      
      // Randomly select a journey card
      const randomCard = faceUpJourney[Math.floor(Math.random() * faceUpJourney.length)];
      
      if (!randomCard || !randomCard.id) {
        return null;
      }
      
      // Check if the player can afford the card
      // This is a simplified check - actual affordability is determined by the game state
      const omSlots = journeyType === 'inner' ? 
        (Array.isArray(player.omSlotsInner) ? player.omSlotsInner : []) : 
        (Array.isArray(player.omSlotsOuter) ? player.omSlotsOuter : []);
      
      const emptySlotsCount = omSlots.filter(slot => slot === 0).length;
      
      if (emptySlotsCount === 0) {
        return null; // No empty slots
      }
      
      // Count energy cubes by type
      const cubesByType = {};
      if (Array.isArray(player.energyCubes)) {
        for (const cube of player.energyCubes) {
          if (cube) {
            cubesByType[cube] = (cubesByType[cube] || 0) + 1;
          }
        }
      }
      
      // Simplified check for required cubes
      let canAfford = true;
      if (randomCard.required && typeof randomCard.required === 'object') {
        for (const [type, count] of Object.entries(randomCard.required)) {
          if ((cubesByType[type] || 0) < count) {
            canAfford = false;
            break;
          }
        }
      } else {
        // If the card doesn't have requirements, assume we can't afford it
        canAfford = false;
      }
      
      // Also check if we have enough OM tokens for the journey slot
      // The OM cost depends on the player count
      const playerCount = bot.gameState?.players?.length || 3;
      // Use 0-1-1-2 for 4 players, 1-1-2-3 for 2-3 players
      const omCosts = playerCount === 4 ? [0, 1, 1, 2] : [1, 1, 2, 3];
      const slotIndex = Math.min(4 - emptySlotsCount, 3);
      const omCost = omCosts[slotIndex];
      
      if (!hasEnoughOmForJourney(player, journeyType, slotIndex)) {
        canAfford = false;
      }
      
      if (!canAfford) {
        return null;
      }
      
      // Create the action plan for collecting a journey
      return {
        action: 'collectJourney',
        params: {
          journeyCardId: randomCard.id,
          journeyType
        }
      };
    } catch (error) {
      bot.logger.error(`Error in RandomStrategy.evaluateJourneyCollection: ${error.message}`);
      return null;
    }
  }
  
  /**
   * Check if the bot can make a move
   * @param {Bot} bot - The bot to check
   * @returns {boolean} Whether the bot can make a move
   * @private
   */
  _canMove(bot) {
    try {
      const state = bot.getPlayerState();
      if (!state || !state.player) return false;
      
      // Check if player has travel cards
      const travelCards = Array.isArray(state.player.hand) ? 
        state.player.hand.filter(card => card && card.type === 'travel' && card.value) : [];
      
      return travelCards.length > 0;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Check if the bot can pick cards
   * @param {Bot} bot - The bot to check
   * @returns {boolean} Whether the bot can pick cards
   * @private
   */
  _canPickCards(bot) {
    try {
      const state = bot.getPlayerState();
      if (!state || !state.player) return false;
      
      // Can only pick cards if hand is not full
      return !Array.isArray(state.player.hand) || state.player.hand.length < 4;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Check if the bot can collect a journey
   * @param {Bot} bot - The bot to check
   * @returns {boolean} Whether the bot can collect a journey
   * @private
   */
  _canCollectJourney(bot) {
    try {
      const state = bot.getPlayerState();
      if (!state || !state.player) return false;
      
      // Need at least one free OM slot (inner or outer)
      const innerSlots = state.player.omSlotsInner.filter(s => s === 0).length;
      const outerSlots = state.player.omSlotsOuter.filter(s => s === 0).length;
      
      // Handle both array and number types for backward compatibility
      const omTemp = Array.isArray(state.player.omTemp) ? state.player.omTemp.length : (state.player.omTemp || 0);
      
      return (innerSlots + outerSlots) > 0 && omTemp > 0;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Log the bot's hand state
   * @param {Bot} bot - The bot to log hand state for
   * @private
   */
  _logHandState(bot) {
    try {
      const state = bot.getPlayerState();
      if (!state || !state.player) return;
      
      const handCards = Array.isArray(state.player.hand) ? state.player.hand : [];
      const travelCards = handCards.filter(card => card && card.type === 'travel' && card.value);
      const eventCards = handCards.filter(card => card && card.type === 'event');
      const otherCards = handCards.filter(card => card && card.type !== 'travel' && card.type !== 'event');
      
      // Create a detailed hand representation
      const handDetails = handCards.map(card => {
        if (!card) return 'null';
        return `${card.id || '?'}:${card.type || '?'}${card.type === 'travel' ? `:${card.value || '?'}` : ''}`;
      }).join(', ');
      
      bot.logger.info(`Bot ${bot.name} hand state: Total=${handCards.length}, Travel=${travelCards.length}, Event=${eventCards.length}, Other=${otherCards.length}`);
      bot.logger.info(`Bot ${bot.name} hand contents: [${handDetails}]`);
      
      // Log details of travel cards
      if (travelCards.length > 0) {
        const travelValues = travelCards.map(card => `${card.id}:${card.value}`).join(', ');
        bot.logger.info(`Bot ${bot.name} travel cards: [${travelValues}]`);
      }
      
      // Check for incomplete/invalid cards
      const invalidCards = handCards.filter(card => !card || !card.type);
      if (invalidCards.length > 0) {
        bot.logger.warn(`Bot ${bot.name} has ${invalidCards.length} invalid cards in hand!`);
      }
    } catch (error) {
      // Log but don't fail for logging function
      bot.logger.error(`Error in _logHandState: ${error.message}`);
    }
  }
}

// Helper to check if player has enough OM tokens for a journey card
const hasEnoughOmForJourney = (player, journeyType, count) => {
  // OM cost depends on the player count - use 0-1-1-2 for 4 players, 1-1-2-3 for 2-3 players
  const playerCount = player.gameState?.players?.length || 3;
  const omCosts = playerCount === 4 ? [0, 1, 1, 2] : [1, 1, 2, 3];
  const omCost = omCosts[count];
  
  // Handle both array and number types for backward compatibility
  const omTemp = Array.isArray(player.omTemp) ? player.omTemp.length : (player.omTemp || 0);
  if (omTemp < omCost) {
    return false;
  }
  return true;
}

// Check if player has any slots with OM tokens and has OM tokens in temp storage
const canPlaceOmTokens = (state) => {
  if (!state || !state.player) return false;
  
  const innerSlots = state.player.omSlotsInner.filter(s => s === 0).length;
  const outerSlots = state.player.omSlotsOuter.filter(s => s === 0).length;
  
  // Handle both array and number types for backward compatibility
  const omTemp = Array.isArray(state.player.omTemp) ? state.player.omTemp.length : (state.player.omTemp || 0);
  
  return (innerSlots + outerSlots) > 0 && omTemp > 0;
}

module.exports = RandomStrategy; 