/**
 * server/simulation/strategies/OptimizedDefensiveStrategy.js
 * A streamlined defensive strategy for bot play.
 */

const BaseStrategy = require('./BaseStrategy');
const PathCalculator = require('../PathCalculator');
const { journeyDeckInner, journeyDeckOuter } = require('../../boardData');

// Helper to count OM tokens in temp slots
const getOmTempCount = player =>
  Array.isArray(player.omTemp) ? player.omTemp.length : (player.omTemp || 0);

/**
 * Calculate the OM tokens required for the next journey card
 * @param {number} collectedCount - Number of journey cards already collected
 * @param {number} playerCount - Total number of players in the game
 * @returns {number} - Required OM tokens
 */
const calculateRequiredOmTokens = (collectedCount, playerCount = 3) => {
  if (playerCount === 4) {
    if (collectedCount === 0) return 0; // First card (free)
    if (collectedCount === 1) return 1; // Second card
    if (collectedCount === 2) return 1; // Third card
    if (collectedCount === 3) return 2; // Fourth card
  } else {
    if (collectedCount === 0) return 1; // First card
    if (collectedCount === 1) return 1; // Second card
    if (collectedCount === 2) return 2; // Third card
    if (collectedCount === 3) return 3; // Fourth card
  }
  return 0; // More than 4 cards (should not happen)
};

class OptimizedDefensiveStrategy extends BaseStrategy {
  constructor(config = {}) {
    super({ name: 'optimized-defensive', config });
    this.pathCalculator = new PathCalculator();
    this._currentTarget = null;
    this._turnState = {
      hasMoved: false,
      hasCollectedCards: false,
      turnNumber: 0
    };
  }

  /**
   * Check if a travel card can be used to reach a target location
   * @param {number} startLocationId - Starting location ID
   * @param {number} cardValue - Travel card value (number of hops)
   * @param {number} targetLocationId - Target location ID
   * @returns {boolean} - Whether the card can reach the target
   */
  canUseCard(startLocationId, cardValue, targetLocationId) {
    // If we're already at the target, no travel needed
    if (startLocationId === targetLocationId) return false;
    
    // Get the exact distance needed
    const exactDistance = this.pathCalculator.calculateHops(startLocationId, targetLocationId);
    
    // Card can be used if its value matches the exact distance
    return cardValue === exactDistance;
  }
  
  /**
   * Find a path between two locations
   * @param {number} startLocationId - Starting location ID
   * @param {number} targetLocationId - Target location ID 
   * @returns {Array|null} - Array of location IDs or null if no path found
   */
  _findPath(startLocationId, targetLocationId) {
    // If already at the target, return single-node path
    if (startLocationId === targetLocationId) {
      return [startLocationId];
    }
    
    // Use BFS to find the shortest path
    const visited = new Set([startLocationId]);
    const queue = [{ location: startLocationId, path: [startLocationId] }];
    
    while (queue.length > 0) {
      const { location, path } = queue.shift();
      
      // If we've reached the target, return the path
      if (location === targetLocationId) {
        return path;
      }
      
      // Get adjacent locations from the adjacency map
      const adjacent = this.pathCalculator.adjacencyMap.get(location) || new Set();
      
      // Try each adjacent location
      for (const next of adjacent) {
        if (!visited.has(next)) {
          visited.add(next);
          queue.push({
            location: next,
            path: [...path, next]
          });
        }
      }
    }
    
    // If no path found
    return null;
  }

  /**
   * Calculate the OM tokens required for a journey card
   * @param {Object} player - Player data
   * @param {Object} card - Journey card
   * @returns {number} - Required OM tokens
   */
  _calculateRequiredOmTokens(player, card) {
    if (!player || !card) return 0;
    
    // Get already collected journeys
    const collectedJourneys = player.collectedJourneys || [];
    
    // Count journeys of the same type (inner or outer)
    let typeCount = 0;
    if (card.cardType === 'inner' || card.reward?.inner) {
      typeCount = collectedJourneys.filter(j => j.reward?.inner).length;
    } else if (card.cardType === 'outer' || card.reward?.outer) {
      typeCount = collectedJourneys.filter(j => j.reward?.outer).length;
    }
    
    // Get player count from the game state if available
    const playerCount = this.gameState?.players?.length || 3;
    
    return calculateRequiredOmTokens(typeCount, playerCount);
  }

  // Compute a score for each journey card
  _scoreJourneyTarget(player, card) {
    // Get the reward value based on whether it's inner or outer journey
    const reward = card.reward?.inner || card.reward?.outer || 0;
    
    // Calculate distance to this card
    const distance = this.pathCalculator.calculateHops(player.position, card.locationId);
    
    // Calculate OM token requirements based on collection history
    const requiredOm = this._calculateRequiredOmTokens(player, card);
    const gapOm = Math.max(0, requiredOm - getOmTempCount(player));
    
    // Calculate total cube gap
    const gapCubes = Object.entries(card.required || {}).reduce((sum, [type, need]) => {
      const have = (player.energyCubes?.[type] || 0);
      return sum + Math.max(0, need - have);
    }, 0);
    
    // Higher reward, lower cost → better
    return reward / (1 + distance + gapCubes * 2 + gapOm * 3);
  }

  // Choose the best face-up journey card
  _selectBestJourneyTarget(bot) {
    const state = bot.getPlayerState();
    if (!state) return null;
    
    const { player, faceUpJourneyInner = [], faceUpJourneyOuter = [] } = state;
    if (!player) return null;
    
    // Combine and enrich journey cards with type information
    const candidates = [
      ...faceUpJourneyInner.map(card => ({ ...card, cardType: 'inner' })),
      ...faceUpJourneyOuter.map(card => ({ ...card, cardType: 'outer' }))
    ];
    
    if (!candidates.length) return null;

    let best = candidates[0], bestScore = -Infinity;
    for (const card of candidates) {
      const score = this._scoreJourneyTarget(player, card);
      if (score > bestScore) {
        bestScore = score;
        best = card;
      }
    }
    return best;
  }

  // When hand hops + available cards won't reach the target, pick 2 that get closest
  _selectTravelCards(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player) return null;
    
    const player = state.player;
    const handHops = (player.hand || [])
      .filter(c => c.type === 'travel')
      .reduce((sum, c) => sum + (c.value || 0), 0);
      
    // If no target, we can't select cards
    const target = this._currentTarget;
    if (!target) return null;

    // Calculate how many more hops we need
    const needed = Math.max(0,
      this.pathCalculator.calculateHops(player.position, target.locationId) - handHops
    );
    
    // If we don't need any more hops, no need to select travel cards
    if (needed === 0) return null;
    
    const market = state.faceUpTravel || [];
    if (market.length < 2) return null; // Need at least 2 face-up cards
    
    let bestCombo = null, bestDiff = Infinity;

    // Find the best combination of 2 cards
    for (let i = 0; i < market.length; i++) {
      for (let j = i + 1; j < market.length; j++) {
        const sum = (market[i].value || 0) + (market[j].value || 0);
        const diff = Math.abs(needed - sum);
        if (diff < bestDiff) {
          bestDiff = diff;
          bestCombo = [market[i].id, market[j].id];
        }
      }
    }
    return bestCombo;
  }

  // Implement the required analyzeMoves method
  async analyzeMoves(bot) {
    // Simple implementation - will be enhanced later
    return [];
  }
  
  // Implement the required evaluateCardPicks method
  async evaluateCardPicks(bot, cardType = 'travel') {
    const state = bot.getPlayerState();
    if (!state || !state.player) return null;
    
    // Check if we can pick cards (hand not full)
    const handSize = state.player.hand ? state.player.hand.length : 0;
    if (handSize >= 4) return null;
    
    const faceUpCards = cardType === 'travel' ? 
      (state.faceUpTravel || []) : (state.faceUpEvent || []);
      
    // If no face-up cards, pick from top of deck
    if (faceUpCards.length === 0) {
      return {
        action: 'pickCards',
        params: {
          type: cardType,
          pickFromTop: true
        }
      };
    }
    
    // Otherwise, pick the highest value face-up cards
    const sortedCards = [...faceUpCards].sort((a, b) => (b.value || 0) - (a.value || 0));
    const maxPickCount = Math.min(4 - handSize, 2, sortedCards.length);
    const cardsToPick = sortedCards.slice(0, maxPickCount);
    
    return {
      action: 'pickCards',
      params: {
        type: cardType,
        pickFromFaceUp: cardsToPick.map(card => card.id)
      }
    };
  }
  
  // Implement the required evaluateJourneyCollection method
  async evaluateJourneyCollection(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player || !this._currentTarget) return null;
    
    const player = state.player;
    const target = this._currentTarget;
    
    // Check if player is at the target location
    if (player.position !== target.locationId) return null;
    
    // Check if player has required cubes
    const hasCubes = Object.entries(target.required || {}).every(
      ([type, need]) => (player.energyCubes?.[type] || 0) >= need
    );
    
    // Calculate the OM tokens required for this journey card
    const requiredOm = this._calculateRequiredOmTokens(player, target);
    
    // Check if player has required OM tokens
    const hasOm = getOmTempCount(player) >= requiredOm;
    
    if (hasCubes && hasOm) {
      return {
        action: 'collectJourney',
        params: {
          journeyCardId: target.id,
          journeyType: target.cardType // 'inner' or 'outer'
        }
      };
    }
    
    return null;
  }

  // Core planning loop
  async planTurn(bot) {
    const state = bot.getPlayerState();
    if (!state || !state.player) {
      return { action: 'endTurn', params: {} };
    }
    
    const player = state.player;
    
    if (!state.isMyTurn) {
      return { action: 'endTurn', params: {} };
    }
    
    // Check if this is a new turn
    if (bot.turnsPlayed > this._turnState.turnNumber) {
      this._turnState = {
        hasMoved: false,
        hasCollectedCards: false,
        turnNumber: bot.turnsPlayed
      };
    }

    // 1) Re-evaluate target when needed
    this._currentTarget = this._selectBestJourneyTarget(bot);

    // 2) If we're on that location and have enough OM + cubes, collect it
    const card = this._currentTarget;
    if (card && player.position === card.locationId) {
      const hasCubes = Object.entries(card.required || {})
        .every(([type, need]) => (player.energyCubes?.[type] || 0) >= need);
      
      // Calculate the OM tokens required for this journey card
      const requiredOm = this._calculateRequiredOmTokens(player, card);
      
      // Check if player has required OM tokens
      const hasOm = getOmTempCount(player) >= requiredOm;

      if (hasCubes && hasOm) {
        this._currentTarget = null;
        this._turnState.hasCollectedCards = true;
        return {
          action: 'collectJourney',
          params: {
            journeyCardId: card.id,
            journeyType: card.cardType // Use the card's type (inner or outer)
          }
        };
      }
    }

    // 3) If we can't reach our target with current hops, pick travel cards
    if (!this._turnState.hasCollectedCards) {
      const combo = this._selectTravelCards(bot);
      if (combo) {
        this._turnState.hasCollectedCards = true;
        return { 
          action: 'pickCards', 
          params: { 
            type: 'travel',
            pickFromFaceUp: combo 
          }
        };
      }
    }

    // 4) Otherwise, move toward target with the valid travel cards
    if (card && !this._turnState.hasMoved) {
      try {
        // Get a fresh copy of the player's travel cards
        const travelCards = (player.hand || []).filter(c => c.type === 'travel');
        if (travelCards.length === 0) {
          // No travel cards, can't move
          return this._pickFallbackCards(state) || { action: 'endTurn', params: {} };
        }
        
        // Calculate the exact distance needed
        const exactDistance = this.pathCalculator.calculateHops(player.position, card.locationId);
        
        // Find a card or combination of cards that exactly matches the distance
        const exactCardMatch = this._findExactCardMatch(travelCards, exactDistance);
        
        if (exactCardMatch) {
          // We found a card or combination that exactly matches the distance
          const path = this._findPath(player.position, card.locationId);
          
          if (!path) {
            // Couldn't find a path - this shouldn't happen if calculateHops returned a finite value
            return this._pickFallbackCards(state) || { action: 'endTurn', params: {} };
          }
          
          this._turnState.hasMoved = true;
          return {
            action: 'movePlayer',
            params: { 
              cardIds: exactCardMatch.map(c => c.id),
              path: path,
              extraHopCount: 0,
              isTriathlon: false
            }
          };
        }
        
        // If we couldn't find an exact match, try finding the best single card
        const bestSingleCard = travelCards.sort((a, b) => (b.value || 0) - (a.value || 0))[0];
        
        if (bestSingleCard) {
          // Find all possible paths with this card's value
          const possiblePaths = this.pathCalculator.findPossiblePaths(
            player.position, 
            [bestSingleCard], 
            0
          );
          
          if (possiblePaths && possiblePaths.length > 0) {
            // Find the path that gets closest to our target
            let bestPath = possiblePaths[0];
            let bestPathDistance = Infinity;
            
            for (const path of possiblePaths) {
              if (path.length > 1) { // Only consider paths that actually move
                const lastLocation = path[path.length - 1];
                const distanceToTarget = this.pathCalculator.calculateHops(lastLocation, card.locationId);
                
                if (distanceToTarget < bestPathDistance) {
                  bestPathDistance = distanceToTarget;
                  bestPath = path;
                }
              }
            }
            
            this._turnState.hasMoved = true;
            return {
              action: 'movePlayer',
              params: { 
                cardIds: [bestSingleCard.id],
                path: bestPath,
                extraHopCount: 0,
                isTriathlon: false
              }
            };
          }
        }
      } catch (error) {
        // If there's an error during movement planning, log it and continue with fallback
        console.error(`Error planning movement: ${error.message}`);
      }
    }

    // 5) Fallback: draw two travel cards to keep options open
    return this._pickFallbackCards(state) || { action: 'endTurn', params: {} };
  }
  
  /**
   * Find a combination of cards that exactly matches a target value
   * @param {Array} cards - Array of travel cards
   * @param {number} targetValue - Target hop value to match
   * @returns {Array|null} - Array of matching cards or null if no match found
   * @private
   */
  _findExactCardMatch(cards, targetValue) {
    // Try single cards first
    for (const card of cards) {
      if ((card.value || 0) === targetValue) {
        return [card]; // Found exact match with a single card
      }
    }
    
    // Try pairs of cards
    if (cards.length >= 2) {
      for (let i = 0; i < cards.length - 1; i++) {
        for (let j = i + 1; j < cards.length; j++) {
          const sum = (cards[i].value || 0) + (cards[j].value || 0);
          if (sum === targetValue) {
            return [cards[i], cards[j]]; // Found exact match with pair of cards
          }
        }
      }
    }
    
    // Try triplets of cards
    if (cards.length >= 3) {
      for (let i = 0; i < cards.length - 2; i++) {
        for (let j = i + 1; j < cards.length - 1; j++) {
          for (let k = j + 1; k < cards.length; k++) {
            const sum = (cards[i].value || 0) + (cards[j].value || 0) + (cards[k].value || 0);
            if (sum === targetValue) {
              return [cards[i], cards[j], cards[k]]; // Found exact match with triplet
            }
          }
        }
      }
    }
    
    return null; // No exact match found
  }
  
  /**
   * Fallback card picking when no other actions are available
   * @param {Object} state - The game state
   * @returns {Object|null} - Card picking action or null
   * @private
   */
  _pickFallbackCards(state) {
    if (!state) return null;
    
    if (!this._turnState.hasCollectedCards && (state.faceUpTravel || []).length >= 2) {
      // Sort by value to pick the highest-value cards
      const sortedCards = [...state.faceUpTravel].sort((a, b) => (b.value || 0) - (a.value || 0));
      const cardsToPick = sortedCards.slice(0, 2);
      
      this._turnState.hasCollectedCards = true;
      return {
        action: 'pickCards',
        params: { 
          type: 'travel',
          pickFromFaceUp: cardsToPick.map(c => c.id)
        }
      };
    }
    
    return null;
  }
}

module.exports = OptimizedDefensiveStrategy;