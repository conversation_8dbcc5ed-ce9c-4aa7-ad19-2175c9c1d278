// server/gameState.js

const {
  locations,
  edges,
  travelDeck,
  eventDeck,
  journeyDeckInner,
  journeyDeckOuter
} = require('./boardData');
const characterCards = require('./characterCards');
const { EventEmitter } = require('events');

const MAX_EVENTS_HISTORY = 1000; // Limit event history to last 1000 events

// 4 types of energy cubes
const ENERGY_TYPES = ['artha', 'karma', 'gnana', 'bhakti'];
// Number of spaces on the Om Turn Track (0 to 7)
const OM_TRACK_SPACES = 8;

// Global event cards
const GLOBAL_EVENT_CARDS = [
  {
    id: 'global-event-1',
    name: 'Drizzle of Delay',
    text: 'Max 2 moves; ending in North or East costs 1 Artha.',
    effect: 'max_moves_2_and_cost_artha_north_east'
  },
  {
    id: 'global-event-2',
    name: '<PERSON><PERSON><PERSON> Distraction',
    text: 'All gain +5 inner pts but no cube pickup.',
    effect: 'gain_5_inner_no_cube_pickup'
  },
  {
    id: 'global-event-3',
    name: '<PERSON><PERSON>',
    text: 'Visit any Jyotirlinga for 7 inner pts; skip for 1 bonus cube.',
    effect: 'jyotirlinga_7_inner_or_bonus_cube'
  },
  {
    id: 'global-event-4',
    name: 'Drought of Spirits',
    text: 'No inner journey cards can be collected this round',
    effect: 'no_inner_journey_cards'
  },
  {
    id: 'global-event-5',
    name: 'Bountiful Bhandara',
    text: 'Draw 2 random energy cubes; +5 outer points if any journey card collected',
    effect: 'draw_2_cubes_bonus_5_outer'
  },
  {
    id: 'global-event-6',
    name: 'Turbulent Skies',
    text: 'No airport travel this round',
    effect: 'no_airport_travel'
  },
  {
    id: 'global-event-7',
    name: 'Election Campaigns',
    text: 'All trade yield 2x. No travel allowed this round',
    effect: 'double_trade_no_travel'
  },
  {
    id: 'global-event-8',
    name: 'Triathlon',
    text: 'Gain +7 outer points if travelled with 3 unique value travel cards this round',
    effect: 'triathlon_bonus'
  },
  {
    id: 'global-event-9',
    name: 'Riots',
    text: 'Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1',
    effect: 'riots_discard'
  },
  {
    id: 'global-event-10',
    name: 'Om Meditation',
    text: 'Gain 1 om token from nearest jyotirlinga in your current region',
    effect: 'om_meditation'
  },
  {
    id: 'global-event-32',
    name: 'Eco Trail',
    text: 'Cycle or Trek: gain +5 Inner points',
    effect: 'eco_trail_reward'
  },
  {
    id: 'global-event-33',
    name: 'Rajput Caravans',
    text: 'Horse or Camel: gain +5 Outer points',
    effect: 'rajput_caravans_reward'
  },
  {
    id: 'global-event-34',
    name: 'Urban Ride',
    text: 'Motorbike or Rickshaw: gain +5 Outer points',
    effect: 'urban_ride_reward'
  },
  {
    id: 'global-event-35',
    name: 'Road Warriors',
    text: 'Car or Bus: gain +5 Outer points',
    effect: 'road_warriors_reward'
  },
  {
    id: 'global-event-36',
    name: 'Rails and Sails',
    text: 'Train or Boat: gain +5 Outer points',
    effect: 'rails_and_sails_reward'
  },
  {
    id: 'global-event-21',
    name: 'Heavy Haul',
    text: 'Use the Truck travel card for 10 outer points but lose 2 energy cubes',
    effect: 'heavy_haul_reward'
  },
  {
    id: 'global-event-23',
    name: 'Merchant\'s Midas',
    text: 'Gain 7 outer points if a merchant trades for artha',
    effect: 'merchants_midas_reward'
  },
  {
    id: 'global-event-24',
    name: 'Professor\'s Insight',
    text: 'Gain 7 inner points if a Professor trades for gnana',
    effect: 'professors_insight_reward'
  },
  {
    id: 'global-event-25',
    name: 'Pilgrim\'s Grace',
    text: 'Gain 7 inner points if a Pilgrim trades for bhakti',
    effect: 'pilgrims_grace_reward'
  },
  {
    id: 'global-event-26',
    name: 'Engineer\'s Precision',
    text: 'Gain 7 outer points if an Engineer trades for karma',
    effect: 'engineers_precision_reward'
  },
  {
    id: 'global-event-27',
    name: 'Frozen North',
    text: 'If starting in North: moves cost 2x; if moved, gain 7 inner points',
    effect: 'frozen_north'
  },
  {
    id: 'global-event-28',
    name: 'Solar South',
    text: 'Lose 2 energy cubes if you end in South',
    effect: 'solar_south'
  },
  {
    id: 'global-event-30',
    name: 'Himalayan NE',
    text: 'Gain 5 inner points if you are in North East at the end of turn',
    effect: 'himalayan_ne_end_turn_reward'
  },
  {
    id: 'global-event-37',
    name: 'Central Heart',
    text: 'Gain 5 inner points if you are in Central at the end of turn',
    effect: 'central_heart_end_turn_reward'
  },
  {
    id: 'global-event-38',
    name: 'Excess Baggage',
    text: 'Hand limit 2 this round, discard down immediately',
    effect: 'excess_baggage'
  },
  {
    id: 'global-event-39',
    name: 'Heritage Site Renovations',
    text: 'No outer journey cards can be collected this round',
    effect: 'no_outer_journey_cards'
  },
  {
    id: 'global-event-40',
    name: 'Spirit of Seva',
    text: 'Leader on each track donates 3 points to player with lowest score on that track',
    effect: 'spirit_of_seva'
  },
  {
    id: 'global-event-41',
    name: 'Pushkar Holy Dip',
    text: 'Gain 5 inner points if you are in West at the end of turn',
    effect: 'pushkar_holy_dip_end_turn_reward'
  },
  {
    id: 'global-event-42',
    name: 'Parikrama in the Clouds',
    text: 'Gain 5 inner points if you visit > 1 Airports this round',
    effect: 'parikrama_in_clouds_reward'
  },
  {
    id: 'global-event-43',
    name: 'Cultural Exchange',
    text: 'At the start of turn, you may swap locations with another player who agrees. Both gain 5 Inner points.',
    effect: 'cultural_exchange'
  }
];

/**
 * Game state management class
 */
class GameState extends EventEmitter {
  /**
   * Create a new game state
   * @param {number} numPlayers - Number of players
   * @param {object} options - Game options
   */
  constructor(numPlayers, options = {}) {
    super(); // Initialize EventEmitter

    this.reset();

    // Initialize players
    this.players = [];
    for (let i = 0; i < numPlayers; i++) {
      const player = {
        id: i + 1, // Assuming player IDs are sequential
        name: `Player ${i + 1}`,
        position: 1, // start location
        hand: [], // up to 4 cards
        energyCubes: [], // up to 5 cubes
        omTemp: [], // up to 3 in temp
        omSlotsOuter: [0, 0, 0, 0], // required: [1,1,2,3]
        omSlotsInner: [0, 0, 0, 0],
        outerScore: 0,
        innerScore: 0,
        collectedJourneys: [],
        didMoveThisTurn: false,
        didSelectionActionThisTurn: false,
        didTradeThisTurn: false,
        cardPickCount: 0,
        character: null, // Character card will be assigned at game start
      };
      this.players.push(player);
    }
  }

  reset() {
    this.players = []; // each: { id, name, position, hand, energyCubes, omTemp, omSlotsOuter, omSlotsInner, outerScore, innerScore, didMoveThisTurn, didSelectionActionThisTurn, ...}
    this.started = false;
    this.turnIndex = 0;
    this.roundCount = 0;
    // Initialize Om Turn Track stacks
    this.omTrack = Array.from({ length: OM_TRACK_SPACES }, () => []);

    // Add game events array to track all actions
    this.gameEvents = [];

    // Add round summaries array to track round information
    this.roundSummaries = [];

    // final round tracking
    this._finalRound = false;
    this._finalRoundStarter = null;
    this._finalRoundEnd = null;

    // OM token victory tracking
    this._omTokenVictory = false;
    this._omTokenVictor = null;

    // Game settings
    this.nameMode = false; // When true, shows energy cubes inside location circles instead of location numbers

    // Initialize energy cube pile
    this.energyCubePile = {
      artha: 2,
      karma: 2,
      gnana: 2,
      bhakti: 2
    };

    // Initialize global event cards deck - will be shuffled in a new game
    this.globalEventDeck = shuffle([...GLOBAL_EVENT_CARDS]);
    this.globalEventDiscard = [];
    this.currentGlobalEvent = null;
    this.currentRoundHandLimit = 4; // Default hand limit is 4, can be changed by global events

    // Draw initial global event card
    this._drawGlobalEventCard(false);

    // Shuffle decks
    this.travelDeck = shuffle([...travelDeck]);
    this.eventDeck = shuffle([...eventDeck]);
    this.journeyDeckInner = shuffle([...journeyDeckInner]);
    this.journeyDeckOuter = shuffle([...journeyDeckOuter]);

    // Initialize discard piles for all cards
    this.travelDiscard = [];
    this.eventDiscard = [];
    this.journeyDiscardInner = [];
    this.journeyDiscardOuter = [];

    // Face-up cards
    this.faceUpTravel = [];
    this.faceUpEvent = [];
    this.faceUpJourneyInner = [];
    this.faceUpJourneyOuter = [];

    // Character cards (shuffle them for random assignment)
    this.characterDeck = shuffle([...characterCards]);

    // locationCubes: locationId => energy type or null
    this.locationCubes = {};
    // locationOm: locationId => boolean
    this.locationOm = {};

    this._distributeCubesAndOm();
    this._drawInitialFaceUp();
  }

  _distributeCubesAndOm() {
    // Get all 48 standard locations
    const standardLocs = locations.filter((loc) => loc.id <= 48).map((l) => l.id);
    shuffle(standardLocs);

    // Create 12 cubes of each energy type (48 total)
    const allCubes = [];
    ENERGY_TYPES.forEach((type) => {
      for (let i = 0; i < 12; i++) {
        allCubes.push(type);
      }
    });
    shuffle(allCubes);

    // Distribute the cubes randomly across all 48 locations
    for (let i = 0; i < 48; i++) {
      this.locationCubes[standardLocs[i]] = allCubes[i];
    }

    // 12 Jyotirlingas (IDs 49..60) => place an OM token
    for (let j = 49; j <= 60; j++) {
      this.locationOm[j] = true;
    }
  }

  _drawInitialFaceUp() {
    while (this.faceUpTravel.length < 4 && this.travelDeck.length > 0) {
      this.faceUpTravel.push(this.travelDeck.pop());
    }
    while (this.faceUpEvent.length < 4 && this.eventDeck.length > 0) {
      this.faceUpEvent.push(this.eventDeck.pop());
    }
    while (this.faceUpJourneyInner.length < 4 && this.journeyDeckInner.length > 0) {
      this.faceUpJourneyInner.push(this.journeyDeckInner.pop());
    }
    while (this.faceUpJourneyOuter.length < 4 && this.journeyDeckOuter.length > 0) {
      this.faceUpJourneyOuter.push(this.journeyDeckOuter.pop());
    }
  }

  addPlayer(playerId, playerName) {
    if (this.players.length >= 4) return false; // max 4
    if (this.started) return false; // cannot join after start

    this.players.push({
      id: playerId,
      name: playerName,
      position: 1, // start location
      hand: [], // up to 4 cards
      energyCubes: [], // up to 5 cubes
      omTemp: [], // up to 3 in temp
      omSlotsOuter: [0, 0, 0, 0], // required: [1,1,2,3]
      omSlotsInner: [0, 0, 0, 0],
      outerScore: 0,
      innerScore: 0,
      collectedJourneys: [],
      didMoveThisTurn: false,
      didSelectionActionThisTurn: false,
      didTradeThisTurn: false,
      cardPickCount: 0,
      character: null, // Character card will be assigned at game start
    });
    return true;
  }

  startGame() {
    if (this.players.length < 2) return false;
    this.started = true;

    // Use blank spots for starting positions (ensure we have enough for 4 players)
    // Locations 61-65 are blank spots that can be used for starting positions
    const blankSpots = [61, 62, 63, 64, 65];
    shuffle(blankSpots);

    // Assign each player a unique blank spot as starting location
    this.players.forEach((player, index) => {
      player.position = blankSpots[index];
      player.didMoveThisTurn = false;
      player.didSelectionActionThisTurn = false;
      player.didTradeThisTurn = false;

      // Assign a character card to each player
      player.character = this.characterDeck.pop();
    });

    // Deal initial travel cards to each player
    this._dealInitialTravelCards();
    // Initialize Om Turn Track after game start
    this._initializeOmTrack();

    return true;
  }

  _currentPlayer() {
    // Add logging to help debug turn order issues
    const currentPlayer = this.players[this.turnIndex];
    if (currentPlayer) {
      console.log(`Current player is ${currentPlayer.name} (${currentPlayer.id}) at turnIndex ${this.turnIndex}`);
    } else {
      console.error(`ERROR: No player found at turnIndex ${this.turnIndex}. Total players: ${this.players.length}`);
    }
    return currentPlayer;
  }

  getState() {
    // Create a memory-efficient representation of the state
    // Avoid deep cloning when possible
    const state = {
      // Include only essential data
      started: this.started,
      players: this.players.map(player => ({
        id: player.id,
        name: player.name,
        position: player.position,
        hand: player.hand,
        energyCubes: player.energyCubes,
        omTemp: player.omTemp,
        omSlotsOuter: player.omSlotsOuter,
        omSlotsInner: player.omSlotsInner,
        collectedJourneys: player.collectedJourneys,
        outerScore: player.outerScore,
        innerScore: player.innerScore,
        didMoveThisTurn: player.didMoveThisTurn,
        didSelectionActionThisTurn: player.didSelectionActionThisTurn,
        didTradeThisTurn: player.didTradeThisTurn,
        character: player.character,  // Include character information
        pendingCardSelection: player.pendingCardSelection,
        pendingCubeSelection: player.pendingCubeSelection,
        pendingHeavyHaulReward: player.pendingHeavyHaulReward
      })),
      turnIndex: this.turnIndex,
      roundCount: this.roundCount,
      finalRound: this._finalRound,
      omTokenVictory: this._omTokenVictory,
      omTokenVictor: this._omTokenVictor,
      faceUpTravel: this.faceUpTravel,
      faceUpEvent: this.faceUpEvent,
      faceUpJourneyOuter: this.faceUpJourneyOuter,
      faceUpJourneyInner: this.faceUpJourneyInner,
      locationCubes: this.locationCubes,
      locationOm: this.locationOm,
      // Om Turn Track stacks
      omTrack: this.omTrack,
      // Include energy cube pile
      energyCubePile: this.energyCubePile,
      // Include current global event
      currentGlobalEvent: this.currentGlobalEvent,
      // Include game settings
      nameMode: this.nameMode,
      // Ensure locations and edges are always included
      locations: locations,
      edges: edges,
      // Include only recent events, not the full history
      recentEvents: this.gameEvents ? this.gameEvents.slice(-50) : []
    };

    return state;
  }

  _findPlayer(id) {
    return this.players.find(player => player.id === id);
  }

  /**
   * Get the player state for a specific player
   * @param {string|number} playerId - The ID of the player
   * @returns {Object|null} Player state object or null if player not found
   */
  getPlayerState(playerId) {
    const player = this._findPlayer(playerId);
    if (!player) return null;

    // Ensure player has collectedJourneys property
    if (!player.collectedJourneys) {
      player.collectedJourneys = [];
      console.log(`WARNING: Player ${player.name} (${player.id}) missing collectedJourneys property. Initializing to empty array.`);
    }

    return {
      player: player,
      locations: locations,
      edges: edges,
      faceUpTravel: this.faceUpTravel || [],
      faceUpEvent: this.faceUpEvent || [],
      faceUpJourneyInner: this.faceUpJourneyInner || [],
      faceUpJourneyOuter: this.faceUpJourneyOuter || [],
      locationOm: this.locationOm || {},
      locationCubes: this.locationCubes || {},
      isMyTurn: this.players[this.turnIndex]?.id === playerId,
      opponents: this.players
        .filter(p => p.id !== playerId)
        .map(p => {
          // Find opponent's position on the Om track
          let opponentOmSpace = 0;
          let opponentStackPosition = 0;

          if (this.omTrack) {
            for (let space = this.omTrack.length - 1; space >= 0; space--) {
              const stack = this.omTrack[space] || [];
              const stackPos = stack.indexOf(p.id);

              if (stackPos !== -1) {
                opponentOmSpace = space;
                opponentStackPosition = stackPos;
                break;
              }
            }
          }

          return {
            id: p.id,
            name: p.name,
            position: p.position,
            handSize: (p.hand || []).length,
            journeyCards: (p.collectedJourneys || []).length,
            omTokens: {
              inner: (p.omSlotsInner || []).filter(x => x > 0).length,
              outer: (p.omSlotsOuter || []).filter(x => x > 0).length,
              temp: Array.isArray(p.omTemp) ? p.omTemp.length : (p.omTemp || 0)
            },
            // Include the total Om count for easier access
            totalOmCount: this._totalOm(p),
            // Include Om track position information
            omSpace: opponentOmSpace,
            omStackPosition: opponentStackPosition
          };
        })
    };
  }

  movePlayer(playerId, path, travelCardIds, extraHopCount, isTriathlon = false) {
    console.log(`Player ${playerId} attempting to move along path: ${path.join(' -> ')}`);
    if (!this.started) return false;
    const player = this._findPlayer(playerId);
    if (!player) return false;
    if (playerId !== this._currentPlayer().id) return false;

    // Check for "Election Campaigns" global event effect - no travel this round
    if (this.currentGlobalEvent?.effect === 'double_trade_no_travel') {
      console.log('Movement blocked by Election Campaigns global event');
      return false;
    }

    if (!Array.isArray(path) || path.length < 2) return false;

    // Determine the starting location's region
    const startLocation = locations.find(loc => loc.id === path[0]);
    const startRegion = startLocation ? startLocation.region : null;

    // Apply region-specific effects that modify movement costs
    let hopMultiplier = 1; // Default: no multiplier

    // Check if any region-based events are active that affect movement costs
    if (!isTriathlon && startRegion) {
      // Frozen North event - double movement cost if starting in North
      if (this.currentGlobalEvent?.effect === 'frozen_north' && startRegion === 'North') {
        hopMultiplier = 2;
        console.log(`Applied Frozen North effect: Movement cost doubled for starting in North region`);
      }

      // Himalayan NE event - double movement cost if starting in Northeast
      if (this.currentGlobalEvent?.effect === 'himalayan_ne' && startRegion === 'Northeast') {
        hopMultiplier = 2;
        console.log(`Applied Himalayan NE effect: Movement cost doubled for starting in Northeast region`);
      }
    }

    // Special handling for triathlon movement
    // We don't apply certain restrictions for triathlon moves
    if (!isTriathlon) {
      // Apply "Drizzle of Delay" global event effect - max 2 moves
      if (this.currentGlobalEvent?.effect === 'max_moves_2_and_cost_artha_north_east') {
        // Limit to max 2 hops
        if (path.length > 3) { // path.length - 1 = hops
          console.log(`Limiting move to 2 hops due to Drizzle of Delay global event`);
          path = path.slice(0, 3); // Limit to start point + 2 more locations
        }
      }
    }

    // Check for "Turbulent Skies" global event - no airport travel
    if (this.currentGlobalEvent?.effect === 'no_airport_travel') {
      // Check if the path contains airport-to-airport travel
      for (let i = 0; i < path.length - 1; i++) {
        const currentLoc = locations.find(l => l.id === path[i]);
        const nextLoc = locations.find(l => l.id === path[i + 1]);

        if (currentLoc && nextLoc &&
            currentLoc.journeyType === 'Airport' &&
            nextLoc.journeyType === 'Airport') {
          console.log('Airport travel blocked by Turbulent Skies global event');
          return false;
        }
      }
    }

    // Add a path length limit to prevent exponential memory growth
    const MAX_PATH_LENGTH = 15;
    if (path.length > MAX_PATH_LENGTH) {
      console.log(`Path exceeds maximum length (${path.length} > ${MAX_PATH_LENGTH}). Truncating.`);
      // Only keep start point and last MAX_PATH_LENGTH-1 locations
      path = [path[0], ...path.slice(-(MAX_PATH_LENGTH-1))];
    }

    const totalHops = path.length - 1;

    // Sum of chosen travel cards
    let sumTravel = 0;
    const usedCards = [];

    if (!travelCardIds || travelCardIds.length === 0) {
      console.log('No travel cards provided');
      return false;
    }

    travelCardIds.forEach((cid) => {
      const c = player.hand.find((h) => h.id === cid && h.value);
      if (c) {
        sumTravel += c.value;
        usedCards.push(c);
      }
    });
    console.log(`Travel values: sum=${sumTravel}, hops=${totalHops}, extraHops=${extraHopCount}`);

    // Grab any extra hop cards the player wants to use
    const usedExtraHops = [];
    if (extraHopCount && extraHopCount > 0) {
      // Find extraHop cards in the hand
      const extraHopCards = player.hand.filter((h) => h.type === 'extraHop');
      console.log('extraHopCards in hand:', extraHopCards.length);
      let remainingExtraHops = extraHopCount;
      for (const card of extraHopCards) {
        if (remainingExtraHops <= 0) break;

        // Use this card
        usedExtraHops.push(card);
        remainingExtraHops -= card.value;
      }

      // Adjust if the player tried to use more than they have
      if (remainingExtraHops > 0) {
        extraHopCount -= remainingExtraHops;
      }
    }

    // For triathlon movement, we allow the special case of using 3 cards (1, 2, and 3) for a 6-hop path
    if (isTriathlon) {
      // Check if the player is using exactly 3 cards with values 1, 2, and 3
      const cardValues = usedCards.map(card => card.value);
      const hasAllThree = cardValues.includes(1) && cardValues.includes(2) && cardValues.includes(3);

      if (hasAllThree && totalHops === 6) {
        // For triathlon, we allow the special case of 6 hops with 3 cards (1+2+3)
        console.log('Triathlon movement: using cards 1, 2, and 3 for a 6-hop path');
      } else {
        console.log('Invalid triathlon movement');
        return false;
      }
    } else {
      // Validate the path length against the cards used for non-triathlon moves
      // Apply hop multiplier for region-based events
      // For Himalayan NE, the UI shows half the distance but the server expects double the travel cards
      // So we need to check if totalHops * hopMultiplier = sumTravel + extraHopCount
      if (totalHops * hopMultiplier !== sumTravel + extraHopCount) {
        console.log('Invalid path length:', totalHops, 'with multiplier', hopMultiplier,
        'requires', totalHops * hopMultiplier, 'but got', sumTravel + extraHopCount);
        return false;
      }
    }

    // Remove used travel cards from hand
    usedCards.forEach((card) => {
      player.hand = player.hand.filter((h) => h.id !== card.id);
      this.travelDiscard.push(card);
    });

    // Remove used extra hop cards
    usedExtraHops.forEach((card) => {
      player.hand = player.hand.filter((h) => h.id !== card.id);
      this.eventDiscard.push(card);
    });

    // Check if each hop in the path is valid (i.e. the locations are connected)
    // Validate each step in the path
    let valid = true;
    for (let i = 0; i < path.length - 1; i++) {
      if (!this._edgeExists(path[i], path[i + 1])) {
        console.log('Invalid edge in path between', path[i], 'and', path[i + 1]);
        valid = false;
        break;
      }
    }
    if (!valid) return false;

    // Move the player
    player.position = path[path.length - 1];

    // Track used travel cards for global event effects
    // Initialize travelCardsUsed array if it doesn't exist
    if (!player.travelCardsUsed) {
      player.travelCardsUsed = [];
    }

    // Initialize travelCardVehicles set if it doesn't exist
    if (!player.travelCardVehicles) {
      player.travelCardVehicles = new Set();
    }

    // Track airport visits for Parikrama in the Clouds event
    if (this.currentGlobalEvent?.effect === 'parikrama_in_clouds_reward') {
      // Initialize airportsVisited set if it doesn't exist
      if (!player.airportsVisited) {
        player.airportsVisited = new Set();
      }

      // Add each airport in the path to the set
      for (let i = 0; i < path.length; i++) {
        const locationId = path[i];
        const location = locations.find(loc => loc.id === locationId);
        if (location && location.journeyType === 'Airport') {
          player.airportsVisited.add(locationId);
        }
      }

      console.log(`Player ${player.name} has visited ${player.airportsVisited.size} airports this round`);
    }

    // Special handling for triathlon movement
    if (isTriathlon && this.currentGlobalEvent?.effect === 'triathlon_bonus') {
      // When using triathlon movement, explicitly add 1, 2, and 3 to ensure the bonus is awarded
      player.travelCardsUsed = [1, 2, 3];
      console.log('Triathlon movement: explicitly tracking travel cards 1, 2, and 3 for bonus');
      // Player gets 7 outer points for triathlon
      player.outerScore += 7;
    } else {
      // Regular movement - add the actual values of used travel cards
      player.travelCardsUsed.push(...usedCards.map(card => card.value));

      // Track which vehicle types were used for travel-specific global events
      usedCards.forEach(card => {
        if (card.vehicle) {
          player.travelCardVehicles.add(card.vehicle);
        }
      });

      // Check if any travel-specific global events are active
      this._applyTravelCardEffects(player, usedCards);
    }

    // Apply region-based event effects now that movement is complete
    this._applyRegionBasedEffects(player, startRegion, path, usedCards);

    // Pickup cubes along the way
    for (let i = 1; i < path.length; i++) { // Skip starting position
      const loc = path[i];

      // Apply "Diwali Distraction" effect - no cube pickup
      if (this.currentGlobalEvent?.effect === 'gain_5_inner_no_cube_pickup') {
        // Skip cube pickup
        continue;
      }

      // Only pick up at the final destination (the last location in the path)
      if (i === path.length - 1) {
        // Normal cube pickup
        const cube = this.locationCubes[loc];
        if (cube && player.energyCubes.length < 5) {
          // Player picks up the cube
          player.energyCubes.push(cube);
          // Remove the cube from the location
          delete this.locationCubes[loc];
        }
      }
    }

    // Pick up Om Tokens at the endpoint
    if (this.locationOm[player.position] && player.omTemp.length < 3) {
      player.omTemp.push(1);
      this._advanceOmTrack(playerId, 1);
      this.locationOm[player.position] = Math.max(0, this.locationOm[player.position] - 1);
      if (this.locationOm[player.position] === 0) {
        delete this.locationOm[player.position];
      }
    }

    // Check if player landed on a Jyotirlinga (locations 49-60)
    const endLocation = locations.find(l => l.id === player.position);
    const isJyotirlinga = endLocation && endLocation.id >= 49 && endLocation.id <= 60;

    // Apply "Maha Kumbh" global event effect - bonus for visiting Jyotirlinga
    if (this.currentGlobalEvent?.effect === 'jyotirlinga_7_inner_or_bonus_cube') {
      if (isJyotirlinga) {
        // Player gets 7 inner points for visiting Jyotirlinga
        player.innerScore += 7;
      } else {
        // Player gets a random energy cube
        const randomCube = this._getRandomEnergyFromPile();
        if (randomCube && player.energyCubes.length < 5) {
          player.energyCubes.push(randomCube);
        }
      }
    }

    // Record the move event
    this._recordEvent('move', playerId, {
      path,
      travelCards: usedCards.map(c => c.id),
      extraHopCards: usedExtraHops.map(c => c.id)
    });

    return true;
  }

  _edgeExists(a, b) {
    return edges.some(
      (e) => (e.from === a && e.to === b) || (e.from === b && e.to === a)
    ) || (a >= 61 && a <= 66 && b >= 61 && b <= 66); // Allow direct airport-to-airport travel
  }

  pickCards(playerId, { type, pickFromFaceUp, pickFromTop }) {
    if (!this.started) return;
    const player = this._findPlayer(playerId);
    if (!player) return;
    if (playerId !== this._currentPlayer().id) return;

    if (type !== 'travel' && type !== 'event') return;

    // Check if player is already at the hand limit (standard or reduced by global events)
    const handLimit = this.currentRoundHandLimit || 4;
    if (player.hand.length >= handLimit) {
      console.log(`Player ${player.name} is already at the hand limit of ${handLimit}`);
      return;
    }

    console.log(`Player ${player.name} is picking ${type} cards. Selected IDs:`, pickFromFaceUp, `Pick from top: ${pickFromTop ? 'yes' : 'no'}`);
    console.log(`Before - FaceUpTravel:`, this.faceUpTravel.map(c => c.id));

    const pickedCards = [];

    // Initialize card pick count if not present
    if (!player.cardPickCount) {
      player.cardPickCount = 0;
    }

    if (type === 'travel') {
      let needed = 2 - player.cardPickCount;

      // Don't allow more than 2 cards total
      if (needed <= 0) return;

      // Make sure we don't exceed the current hand limit
      const maxNewCards = handLimit - player.hand.length;
      needed = Math.min(needed, maxNewCards);

      if (needed <= 0) {
        console.log(`Player ${player.name} cannot pick more cards due to hand limit of ${handLimit}`);
        return;
      }

      // Handle picking from face-up cards
      const chosenIds = pickFromFaceUp || [];
      if (chosenIds.length > needed) return;

      chosenIds.forEach((cid) => {
        if (needed > 0 && player.hand.length < handLimit) {
          const idx = this.faceUpTravel.findIndex((c) => c.id === cid);
          if (idx !== -1) {
            console.log(`Removing card ${cid} from faceUpTravel`);
            pickedCards.push(this.faceUpTravel[idx]);
            player.hand.push(this.faceUpTravel[idx]);
            this.faceUpTravel.splice(idx, 1);
            needed--;
            player.cardPickCount++;

            // Immediately refill after each card is picked
            this._refillFaceUp('travel');
          }
        }
      });

      // Record cards picked from face-up display
      if (pickedCards.length > 0) {
        this._recordEvent('PICK_FACE_UP_CARDS', playerId, {
          cardType: 'travel',
          pickedCards,
          pickedFromFaceUp: true
        });
      }

      // Pick from top of deck if requested
      if (pickFromTop && needed > 0 && this.travelDeck.length > 0 && player.hand.length < handLimit) {
        const top = this.travelDeck.pop();
        player.hand.push(top);
        needed--;
        player.cardPickCount++;

        // Record event card drawn from deck
        this._recordEvent('PICK_DECK_CARDS', playerId, {
          cardType: 'travel',
          drawnCards: [top],
          pickFromTop: true
        });
      }

      // Check if player has picked all allowed cards for this turn
      if (player.cardPickCount >= 2) {
        player.cardPickCount = 0; // Reset for next turn
      }

      this._refillFaceUp('travel');
      console.log(`After - FaceUpTravel:`, this.faceUpTravel.map(c => c.id));
    } else if (type === 'event') {
      // Event card handling
      let needed = 1;

      // Make sure we don't exceed the current hand limit
      const maxNewCards = handLimit - player.hand.length;
      needed = Math.min(needed, maxNewCards);

      if (needed <= 0) {
        console.log(`Player ${player.name} cannot pick more cards due to hand limit of ${handLimit}`);
        return;
      }

      // Handle picking from face-up
      const chosenIds = pickFromFaceUp || [];
      if (chosenIds.length > 1) return;

      if (chosenIds.length === 1) {
        const cid = chosenIds[0];
        const idx = this.faceUpEvent.findIndex((c) => c.id === cid);
        if (idx !== -1 && player.hand.length < handLimit) {
          pickedCards.push(this.faceUpEvent[idx]);
          player.hand.push(this.faceUpEvent[idx]);
          this.faceUpEvent.splice(idx, 1);
          needed--;

          // Record event card picked from face-up display
          this._recordEvent('PICK_FACE_UP_CARDS', playerId, {
            cardType: 'event',
            pickedCards,
            pickedFromFaceUp: true
          });
        }
      }

      // Pick from top of deck if requested
      if (pickFromTop && needed === 1 && this.eventDeck.length > 0 && player.hand.length < handLimit) {
        const top = this.eventDeck.pop();
        player.hand.push(top);
        needed--;

        // Record event card drawn from deck
        this._recordEvent('PICK_DECK_CARDS', playerId, {
          cardType: 'event',
          drawnCards: [top],
          pickFromTop: true
        });
      }

      this._refillFaceUp('event');
    }

    // Return success with the picked cards for animation
    return {
      success: true,
      pickedCards: pickedCards
    };
  }

  _refillFaceUp(deckType) {
    // Remove excessive logs
    if (deckType === 'travel') {
      // If the deck is empty and discard has cards, shuffle discard into deck
      if (this.travelDeck.length === 0 && this.travelDiscard.length > 0) {
        console.log(`Travel deck empty, reshuffling ${this.travelDiscard.length} discarded cards`);
        this.travelDeck = shuffle([...this.travelDiscard]);
        this.travelDiscard = [];
      }

      while (this.faceUpTravel.length < 4 && this.travelDeck.length > 0) {
        this.faceUpTravel.push(this.travelDeck.pop());
      }
    }
    else if (deckType === 'event') {
      // If the deck is empty and discard has cards, shuffle discard into deck
      if (this.eventDeck.length === 0 && this.eventDiscard.length > 0) {
        console.log(`Event deck empty, reshuffling ${this.eventDiscard.length} discarded cards`);
        this.eventDeck = shuffle([...this.eventDiscard]);
        this.eventDiscard = [];
      }

      while (this.faceUpEvent.length < 4 && this.eventDeck.length > 0) {
        this.faceUpEvent.push(this.eventDeck.pop());
      }
    }
    else if (deckType === 'journeyOuter') {
      // If the deck is empty and discard has cards, shuffle discard into deck
      if (this.journeyDeckOuter.length === 0 && this.journeyDiscardOuter.length > 0) {
        console.log(`Outer journey deck empty, reshuffling ${this.journeyDiscardOuter.length} discarded cards`);
        this.journeyDeckOuter = shuffle([...this.journeyDiscardOuter]);
        this.journeyDiscardOuter = [];
      }

      while (this.faceUpJourneyOuter.length < 4 && this.journeyDeckOuter.length > 0) {
        this.faceUpJourneyOuter.push(this.journeyDeckOuter.pop());
      }
    }
    else if (deckType === 'journeyInner') {
      // If the deck is empty and discard has cards, shuffle discard into deck
      if (this.journeyDeckInner.length === 0 && this.journeyDiscardInner.length > 0) {
        console.log(`Inner journey deck empty, reshuffling ${this.journeyDiscardInner.length} discarded cards`);
        this.journeyDeckInner = shuffle([...this.journeyDiscardInner]);
        this.journeyDiscardInner = [];
      }

      while (this.faceUpJourneyInner.length < 4 && this.journeyDeckInner.length > 0) {
        this.faceUpJourneyInner.push(this.journeyDeckInner.pop());
      }
    }
  }

  collectJourney(playerId, journeyCardId, journeyType) {
    if (!this.started) {
      console.log('Game not started');
      return false;
    }

    const player = this._findPlayer(playerId);
    if (!player) {
      console.log('Player not found');
      return false;
    }

    if (playerId !== this._currentPlayer().id) {
      console.log('Not your turn');
      return false;
    }

    // Apply "Drought of Spirits" global event effect - no inner journey cards
    if (this.currentGlobalEvent?.effect === 'no_inner_journey_cards' && journeyType === 'inner') {
      console.log('Cannot collect inner journey cards due to Drought of Spirits');
      return false;
    }

    // Apply "Heritage Site Renovations" global event effect - no outer journey cards
    if (this.currentGlobalEvent?.effect === 'no_outer_journey_cards' && journeyType === 'outer') {
      console.log('Cannot collect outer journey cards due to Heritage Site Renovations');
      return false;
    }

    // Find the journey card from the face-up cards
    let journeyDeck, journeyDiscard, faceUpJourney;
    if (journeyType === 'outer') {
      journeyDeck = this.journeyDeckOuter;
      journeyDiscard = this.journeyDiscardOuter;
      faceUpJourney = this.faceUpJourneyOuter;
    } else if (journeyType === 'inner') {
      journeyDeck = this.journeyDeckInner;
      journeyDiscard = this.journeyDiscardInner;
      faceUpJourney = this.faceUpJourneyInner;
    } else {
      console.log('Invalid journey type');
      return false;
    }

    // Find card by id
    const cardIndex = faceUpJourney.findIndex((c) => c.id === journeyCardId);
    if (cardIndex === -1) {
      console.log('Card not found in face-up cards');
      return false;
    }

    const card = faceUpJourney[cardIndex];

    // Verify the player is at the correct location
    if (player.position !== card.locationId) {
      console.log('Player not at the card location', player.position, card.locationId);
      return false;
    }

    // Count how many journey cards of this type the player already has
    const journeySlots = journeyType === 'outer' ? player.omSlotsOuter : player.omSlotsInner;
    const filledSlots = journeySlots.filter((v) => v > 0).length;

    if (filledSlots >= journeySlots.length) {
      console.log('No more slots available');
      return false;
    }

    // Check energy cube requirements directly - no wild cubes
    const requiredCubes = card.required || {};
    const playerCubes = {};

    // Count player's energy cubes by type
    if (Array.isArray(player.energyCubes)) {
      for (const cube of player.energyCubes) {
        playerCubes[cube] = (playerCubes[cube] || 0) + 1;
      }
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      Object.assign(playerCubes, player.energyCubes);
    }

    // Check if player has enough energy cubes
    for (const [type, amount] of Object.entries(requiredCubes)) {
      if ((playerCubes[type] || 0) < amount) {
        console.log(`Not enough ${type} cubes. Required: ${amount}, Available: ${playerCubes[type] || 0}`);
        return false;
      }
    }

    // Check om token requirement based on which slot this will go in
    let omRequirement;
    if (this.players.length === 4) {
      // Special OM cost sequence for 4 players: 0-1-1-2
      omRequirement = [0, 1, 1, 2][filledSlots];
    } else {
      // Standard OM cost sequence for 2-3 players: 1-1-2-3
      omRequirement = [1, 1, 2, 3][filledSlots];
    }

    if (player.omTemp.length < omRequirement) {
      console.log('Not enough Om tokens, have', player.omTemp.length, 'need', omRequirement);
      return false;
    }

    // Spend energy cubes directly - no wild cubes
    if (Array.isArray(player.energyCubes)) {
      // For array format
      for (const [type, amount] of Object.entries(requiredCubes)) {
        let remaining = amount;
        while (remaining > 0) {
          const idx = player.energyCubes.indexOf(type);
          if (idx !== -1) {
            player.energyCubes.splice(idx, 1);
            remaining--;
          } else {
            console.log(`Warning: Could not find ${type} cube despite counting it earlier`);
            break;
          }
        }
      }
    } else if (typeof player.energyCubes === 'object' && player.energyCubes !== null) {
      // For object format
      for (const [type, amount] of Object.entries(requiredCubes)) {
        player.energyCubes[type] = Math.max(0, (player.energyCubes[type] || 0) - amount);
      }
    }

    // Return the spent cubes to the energy cube pile
    this._returnCubesToPile([...Object.entries(requiredCubes).flatMap(([type, count]) =>
      Array(count).fill(type)
    )]);

    // Spend Om tokens
    for (let i = 0; i < omRequirement; i++) {
      player.omTemp.pop();
    }

    // Place Om tokens on the journey slot
    if (journeyType === 'outer') {
      player.omSlotsOuter[filledSlots] = omRequirement;
    } else {
      player.omSlotsInner[filledSlots] = omRequirement;
    }

    // Remove the card from face-up cards
    faceUpJourney.splice(cardIndex, 1);

    // Add the card to the player's collected journeys
    player.collectedJourneys.push(card);

    // Add points from the card
    if (journeyType === 'outer') {
      player.outerScore += card.reward.outer;
    } else {
      player.innerScore += card.reward.inner;
    }

    // Apply "Bountiful Bhandara" global event effect - bonus outer points
    if (this.currentGlobalEvent?.effect === 'draw_2_cubes_bonus_5_outer') {
      player.outerScore += 5;
    }

    // Draw a new card to replace the collected one
    if (journeyDeck.length > 0) {
      faceUpJourney.push(journeyDeck.pop());
    }

    // Record the collection event
    this._recordEvent('collectJourney', playerId, {
      journeyType,
      journeyCardId,
      omRequirement,
    });

    // Check win condition
    const isOmVictory = this._checkWinCondition();

    // If this is an immediate OM token victory, emit the game over event
    if (isOmVictory) {
      console.log(`OM token victory achieved by ${player.name} with ${this._totalOm(player)} tokens!`);
    }

    return true;
  }

  /**
   * End the current player's turn
   * @param {string} playerId - ID of the player ending their turn
   * @returns {boolean} Whether the turn was successfully ended
   */
  endTurn(playerId) {
    if (!this.started) return false;
    const player = this._findPlayer(playerId);
    if (!player) return false;
    if (playerId !== this._currentPlayer().id) return false;

    // Check for Triathlon global event bonus before resetting turn state
    if (this.currentGlobalEvent?.effect === 'triathlon_bonus' && player.travelCardsUsed) {
      console.log(`Checking triathlon bonus for ${player.name}. Travel cards used:`, player.travelCardsUsed);

      // Check if player used all three unique value travel cards (1, 2, and 3)
      const uniqueValues = new Set(player.travelCardsUsed);
      console.log(`Unique travel card values:`, Array.from(uniqueValues));

      if (uniqueValues.has(1) && uniqueValues.has(2) && uniqueValues.has(3)) {
        player.outerScore += 7;
        console.log(`Awarding triathlon bonus: +7 outer points to ${player.name}`);

        // Record the triathlon bonus event
        this._recordEvent('triathlon_bonus', playerId, {
          bonus: 7,
          usedValues: Array.from(uniqueValues)
        });
      } else {
        console.log(`No triathlon bonus: Player didn't use all three required travel card values (1, 2, and 3)`);
      }
    }

    // Check for Parikrama in the Clouds global event bonus
    if (this.currentGlobalEvent?.effect === 'parikrama_in_clouds_reward' && player.airportsVisited) {
      console.log(`Checking Parikrama in the Clouds bonus for ${player.name}. Airports visited:`,
        player.airportsVisited.size);

      // Check if player visited more than 1 airport this round
      if (player.airportsVisited.size > 1) {
        const previousInnerScore = player.innerScore;
        player.innerScore += 5;
        console.log(`Awarding Parikrama in the Clouds bonus: +5 inner points to ${player.name} for visiting multiple airports`);

        // Record the bonus event
        this._recordEvent('parikrama_in_clouds_reward', playerId, {
          previousInnerScore,
          newInnerScore: player.innerScore,
          airportsVisited: Array.from(player.airportsVisited),
          bonus: 5
        });
      } else {
        console.log(`No Parikrama in the Clouds bonus: Player didn't visit more than 1 airport this round`);
      }

      // Reset airports visited for next round
      player.airportsVisited = new Set();
    }

    // Check for region-based end of turn rewards
    if (this.currentGlobalEvent?.effect === 'himalayan_ne_end_turn_reward' ||
        this.currentGlobalEvent?.effect === 'central_heart_end_turn_reward' ||
        this.currentGlobalEvent?.effect === 'pushkar_holy_dip_end_turn_reward') {

      // Get the player's current location and region
      const playerLocation = locations.find(l => l.id === player.position);
      if (playerLocation && playerLocation.region) {
        const playerRegion = playerLocation.region.toLowerCase();

        // For Himalayan NE event
        if (this.currentGlobalEvent.effect === 'himalayan_ne_end_turn_reward' &&
            (playerRegion === 'northeast' || playerRegion === 'north east')) {
          // Award inner points for being in Northeast region
          const previousInnerScore = player.innerScore;
          player.innerScore += 5;
          console.log(`${player.name} gains +5 inner points for ending turn in Northeast during Himalayan NE event`);

          // Record the event
          this._recordEvent('himalayan_ne_reward', playerId, {
            previousInnerScore,
            newInnerScore: player.innerScore,
            region: playerRegion,
            bonus: 5
          });
        }

        // For Central Heart event
        if (this.currentGlobalEvent.effect === 'central_heart_end_turn_reward' &&
            playerRegion === 'central') {
          // Award inner points for being in Central region
          const previousInnerScore = player.innerScore;
          player.innerScore += 5;
          console.log(`${player.name} gains +5 inner points for ending turn in Central during Central Heart event`);

          // Record the event
          this._recordEvent('central_heart_reward', playerId, {
            previousInnerScore,
            newInnerScore: player.innerScore,
            region: playerRegion,
            bonus: 5
          });
        }

        // For Pushkar Holy Dip event
        if (this.currentGlobalEvent.effect === 'pushkar_holy_dip_end_turn_reward' &&
            playerRegion === 'west') {
          // Award inner points for being in West region
          const previousInnerScore = player.innerScore;
          player.innerScore += 5;
          console.log(`${player.name} gains +5 inner points for ending turn in West during Pushkar Holy Dip event`);

          // Record the event
          this._recordEvent('pushkar_holy_dip_reward', playerId, {
            previousInnerScore,
            newInnerScore: player.innerScore,
            region: playerRegion,
            bonus: 5
          });
        }
      }
    }

    // Reset heavy haul processed flag if it exists
    if (player.heavyHaulProcessed) {
      delete player.heavyHaulProcessed;
      console.log(`Reset heavyHaulProcessed flag for player ${player.name}`);
    }

    // Reset the travel cards tracking
    player.travelCardsUsed = [];
    if (player.travelCardVehicles) {
      player.travelCardVehicles = new Set();
    }

    // Reset player's turn state
    player.didMoveThisTurn = false;
    player.didSelectionActionThisTurn = false;
    player.didTradeThisTurn = false;
    player.cardPickCount = 0;

    // Update turn index
    this.turnIndex = (this.turnIndex + 1) % this.players.length;
    this.turnCount++;

    // Check if we've completed a round (all players have taken a turn)
    const newRound = this.turnIndex === 0;
    if (newRound) {
      console.log(`\n========== END OF ROUND ${this.roundCount} ==========`);
      console.log(`All players have taken a turn. Applying Om Turn Track ordering for next round.`);
      console.log(`Player order BEFORE applying Om Turn Track:`, this.players.map(p => `${p.name}(${p.id})`).join(' → '));

      // Reorder players based on Om Turn Track for next round
      this._applyOmTrackTurnOrder();
      this.roundCount++;

      // Add prominent log message for new round
      console.log(`\n==========================================`);
      console.log(`===== NEW ROUND ${this.roundCount} BEGINS =====`);
      console.log(`==========================================\n`);

      // Round analytics
      console.log(`=== ROUND ${this.roundCount} COMPLETE ===`);

      // Reset round-specific settings
      this.currentRoundHandLimit = 4; // Reset hand limit back to default

      // Create round summary
      const roundSummary = this._createRoundSummary();
      // console.log('Round Summary:', roundSummary);

      // Add the round summary to the array
      this.roundSummaries.push(roundSummary);

      // Log the state of all players at the beginning of each round
      console.log(`PLAYER SUMMARY FOR ROUND ${this.roundCount}:`);
      this.players.forEach(player => {
        const handSize = player.hand ? player.hand.length : 0;
        const travelCards = player.hand ? player.hand.filter(card => card.type === 'travel' || card.cardType === 'travel').length : 0;
        const omTemp = player.omTemp ? player.omTemp.length : 0;
        const omSlotsOuter = player.omSlotsOuter ? player.omSlotsOuter.filter(slot => slot > 0).length : 0;
        const omSlotsInner = player.omSlotsInner ? player.omSlotsInner.filter(slot => slot > 0).length : 0;

        // Count energy cubes by type
        const cubeCounts = {
          bhakti: 0,
          gnana: 0,
          karma: 0,
          artha: 0
        };

        if (player.energyCubes && Array.isArray(player.energyCubes)) {
          player.energyCubes.forEach(cube => {
            if (cubeCounts[cube] !== undefined) {
              cubeCounts[cube]++;
            }
          });
        }

        const totalEnergyCubes = cubeCounts.bhakti + cubeCounts.gnana + cubeCounts.karma + cubeCounts.artha;

        console.log(`Player ${player.name}: Position=${player.position}, Hand=${handSize} (${travelCards} travel), ` +
                    `OM Temp=${omTemp}, OM Slots=[${omSlotsOuter}/${omSlotsInner}], ` +
                    `Energy=${totalEnergyCubes} (Bhakti=${cubeCounts.bhakti}, Gnana=${cubeCounts.gnana}, Karma=${cubeCounts.karma}, Artha=${cubeCounts.artha}), ` +
                    `Scores=[${player.outerScore}/${player.innerScore}]`);
      });

      // Log the face-up travel cards
      if (this.faceUpTravel && this.faceUpTravel.length > 0) {
        const travelCardDetails = this.faceUpTravel.map(card => {
          return `T${card.id}(${card.value}${card.vehicle ? `:${card.vehicle}` : ''})`;
        }).join(', ');
        console.log(`Face-up travel cards: ${travelCardDetails}`);
      } else {
        console.log('Face-up travel cards: None');
      }

      // Log the face-up journey cards
      if (this.faceUpJourneyOuter && this.faceUpJourneyOuter.length > 0) {
        const journeyOuterDetails = this.faceUpJourneyOuter.map(card => {
          // Format required cubes if they exist
          let cubeReq = '';
          if (card.requiredCubes) {
            cubeReq = Object.entries(card.requiredCubes)
              .filter(([_, count]) => count > 0)
              .map(([type, count]) => `${type}=${count}`)
              .join(',');
            cubeReq = cubeReq ? ` (${cubeReq})` : '';
          }

          // Add points information
          const points = card.reward && card.reward.outer ? card.reward.outer : 0;
          return `JO${card.id}@${card.locationId}${cubeReq} [${points}pts]`;
        }).join(', ');
        console.log(`Face-up outer journey cards: ${journeyOuterDetails}`);
      } else {
        console.log('Face-up outer journey cards: None');
      }

      if (this.faceUpJourneyInner && this.faceUpJourneyInner.length > 0) {
        const journeyInnerDetails = this.faceUpJourneyInner.map(card => {
          // Format required cubes if they exist
          let cubeReq = '';
          if (card.requiredCubes) {
            cubeReq = Object.entries(card.requiredCubes)
              .filter(([_, count]) => count > 0)
              .map(([type, count]) => `${type}=${count}`)
              .join(',');
            cubeReq = cubeReq ? ` (${cubeReq})` : '';
          }

          // Add points information
          const points = card.reward && card.reward.inner ? card.reward.inner : 0;
          return `JI${card.id}@${card.locationId}${cubeReq} [${points}pts]`;
        }).join(', ');
        console.log(`Face-up inner journey cards: ${journeyInnerDetails}`);
      } else {
        console.log('Face-up inner journey cards: None');
      }
      console.log('');

      // Draw a new global event card at the start of each round
      this._drawGlobalEventCard();

      // Apply global event effects for this round
      this._applyGlobalEventEffect();
    }

    // Check for game over conditions
    if (this._omTokenVictory || (this._finalRound && this.turnIndex === this._finalRoundEnd)) {
      // Game is over, check the winner
      this._isGameOver();
    }

    // Record the turn end event with additional data
    this._recordEvent('endTurn', playerId, {
      nextPlayerId: this.players[this.turnIndex].id,
      newRound,
      roundCount: this.roundCount,
      turnCount: this.turnCount
    });

    // Emit turnChanged event for bot processing
    this.emit('turnChanged', this.players[this.turnIndex].id);

    return true;
  }

  _checkWinCondition() {
    const current = this._currentPlayer();
    const sumScores = current.outerScore + current.innerScore;
    const totalOm = this._totalOm(current);


    // Check for score threshold (triggers final round)
    if (!this._finalRound && sumScores >= 100) {
      this._finalRound = true;
      this._finalRoundStarter = this.turnIndex;
      let endIndex = this.turnIndex - 1;
      if (endIndex < 0) endIndex = this.players.length - 1;
      this._finalRoundEnd = endIndex;

      // Record final round trigger event
      this._recordEvent('FINAL_ROUND_TRIGGERED', current.id, {
        winCondition: 'SCORE_THRESHOLD',
        playerScore: sumScores,
        finalRoundStarter: this._finalRoundStarter,
        finalRoundEnd: this._finalRoundEnd
      });
    }

    return false;
  }

  _isGameOver() {
    // Check for immediate OM token victory
    if (this._omTokenVictory) {
      const winner = this._findPlayer(this._omTokenVictor);
      if (winner) {
        // Record game over event for OM token victory
        this._recordEvent('GAME_OVER', 'system', {
          winner: {
            id: winner.id,
            name: winner.name,
            outerScore: winner.outerScore,
            innerScore: winner.innerScore,
            totalScore: winner.outerScore + winner.innerScore,
            omTotal: this._totalOm(winner),
            winByOm: true,
            winByScore: false
          },
          winCondition: 'OM_THRESHOLD',
          totalRounds: this.roundCount,
          players: this.players.map(p => ({
            id: p.id,
            name: p.name,
            outerScore: p.outerScore,
            innerScore: p.innerScore,
            totalScore: p.outerScore + p.innerScore,
            omTotal: this._totalOm(p)
          }))
        });

        // Emit game completion event for server shutdown
        this.emit('gameCompleted', {
          winner,
          winCondition: 'OM_THRESHOLD',
          totalRounds: this.roundCount
        });
      }
      return true;
    }

    // Check for final round completion (score threshold victory)
    if (!this._finalRound) return false;
    const isOver = this.turnIndex === (this._finalRoundEnd + 1) % this.players.length;

    if (isOver) {
      const winner = this.getGameResult();
      if (winner) {
        // Record game over event for score threshold victory
        this._recordEvent('GAME_OVER', 'system', {
          winner: {
            id: winner.id,
            name: winner.name,
            outerScore: winner.outerScore,
            innerScore: winner.innerScore,
            totalScore: winner.outerScore + winner.innerScore,
            omTotal: this._totalOm(winner),
            winByOm: false,
            winByScore: true
          },
          winCondition: 'SCORE_THRESHOLD',
          totalRounds: this.roundCount,
          players: this.players.map(p => ({
            id: p.id,
            name: p.name,
            outerScore: p.outerScore,
            innerScore: p.innerScore,
            totalScore: p.outerScore + p.innerScore,
            omTotal: this._totalOm(p)
          }))
        });

        // Emit game completion event for server shutdown
        this.emit('gameCompleted', {
          winner,
          winCondition: 'SCORE_THRESHOLD',
          totalRounds: this.roundCount
        });

        // End the server
        // process.exit(0);
      }
    }

    return isOver;
  }

  getGameResult() {
    // Determine OM token threshold based on number of players
    // 4 for 4 players, 5 for 3 players, 7 for 2 players
    const omTokenThreshold = this.players.length === 4 ? 4 : (this.players.length === 3 ? 5 : 7);

    const overPlayers = this.players.filter(
      (p) => p.outerScore + p.innerScore >= 100 || this._totalOm(p) >= omTokenThreshold
    );
    if (overPlayers.length === 0) {
      return null;
    }
    let best = null;
    let bestMetric = -999;
    for (let p of overPlayers) {
      const sumScores = p.outerScore + p.innerScore;
      const diff = sumScores - 100;
      const om = this._totalOm(p);
      let metric = diff + (om >= omTokenThreshold ? om * 0.5 : 0);
      if (!best || metric > bestMetric) {
        best = p;
        bestMetric = metric;
      }
    }
    return best;
  }

  _totalOm(player) {
    const sumOuter = player.omSlotsOuter.reduce((a, b) => a + b, 0);
    const sumInner = player.omSlotsInner.reduce((a, b) => a + b, 0);
    return player.omTemp.length + sumOuter + sumInner;
  }

  // Method to handle trading energy cubes based on player's character card
  tradeEnergyCubes(playerId, selectedCubes) {
    const player = this._findPlayer(playerId);
    if (!player || !player.character) return false;

    // Check if player already traded this turn
    if (player.didTradeThisTurn) return false;

    // Verify the player has the selected cubes
    const playerCubeTypes = player.energyCubes.map(cube => cube.toLowerCase());
    for (const cubeType of selectedCubes) {
      const index = playerCubeTypes.indexOf(cubeType);
      if (index === -1) return false; // Player doesn't have this cube
    }

    // Check if the selected cubes are valid for trading
    const character = player.character;
    if (!character.ability) return false;

    // Check that all selected cubes are allowed by the character's ability
    const validCubeTypes = character.ability.takes;
    if (!selectedCubes.every(cube => validCubeTypes.includes(cube))) {
      return false; // Invalid cube type for this character
    }

    // Check count of cubes (need 1 for trade)
    if (selectedCubes.length !== 1) {
      return false; // Need exactly 1 cube for a trade
    }

    // Remove the traded cubes from player's inventory
    selectedCubes.forEach(cubeType => {
      const index = player.energyCubes.findIndex(
        c => c.toLowerCase() === cubeType.toLowerCase()
      );
      if (index !== -1) {
        player.energyCubes.splice(index, 1);
        // Return the cube to the pile
        this.energyCubePile[cubeType.toLowerCase()]++;
      }
    });

    // Determine what the player gets in return
    const receivedCubeType = character.ability.gives;

    // Check if the "Election Campaigns" global event is active (double yield)
    const tradeMultiplier = this.currentGlobalEvent?.effect === 'double_trade_no_travel' ? 2 : 1;

    // Add the received cube(s) to the player's inventory
    for (let i = 0; i < tradeMultiplier; i++) {
      // Make sure player has room for the new cube
      if (player.energyCubes.length < 5) {
        player.energyCubes.push(receivedCubeType);
        // Remove the cube from the pile
        this.energyCubePile[receivedCubeType]--;
      }
    }

    // Check if any character-specific trade events are active
    this._applyCharacterTradeRewards(player, receivedCubeType);

    // Mark that the player has traded this turn
    player.didTradeThisTurn = true;

    // Record the trade event
    this._recordEvent('trade', playerId, {
      cubesTraded: selectedCubes,
      cubeReceived: receivedCubeType,
      count: tradeMultiplier
    });

    return true;
  }

  /**
   * Apply character-specific trade rewards based on active global event
   * @param {Object} player - The player who completed the trade
   * @param {string} receivedCubeType - The type of cube received from the trade
   */
  _applyCharacterTradeRewards(player, receivedCubeType) {
    if (!this.currentGlobalEvent || !player.character) return;

    const effect = this.currentGlobalEvent.effect;
    const characterType = player.character.type.toLowerCase();

    switch (effect) {
      case 'merchants_midas_reward':
        if (characterType === 'merchant' && receivedCubeType.toLowerCase() === 'artha') {
          player.outerScore += 7;
          console.log(`Applied Merchant's Midas reward: +7 outer points to ${player.name} for trading for artha`);
          this._recordEvent('character_trade_reward', player.id, {
            character: 'merchant',
            cubeReceived: 'artha',
            effect: 'merchants_midas_reward',
            outerPoints: 7
          });
        }
        break;

      case 'professors_insight_reward':
        if (characterType === 'professor' && receivedCubeType.toLowerCase() === 'gnana') {
          player.innerScore += 7;
          console.log(`Applied Professor's Insight reward: +7 inner points to ${player.name} for trading for gnana`);
          this._recordEvent('character_trade_reward', player.id, {
            character: 'professor',
            cubeReceived: 'gnana',
            effect: 'professors_insight_reward',
            innerPoints: 7
          });
        }
        break;

      case 'pilgrims_grace_reward':
        if (characterType === 'pilgrim' && receivedCubeType.toLowerCase() === 'bhakti') {
          player.innerScore += 7;
          console.log(`Applied Pilgrim's Grace reward: +7 inner points to ${player.name} for trading for bhakti`);
          this._recordEvent('character_trade_reward', player.id, {
            character: 'pilgrim',
            cubeReceived: 'bhakti',
            effect: 'pilgrims_grace_reward',
            innerPoints: 7
          });
        }
        break;

      case 'engineers_precision_reward':
        if (characterType === 'engineer' && receivedCubeType.toLowerCase() === 'karma') {
          player.outerScore += 7;
          console.log(`Applied Engineer's Precision reward: +7 outer points to ${player.name} for trading for karma`);
          this._recordEvent('character_trade_reward', player.id, {
            character: 'engineer',
            cubeReceived: 'karma',
            effect: 'engineers_precision_reward',
            outerPoints: 7
          });
        }
        break;
    }
  }

  // Method to prepare the game state for saving to a file
  saveState() {
    const state = {
      players: this.players.map((p) => ({
        id: p.id,
        name: p.name,
        position: p.position,
        hand: p.hand,
        energyCubes: p.energyCubes,
        omTemp: p.omTemp,
        omSlotsOuter: p.omSlotsOuter,
        omSlotsInner: p.omSlotsInner,
        collectedJourneys: p.collectedJourneys,
        outerScore: p.outerScore,
        innerScore: p.innerScore,
        didMoveThisTurn: p.didMoveThisTurn,
        didSelectionActionThisTurn: p.didSelectionActionThisTurn,
        didTradeThisTurn: p.didTradeThisTurn,
        character: p.character,
      })),
      started: this.started,
      turnIndex: this.turnIndex,
      roundCount: this.roundCount,
      travelDeck: this.travelDeck,
      eventDeck: this.eventDeck,
      journeyDeckInner: this.journeyDeckInner,
      journeyDeckOuter: this.journeyDeckOuter,
      travelDiscard: this.travelDiscard,
      eventDiscard: this.eventDiscard,
      journeyInnerDiscard: this.journeyDiscardInner,
      journeyOuterDiscard: this.journeyDiscardOuter,
      faceUpTravel: this.faceUpTravel,
      faceUpEvent: this.faceUpEvent,
      faceUpJourneyInner: this.faceUpJourneyInner,
      faceUpJourneyOuter: this.faceUpJourneyOuter,
      locationCubes: this.locationCubes,
      locationOm: this.locationOm,
      finalRound: this._finalRound,
      finalRoundStarter: this._finalRoundStarter,
      finalRoundEnd: this._finalRoundEnd,
      omTokenVictory: this._omTokenVictory,
      omTokenVictor: this._omTokenVictor,
      gameEvents: this.gameEvents,
      roundSummaries: this.roundSummaries,
      characterDeck: this.characterDeck,
      energyCubePile: this.energyCubePile,
      currentGlobalEvent: this.currentGlobalEvent,
      globalEventDeck: this.globalEventDeck,
      globalEventDiscard: this.globalEventDiscard,
      // Save game settings
      nameMode: this.nameMode,
    };
    return state;
  }

  // Method to load the game state from a saved state
  loadState(loadedState, io) {
    this.players = loadedState.players.map((p) => ({
      ...p,
      id: p.id, // will be re-mapped
    }));
    this.started = loadedState.started;
    this.turnIndex = loadedState.turnIndex;
    this.roundCount = loadedState.roundCount;
    this.travelDeck = loadedState.travelDeck || [];
    this.eventDeck = loadedState.eventDeck || [];
    this.journeyDeckInner = loadedState.journeyDeckInner || [];
    this.journeyDeckOuter = loadedState.journeyDeckOuter || [];
    this.travelDiscard = loadedState.travelDiscard || [];
    this.eventDiscard = loadedState.eventDiscard || [];
    this.journeyDiscardInner = loadedState.journeyInnerDiscard || [];
    this.journeyDiscardOuter = loadedState.journeyOuterDiscard || [];
    this.roundSummaries = loadedState.roundSummaries || [];
    this.faceUpTravel = loadedState.faceUpTravel || [];
    this.faceUpEvent = loadedState.faceUpEvent || [];
    this.faceUpJourneyInner = loadedState.faceUpJourneyInner || [];
    this.faceUpJourneyOuter = loadedState.faceUpJourneyOuter || [];
    this.locationCubes = loadedState.locationCubes || {};
    this.locationOm = loadedState.locationOm || {};
    this._finalRound = loadedState.finalRound || false;
    this._finalRoundStarter = loadedState.finalRoundStarter || null;
    this._finalRoundEnd = loadedState.finalRoundEnd || null;
    this._omTokenVictory = loadedState.omTokenVictory || false;
    this._omTokenVictor = loadedState.omTokenVictor || null;
    this.gameEvents = loadedState.gameEvents || [];
    this.characterDeck = loadedState.characterDeck || [];
    this.energyCubePile = loadedState.energyCubePile || { artha: 2, karma: 2, gnana: 2, bhakti: 2 };

    // Load the global event deck and current global event as-is without reshuffling
    // since they were already shuffled when the state was saved
    this.currentGlobalEvent = loadedState.currentGlobalEvent || null;
    this.globalEventDeck = loadedState.globalEventDeck || [];
    this.globalEventDiscard = loadedState.globalEventDiscard || [];

    // Load game settings
    this.nameMode = loadedState.nameMode || false;

    // Ensure all players have character cards (for backwards compatibility)
    this.players.forEach(player => {
      if (!player.character) {
        player.character = characterCards.find(c => c.type === 'Merchant');
      }
    });

    // Record game resumption event
    this._recordEvent('GAME_RESUMED', 'system', {
      timestamp: Date.now(),
      loadedStateRoundCount: this.roundCount,
      playerCount: this.players.length
    });

    // Re-assign socket IDs based on player names
    if (io) {
      io.sockets.sockets.forEach((socket) => {
        const player = this.players.find((p) => p.name === socket.playerName);
        if (player) {
          player.id = socket.id;
        }
      });
    }
  }

  /**
   * Create a round summary object with player states and face-up cards
   * @returns {Object} Round summary object
   * @private
   */
  _createRoundSummary() {
    // Get the previous round summary if available
    const previousRoundSummary = this.roundSummaries.length > 0
      ? this.roundSummaries[this.roundSummaries.length - 1]
      : null;

    // Create player summaries
    const playerSummaries = this.players.map(player => {
      const handSize = player.hand ? player.hand.length : 0;
      const travelCards = player.hand ? player.hand.filter(card => card.type === 'travel' || card.cardType === 'travel').length : 0;
      const omTemp = player.omTemp ? player.omTemp.length : 0;
      const omSlotsOuter = player.omSlotsOuter ? player.omSlotsOuter.filter(slot => slot > 0).length : 0;
      const omSlotsInner = player.omSlotsInner ? player.omSlotsInner.filter(slot => slot > 0).length : 0;

      // Count energy cubes by type
      const cubeCounts = {
        bhakti: 0,
        gnana: 0,
        karma: 0,
        artha: 0
      };

      if (player.energyCubes && Array.isArray(player.energyCubes)) {
        player.energyCubes.forEach(cube => {
          if (cubeCounts[cube] !== undefined) {
            cubeCounts[cube]++;
          }
        });
      }

      const totalEnergyCubes = cubeCounts.bhakti + cubeCounts.gnana + cubeCounts.karma + cubeCounts.artha;

      // Find the player's previous state in the last round summary
      let gained = null;
      if (previousRoundSummary) {
        const previousPlayerState = previousRoundSummary.players.find(p => p.id === player.id);
        if (previousPlayerState) {
          // Calculate gained OM tokens in temporary slot
          const gainedOmTemp = omTemp > previousPlayerState.omTemp ? omTemp - previousPlayerState.omTemp : null;

          // Calculate gained energy cubes
          let gainedEnergyCubes = {
            total: totalEnergyCubes > previousPlayerState.energyCubes.total ? totalEnergyCubes - previousPlayerState.energyCubes.total : null,
            bhakti: cubeCounts.bhakti > previousPlayerState.energyCubes.bhakti ? cubeCounts.bhakti - previousPlayerState.energyCubes.bhakti : null,
            gnana: cubeCounts.gnana > previousPlayerState.energyCubes.gnana ? cubeCounts.gnana - previousPlayerState.energyCubes.gnana : null,
            karma: cubeCounts.karma > previousPlayerState.energyCubes.karma ? cubeCounts.karma - previousPlayerState.energyCubes.karma : null,
            artha: cubeCounts.artha > previousPlayerState.energyCubes.artha ? cubeCounts.artha - previousPlayerState.energyCubes.artha : null
          };

          // If all energy cube gains are null, set the entire object to null
          if (gainedEnergyCubes.total === null &&
              gainedEnergyCubes.bhakti === null &&
              gainedEnergyCubes.gnana === null &&
              gainedEnergyCubes.karma === null &&
              gainedEnergyCubes.artha === null) {
            gainedEnergyCubes = null;
          }

          // Calculate gained journey cards (use collectedJourneys array length)
          const currentJourneyCount = (player.collectedJourneys || []).length;
          // The previous player state might have collectedJourneys as an array or as a number
          let previousJourneyCount = 0;
          if (typeof previousPlayerState.collectedJourneys === 'number') {
            previousJourneyCount = previousPlayerState.collectedJourneys;
          } else if (Array.isArray(previousPlayerState.collectedJourneys)) {
            previousJourneyCount = previousPlayerState.collectedJourneys.length;
          }
          const gainedJourneyCards = currentJourneyCount > previousJourneyCount ?
            currentJourneyCount - previousJourneyCount : null;

          // Set the gained object if anything was gained
          if (gainedOmTemp !== null || gainedEnergyCubes !== null || gainedJourneyCards !== null) {
            gained = {
              omTemp: gainedOmTemp,
              energyCubes: gainedEnergyCubes,
              journeyCards: gainedJourneyCards
            };
          }
        }
      }

      return {
        id: player.id,
        name: player.name,
        position: player.position,
        hand: {
          total: handSize,
          travel: travelCards
        },
        omTemp: omTemp,
        omSlots: {
          outer: omSlotsOuter,
          inner: omSlotsInner
        },
        energyCubes: {
          total: totalEnergyCubes,
          bhakti: cubeCounts.bhakti,
          gnana: cubeCounts.gnana,
          karma: cubeCounts.karma,
          artha: cubeCounts.artha
        },
        scores: {
          outer: player.outerScore,
          inner: player.innerScore,
          total: player.outerScore + player.innerScore
        },
        collectedJourneys: (player.collectedJourneys || []).length,
        gained: gained
      };
    });

    // Create face-up travel card summary
    const travelCardSummary = this.faceUpTravel ? this.faceUpTravel.map(card => {
      return {
        id: card.id,
        value: card.value,
        vehicle: card.vehicle
      };
    }) : [];

    // Create face-up journey card summaries
    const journeyOuterSummary = this.faceUpJourneyOuter ? this.faceUpJourneyOuter.map(card => {
      // Format required cubes if they exist
      const requiredCubes = card.requiredCubes || {};
      const points = card.reward && card.reward.outer ? card.reward.outer : 0;

      return {
        id: card.id,
        locationId: card.locationId,
        requiredCubes,
        points
      };
    }) : [];

    const journeyInnerSummary = this.faceUpJourneyInner ? this.faceUpJourneyInner.map(card => {
      // Format required cubes if they exist
      const requiredCubes = card.requiredCubes || {};
      const points = card.reward && card.reward.inner ? card.reward.inner : 0;

      return {
        id: card.id,
        locationId: card.locationId,
        requiredCubes,
        points
      };
    }) : [];

    // Create the round summary object
    return {
      roundNumber: this.roundCount,
      timestamp: Date.now(),
      players: playerSummaries,
      faceUpCards: {
        travel: travelCardSummary,
        journeyOuter: journeyOuterSummary,
        journeyInner: journeyInnerSummary
      },
      globalEvent: this.currentGlobalEvent ? {
        id: this.currentGlobalEvent.id,
        name: this.currentGlobalEvent.name,
        effect: this.currentGlobalEvent.effect
      } : null
    };
  }

  // Helper to record game events
  _recordEvent(type, playerId, data) {
    const player = this._findPlayer(playerId);
    const playerName = player ? player.name : 'System';

    const event = {
      type,
      playerId,
      playerName,
      roundCount: this.roundCount,
      turnIndex: this.turnIndex,
      timestamp: Date.now(),
      data
    };

    this.gameEvents.push(event);

    // Limit the size of the event history to prevent memory exhaustion
    if (this.gameEvents.length > MAX_EVENTS_HISTORY) {
      // Keep only the most recent events
      this.gameEvents = this.gameEvents.slice(-MAX_EVENTS_HISTORY);
    }

    // Log the event (optional)
    // console.log(`Game Event: ${event.type}`, event);

    return event;
  }

  // Add this new method to deal 2 random travel cards to each player
  _dealInitialTravelCards() {
    for (let player of this.players) {
      // Deal 2 random travel cards to each player
      for (let i = 0; i < 2; i++) {
        if (this.travelDeck.length > 0) {
          const card = this.travelDeck.pop();
          player.hand.push(card);

          // Record event for card dealing
          this._recordEvent('DEAL_INITIAL_CARD', player.id, {
            cardType: 'travel',
            card: card
          });
        }
      }
    }
  }

  // Draw a new global event card and apply effects to start of round
  _drawGlobalEventCard(isLoadingState = false) {
    // If we're loading a saved state, don't modify deck
    if (!isLoadingState && this.globalEventDeck.length > 0) {
      // Move current global event to discard if it exists
      if (this.currentGlobalEvent) {
        this.globalEventDiscard.push(this.currentGlobalEvent);
      }

      // Draw a new card from the top of the deck
      this.currentGlobalEvent = this.globalEventDeck.shift();

      console.log(`Drew new global event: ${this.currentGlobalEvent.name} (${this.currentGlobalEvent.effect})`);

      // Record the event draw
      this._recordEvent('global_event_drawn', 'system', {
        eventId: this.currentGlobalEvent.id,
        eventName: this.currentGlobalEvent.name,
        eventEffect: this.currentGlobalEvent.effect
      });

      // Reset any Heavy Haul processing flags from previous round
      this.players.forEach(player => {
        if (player.heavyHaulProcessed) {
          delete player.heavyHaulProcessed;
          console.log(`Reset heavyHaulProcessed flag for player ${player.name} on new round`);
        }
      });
    }

    return this.currentGlobalEvent;
  }

  // Apply effects of the current global event card
  _applyGlobalEventEffect() {
    if (!this.currentGlobalEvent) return;

    console.log(`Applying global event effect: ${this.currentGlobalEvent.name} (${this.currentGlobalEvent.effect})`);

    // Apply immediate effects (those that happen at the beginning of a round)
    switch (this.currentGlobalEvent.effect) {
      case 'spirit_of_seva':
        // Leader on each track (inner and outer) donates 3 points to player with lowest score on that track
        console.log('Applying Spirit of Seva global event effect');

        // Only apply if there are at least 2 players
        if (this.players.length >= 2) {
          // Collect donation data to display in modal
          const donations = {
            inner: { from: null, to: null },
            outer: { from: null, to: null }
          };

          // Process inner track
          let innerLeader = null;
          let innerLowest = null;

          this.players.forEach(player => {
            // Find leader (highest inner score)
            if (!innerLeader || player.innerScore > innerLeader.innerScore) {
              innerLeader = player;
            }

            // Find lowest inner score
            if (!innerLowest || player.innerScore < innerLowest.innerScore) {
              innerLowest = player;
            }
          });

          // Process outer track
          let outerLeader = null;
          let outerLowest = null;

          this.players.forEach(player => {
            // Find leader (highest outer score)
            if (!outerLeader || player.outerScore > outerLeader.outerScore) {
              outerLeader = player;
            }

            // Find lowest outer score
            if (!outerLowest || player.outerScore < outerLowest.outerScore) {
              outerLowest = player;
            }
          });

          // Execute inner track donation if leader and lowest are different players
          if (innerLeader && innerLowest && innerLeader.id !== innerLowest.id) {
            const previousInnerLeaderScore = innerLeader.innerScore;
            const previousInnerLowestScore = innerLowest.innerScore;

            // Subtract 3 points from leader (min 0)
            innerLeader.innerScore = Math.max(0, innerLeader.innerScore - 3);

            // Add 3 points to lowest
            innerLowest.innerScore += 3;

            // Store donation data
            donations.inner.from = {
              id: innerLeader.id,
              name: innerLeader.name,
              previousScore: previousInnerLeaderScore,
              newScore: innerLeader.innerScore
            };

            donations.inner.to = {
              id: innerLowest.id,
              name: innerLowest.name,
              previousScore: previousInnerLowestScore,
              newScore: innerLowest.innerScore
            };

            console.log(`Inner track: ${innerLeader.name} donated 3 points to ${innerLowest.name}`);
            console.log(`${innerLeader.name}: ${previousInnerLeaderScore} -> ${innerLeader.innerScore}`);
            console.log(`${innerLowest.name}: ${previousInnerLowestScore} -> ${innerLowest.innerScore}`);

            // Record the donation event
            this._recordEvent('spirit_of_seva_inner', innerLeader.id, {
              donorName: innerLeader.name,
              previousDonorScore: previousInnerLeaderScore,
              newDonorScore: innerLeader.innerScore,
              recipientId: innerLowest.id,
              recipientName: innerLowest.name,
              previousRecipientScore: previousInnerLowestScore,
              newRecipientScore: innerLowest.innerScore,
              pointsDonated: 3
            });
          } else {
            console.log('Inner track: No donation possible (same player as leader and lowest)');
          }

          // Execute outer track donation if leader and lowest are different players
          if (outerLeader && outerLowest && outerLeader.id !== outerLowest.id) {
            const previousOuterLeaderScore = outerLeader.outerScore;
            const previousOuterLowestScore = outerLowest.outerScore;

            // Subtract 3 points from leader (min 0)
            outerLeader.outerScore = Math.max(0, outerLeader.outerScore - 3);

            // Add 3 points to lowest
            outerLowest.outerScore += 3;

            // Store donation data
            donations.outer.from = {
              id: outerLeader.id,
              name: outerLeader.name,
              previousScore: previousOuterLeaderScore,
              newScore: outerLeader.outerScore
            };

            donations.outer.to = {
              id: outerLowest.id,
              name: outerLowest.name,
              previousScore: previousOuterLowestScore,
              newScore: outerLowest.outerScore
            };

            console.log(`Outer track: ${outerLeader.name} donated 3 points to ${outerLowest.name}`);
            console.log(`${outerLeader.name}: ${previousOuterLeaderScore} -> ${outerLeader.outerScore}`);
            console.log(`${outerLowest.name}: ${previousOuterLowestScore} -> ${outerLowest.outerScore}`);

            // Record the donation event
            this._recordEvent('spirit_of_seva_outer', outerLeader.id, {
              donorName: outerLeader.name,
              previousDonorScore: previousOuterLeaderScore,
              newDonorScore: outerLeader.outerScore,
              recipientId: outerLowest.id,
              recipientName: outerLowest.name,
              previousRecipientScore: previousOuterLowestScore,
              newRecipientScore: outerLowest.outerScore,
              pointsDonated: 3
            });
          } else {
            console.log('Outer track: No donation possible (same player as leader and lowest)');
          }

          // Emit event with donation data to show modal to all clients
          if (this.emit) {
            this.emit('spiritOfSevaExecuted', {
              donations,
              eventName: this.currentGlobalEvent.name,
              eventEffect: this.currentGlobalEvent.effect
            });
          }
        } else {
          console.log('Not enough players to apply Spirit of Seva effect');
        }
        break;

      case 'gain_5_inner_no_cube_pickup':
        // All players gain 5 inner points
        console.log(`Applying Diwali Distraction effect to ${this.players.length} players`);
        this.players.forEach(player => {
          const previousScore = player.innerScore;
          player.innerScore += 5;
          console.log(`Player ${player.name}: Inner score increased from ${previousScore} to ${player.innerScore}`);

          // Record the event for each player
          this._recordEvent('diwali_distraction_points', player.id, {
            previousInnerScore: previousScore,
            newInnerScore: player.innerScore,
            pointsGained: 5
          });
        });
        console.log(`Applied Diwali Distraction: All players gained +5 inner points`);
        break;

      case 'draw_2_cubes_bonus_5_outer':
        // Each player draws 2 random energy cubes
        this.players.forEach(player => {
          // Draw up to 2 cubes if there's space
          const cubesDrawn = [];
          for (let i = 0; i < 2; i++) {
            if (player.energyCubes.length < 5) {
              const randomCube = this._getRandomEnergyFromPile();
              if (randomCube) {
                player.energyCubes.push(randomCube);
                cubesDrawn.push(randomCube);
              }
            }
          }
          console.log(`Player ${player.name} drew cubes: ${cubesDrawn.join(', ') || 'none'}`);
        });
        console.log(`Applied Bountiful Bhandara effect: Players drew energy cubes`);
        break;

      case 'riots_discard':
        // Discard 1 energy cube of each type if player has 2 or more of that type
        console.log(`Applying Riots effect: Players must discard excess cubes and OM tokens`);

        this.players.forEach(player => {
          console.log(`Checking Riots effect for player ${player.name}:`);

          // Count cubes by type
          const cubeCounts = {
            artha: 0,
            karma: 0,
            gnana: 0,
            bhakti: 0
          };

          player.energyCubes.forEach(cube => {
            if (ENERGY_TYPES.includes(cube)) {
              cubeCounts[cube]++;
            }
          });

          console.log(`Player ${player.name} energy cubes: ${JSON.stringify(cubeCounts)}`);

          // Discard excess cubes
          const discardedCubes = [];
          ENERGY_TYPES.forEach(cubeType => {
            if (cubeCounts[cubeType] >= 2) {  // Changed from > 2 to >= 2
              // Find the first cube of this type to remove
              const cubeIndex = player.energyCubes.findIndex(c => c === cubeType);
              if (cubeIndex !== -1) {
                // Remove the cube and return it to the pile
                const removedCube = player.energyCubes.splice(cubeIndex, 1)[0];
                this.energyCubePile[removedCube]++;
                discardedCubes.push(removedCube);

                // Record the discard event
                this._recordEvent('riots_discard_cube', player.id, {
                  cubeType: removedCube
                });
              }
            }
          });

          if (discardedCubes.length > 0) {
            console.log(`Player ${player.name} discarded cubes: ${discardedCubes.join(', ')}`);
          } else {
            console.log(`Player ${player.name} didn't need to discard any cubes`);
          }

          // Discard 1 om token in jyotirlinga of player's current region if they have 2 or more
          console.log(`Player ${player.name} has ${player.omTemp.length} OM tokens`);
          if (player.omTemp.length >= 2) {  // Changed from > 2 to >= 2
            // Find the region of the player's current position
            const currentLocation = locations.find(l => l.id === player.position);
            if (currentLocation) {
              const currentRegion = currentLocation.region;
              console.log(`Player ${player.name} is in region ${currentRegion}`);

              // Find all jyotirlingas in this region
              const regionJyotirlingas = locations.filter(l =>
                l.region === currentRegion &&
                l.id >= 49 && l.id <= 60
              );

              console.log(`Found ${regionJyotirlingas.length} Jyotirlingas in region ${currentRegion}`);

              // If there are jyotirlingas in this region
              if (regionJyotirlingas.length > 0) {
                // Find a jyotirlinga without an om token
                const availableJyotirlinga = regionJyotirlingas.find(j =>
                  !this.locationOm[j.id] || this.locationOm[j.id] === 0
                );

                if (availableJyotirlinga) {
                  // Remove one om token from player and add to the jyotirlinga
                  player.omTemp.pop();
                  this.locationOm[availableJyotirlinga.id] = (this.locationOm[availableJyotirlinga.id] || 0) + 1;

                  console.log(`Player ${player.name} discarded 1 OM token to Jyotirlinga ${availableJyotirlinga.id}`);

                  // Record the om discard event
                  this._recordEvent('riots_discard_om', player.id, {
                    jyotirlinga: availableJyotirlinga.id
                  });
                } else {
                  console.log(`All Jyotirlingas in region ${currentRegion} already have OM tokens, player keeps extra tokens`);
                }
                // If all jyotirlingas already have om tokens, player doesn't need to discard
              } else {
                console.log(`No Jyotirlingas found in region ${currentRegion} for player to discard OM token`);
              }
            } else {
              console.log(`Could not find location data for player ${player.name} at position ${player.position}`);
            }
          }
        });
        break;

      case 'om_meditation':
        // Gain 1 om token from the first jyotirlinga with an OM token in player's current region
        console.log('Applying Om Meditation global event effect');

        // Process players in turn order
        const playersInTurnOrder = [...this.players];
        // Start from the current turn index and wrap around
        const orderedPlayers = [
          ...playersInTurnOrder.slice(this.turnIndex),
          ...playersInTurnOrder.slice(0, this.turnIndex)
        ];

        console.log(`Processing players in turn order, starting with player at index ${this.turnIndex}`);

        orderedPlayers.forEach(player => {
          // Only proceed if player has room for more om tokens (max 3)
          if (player.omTemp.length < 3) {
            // Find the region of the player's current position
            const currentLocation = locations.find(l => l.id === player.position);
            if (currentLocation) {
              console.log(`Player ${player.name} is at location ${player.position} in region ${currentLocation.region}`);

              const currentRegion = currentLocation.region;

              // Find all jyotirlingas in this region
              const regionJyotirlingas = locations.filter(l =>
                l.region === currentRegion &&
                l.id >= 49 && l.id <= 60
              );

              console.log(`Found ${regionJyotirlingas.length} Jyotirlingas in region ${currentRegion}`);

              if (regionJyotirlingas.length > 0) {
                // Find the first jyotirlinga with an OM token
                const jyotirlingaWithOm = regionJyotirlingas.find(jyotirlinga =>
                  this.locationOm[jyotirlinga.id]
                );

                if (jyotirlingaWithOm) {
                  console.log(`Found Jyotirlinga ${jyotirlingaWithOm.id} with OM token for player ${player.name}`);

                  // Add om token to player
                  player.omTemp.push(1);
                  this._advanceOmTrack(player.id, 1);

                  // Remove the OM token from the Jyotirlinga
                  this.locationOm[jyotirlingaWithOm.id] = Math.max(0, this.locationOm[jyotirlingaWithOm.id] - 1);
                  if (this.locationOm[jyotirlingaWithOm.id] === 0) {
                    delete this.locationOm[jyotirlingaWithOm.id];
                  }

                  // Record the om meditation event
                  this._recordEvent('om_meditation_gain', player.id, {
                    jyotirlinga: jyotirlingaWithOm.id,
                    region: currentRegion
                  });

                  console.log(`Player ${player.name} gained an OM token from Jyotirlinga ${jyotirlingaWithOm.id}, now has ${player.omTemp.length} OM tokens`);
                } else {
                  console.log(`No Jyotirlinga with OM token found in region ${currentRegion} for player ${player.name}`);

                  // Record that player didn't receive an OM token
                  this._recordEvent('om_meditation_no_gain', player.id, {
                    reason: 'no_om_token_available',
                    region: currentRegion
                  });
                }
              } else {
                console.log(`No Jyotirlingas found in region ${currentRegion} for player ${player.name}`);

                // Record that player didn't receive an OM token
                this._recordEvent('om_meditation_no_gain', player.id, {
                  reason: 'no_jyotirlinga_in_region',
                  region: currentRegion
                });
              }
            } else {
              console.log(`Could not find location data for player ${player.name} at position ${player.position}`);

              // Record that player didn't receive an OM token
              this._recordEvent('om_meditation_no_gain', player.id, {
                reason: 'invalid_location',
                position: player.position
              });
            }
          } else {
            console.log(`Player ${player.name} already has max OM tokens (${player.omTemp.length})`);

            // Record that player didn't receive an OM token
            this._recordEvent('om_meditation_no_gain', player.id, {
              reason: 'max_om_tokens',
              currentOmCount: player.omTemp.length
            });
          }
        });
        break;

      case 'excess_baggage':
        // Enforce hand limit of 2 this round, players must discard down immediately
        console.log('Applying Excess Baggage global event effect');

        this.players.forEach(player => {
          const travelCards = player.hand.filter(card => card.type === 'travel');
          console.log(`Player ${player.name} has ${travelCards.length} travel cards in hand`);

          // Check if player needs to discard cards
          if (travelCards.length > 2) {
            const cardsToDiscard = travelCards.length - 2;
            console.log(`Player ${player.name} needs to discard ${cardsToDiscard} travel cards due to Excess Baggage`);

            // Set pending selection for player to choose which travel cards to discard
            player.pendingCardSelection = {
              type: 'travel_card_loss',
              effect: 'excess_baggage',
              reason: 'hand_limit',
              count: cardsToDiscard
            };

            // Record the event
            this._recordEvent('excess_baggage_hand_limit', player.id, {
              currentHandSize: travelCards.length,
              cardsToDiscard: cardsToDiscard
            });
          } else {
            console.log(`Player ${player.name} is under the hand limit, no action needed`);
          }
        });

        // Also set the global hand limit for the round
        this.currentRoundHandLimit = 2;
        console.log('Set round hand limit to 2 for Excess Baggage effect');
        break;
    }

    // Notify that game state has been updated due to global event effect
    this.emit('gameStateUpdated');
  }

  // Helper method to get random energy cube from pile
  _getRandomEnergyFromPile() {
    // Create an array of available cube types
    const availableCubes = [];
    for (const [type, count] of Object.entries(this.energyCubePile)) {
      if (count > 0) {
        for (let i = 0; i < count; i++) {
          availableCubes.push(type);
        }
      }
    }

    if (availableCubes.length === 0) return null;

    // Select a random cube
    const randomIndex = Math.floor(Math.random() * availableCubes.length);
    const selectedCube = availableCubes[randomIndex];

    // Decrease the count in the pile
    this.energyCubePile[selectedCube]--;

    return selectedCube;
  }

  // Method to add an energy cube to the player
  _addEnergyCubeToPlayer(player, cubeType) {
    if (!player || !cubeType) return false;

    // Check if player has room for more cubes
    if (player.energyCubes.length >= 5) return false;

    player.energyCubes.push(cubeType);
    return true;
  }

  // Method to return energy cubes to the pile
  _returnCubesToPile(cubes) {
    if (!cubes || !Array.isArray(cubes)) return;

    cubes.forEach(cube => {
      if (ENERGY_TYPES.includes(cube)) {
        this.energyCubePile[cube]++;
      }
    });
  }

  // Helper method to find the shortest path between two locations
  _findShortestPath(startId, endId) {
    if (startId === endId) return [startId];

    // Keep track of visited nodes to avoid cycles
    const visited = new Set([startId]);

    // Queue for BFS - each entry is [nodeId, path]
    const queue = [[startId, [startId]]];

    while (queue.length > 0) {
      const [currentId, path] = queue.shift();

      // Get all connected locations from edges
      const connectedLocations = [];
      for (const edge of edges) {
        if (edge.from === currentId && !visited.has(edge.to)) {
          connectedLocations.push(edge.to);
        }
        if (edge.to === currentId && !visited.has(edge.from)) {
          connectedLocations.push(edge.from);
        }
      }

      // Check each connected location
      for (const nextId of connectedLocations) {
        // Create new path
        const newPath = [...path, nextId];

        // If we've reached the destination, return the path
        if (nextId === endId) {
          return newPath;
        }

        // Mark as visited and add to queue
        visited.add(nextId);
        queue.push([nextId, newPath]);
      }
    }

    // No path found
    return null;
  }

  /**
   * Apply effects based on travel cards used when specific global events are active
   * @param {Object} player - The player who used the travel cards
   * @param {Array} usedCards - The travel cards used for the movement
   */
  _applyTravelCardEffects(player, usedCards) {
    // Return early if no global event or no travel cards
    if (!this.currentGlobalEvent || !usedCards || usedCards.length === 0) return;

    // Get the effect from the current global event
    const effect = this.currentGlobalEvent.effect;

    usedCards.forEach(card => {
      switch (effect) {
        // New consolidated rewards
        case 'eco_trail_reward':
          if (card.vehicle === 'cycle') {
            player.innerScore += 5;
            console.log(`Applied Eco Trail reward: +5 inner points to ${player.name} for using a cycle card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'cycle',
              effect: 'eco_trail_reward',
              innerPoints: 5
            });
          } else if (card.vehicle === 'trek') {
            player.innerScore += 5;
            console.log(`Applied Eco Trail reward: +5 inner points to ${player.name} for using a trek card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'trek',
              effect: 'eco_trail_reward',
              innerPoints: 5
            });
          }
          break;

        case 'rajput_caravans_reward':
          if (card.vehicle === 'horse') {
            player.outerScore += 5;
            console.log(`Applied Rajput Caravans reward: +5 outer points to ${player.name} for using a horse card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'horse',
              effect: 'rajput_caravans_reward',
              outerPoints: 5
            });
          } else if (card.vehicle === 'camel') {
            player.outerScore += 5;
            console.log(`Applied Rajput Caravans reward: +5 outer points to ${player.name} for using a camel card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'camel',
              effect: 'rajput_caravans_reward',
              outerPoints: 5
            });
          }
          break;

        case 'urban_ride_reward':
          if (card.vehicle === 'motorbike') {
            player.outerScore += 5;
            console.log(`Applied Urban Ride reward: +5 outer points to ${player.name} for using a motorbike card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'motorbike',
              effect: 'urban_ride_reward',
              outerPoints: 5
            });
          } else if (card.vehicle === 'rickshaw') {
            player.outerScore += 5;
            console.log(`Applied Urban Ride reward: +5 outer points to ${player.name} for using a rickshaw card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'rickshaw',
              effect: 'urban_ride_reward',
              outerPoints: 5
            });
          }
          break;

        case 'road_warriors_reward':
          if (card.vehicle === 'car') {
            player.outerScore += 5;
            console.log(`Applied Road Warriors reward: +5 outer points to ${player.name} for using a car card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'car',
              effect: 'road_warriors_reward',
              outerPoints: 5
            });
          } else if (card.vehicle === 'bus') {
            player.outerScore += 5;
            console.log(`Applied Road Warriors reward: +5 outer points to ${player.name} for using a bus card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'bus',
              effect: 'road_warriors_reward',
              outerPoints: 5
            });
          }
          break;

        case 'rails_and_sails_reward':
          if (card.vehicle === 'train') {
            player.outerScore += 5;
            console.log(`Applied Rails and Sails reward: +5 outer points to ${player.name} for using a train card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'train',
              effect: 'rails_and_sails_reward',
              outerPoints: 5
            });
          } else if (card.vehicle === 'boat') {
            player.outerScore += 5;
            console.log(`Applied Rails and Sails reward: +5 outer points to ${player.name} for using a boat card`);
            this._recordEvent('travel_card_reward', player.id, {
              vehicle: 'boat',
              effect: 'rails_and_sails_reward',
              outerPoints: 5
            });
          }
          break;

        case 'heavy_haul_reward':
          if (card.vehicle === 'truck') {
            // Check if the player has already processed Heavy Haul cube selection
            if (player.heavyHaulProcessed) {
              console.log(`Skipping Heavy Haul cube removal for ${player.name} as it was already processed manually`);
              return;
            }

            // Award 10 outer points
            player.outerScore += 10;
            console.log(`Applied Heavy Haul reward: +10 outer points to ${player.name} for using a truck card`);

            // If player has 3 or more cubes, we'll let them choose which cubes to discard
            if (player.energyCubes.length >= 3) {
              // Store the pending reward for later when the player selects which cubes to discard
              player.pendingHeavyHaulReward = {
                cardId: card.id,
                outerPoints: 10, // Already awarded
                needsSelection: true
              };

              console.log(`Player ${player.name} needs to select which energy cubes to discard for Heavy Haul`);
              return; // Exit early, we'll handle cube removal when player makes a selection
            }

            // If player has fewer than 3 cubes, automatically remove up to 2
            const cubesToLose = Math.min(2, player.energyCubes.length);

            if (cubesToLose > 0) {
              // Create a copy of the cubes that will be lost
              const lostCubes = [];

              for (let i = 0; i < cubesToLose; i++) {
                // Remove the cube and return it to the energy cube pile
                const cube = player.energyCubes.pop();
                lostCubes.push(cube);

                // Return the cube to the pile
                this.energyCubePile[cube.toLowerCase()]++;
              }

              console.log(`Heavy Haul cost: ${player.name} lost ${cubesToLose} energy cubes`);
              this._recordEvent('travel_card_reward', player.id, {
                vehicle: 'truck',
                effect: 'heavy_haul_reward',
                outerPoints: 10,
                lostCubes: lostCubes,
                lostCubeCount: cubesToLose
              });

              // Mark this player as having processed Heavy Haul to prevent duplicate processing
              player.heavyHaulProcessed = true;
            } else {
              this._recordEvent('travel_card_reward', player.id, {
                vehicle: 'truck',
                effect: 'heavy_haul_reward',
                outerPoints: 10,
                lostCubeCount: 0
              });

              // Mark this player as having processed Heavy Haul
              player.heavyHaulProcessed = true;
            }
          }
          break;
      }
    });
  }

  /**
   * Handle the selection of energy cubes to discard for the Heavy Haul event
   * @param {string} playerId - The ID of the player selecting cubes
   * @param {Array} selectedCubes - The cubes selected to discard
   * @param {Array} travelCardIds - The travel card IDs used for the move
   * @returns {boolean} - Whether the operation was successful
   */
  handleHeavyHaulCubeSelection(playerId, selectedCubes, travelCardIds) {
    console.log(`Processing Heavy Haul cube selection for player ${playerId}`);

    const player = this._findPlayer(playerId);
    if (!player) {
      console.log(`Heavy Haul Error: Player not found with ID ${playerId}`);
      return false;
    }

    if (!player.pendingHeavyHaulReward) {
      console.log(`Heavy Haul Error: Player ${player.name} has no pending Heavy Haul reward`);
      return false;
    }

    // Check if we have a pending Heavy Haul reward that needs cube selection
    if (!player.pendingHeavyHaulReward.needsSelection) {
      console.log(`Heavy Haul Error: Player ${player.name} has a pending reward but it doesn't need selection`);
      return false;
    }

    console.log(`Player ${player.name} has ${player.energyCubes.length} energy cubes before discard`);

    // Ensure we're selecting 2 cubes (or less if player doesn't have enough)
    const cubesToRemove = Math.min(2, selectedCubes.length);
    if (cubesToRemove < Math.min(2, player.energyCubes.length)) {
      console.log(`Heavy Haul Error: Player must select ${Math.min(2, player.energyCubes.length)} cubes, but only selected ${cubesToRemove}`);
      return false;
    }

    // Track cubes that were removed
    const removedCubes = [];

    // Sort the selected cubes by index in descending order
    // This way we remove cubes from right to left, preventing index shifting issues
    const sortedCubes = [...selectedCubes].sort((a, b) => b.index - a.index);

    console.log(`Removing cubes in order:`, sortedCubes.map(c => `${c.type} at index ${c.index}`).join(', '));

    // Remove the selected cubes
    for (const cubeInfo of sortedCubes) {
      // Find the cube by type and index
      if (player.energyCubes[cubeInfo.index] === cubeInfo.type) {
        // Remove this cube
        const removedCube = player.energyCubes.splice(cubeInfo.index, 1)[0];
        removedCubes.push(removedCube);

        // Return it to the energy cube pile
        this.energyCubePile[removedCube.toLowerCase()]++;
        console.log(`Removed ${removedCube} cube, returned to energy cube pile`);
      } else {
        console.log(`Warning: Cube at index ${cubeInfo.index} is ${player.energyCubes[cubeInfo.index] || 'not found'} but expected ${cubeInfo.type}`);
      }
    }

    console.log(`Player ${player.name} has ${player.energyCubes.length} energy cubes after discard`);

    // Record the event
    this._recordEvent('travel_card_reward', playerId, {
      vehicle: 'truck',
      effect: 'heavy_haul_reward',
      outerPoints: 10, // Already awarded when the card was played
      lostCubes: removedCubes,
      lostCubeCount: removedCubes.length,
      manualSelection: true
    });

    // Clear the pending reward and mark as processed so it won't be processed again
    delete player.pendingHeavyHaulReward;

    // Add a flag to indicate that Heavy Haul has been processed for this player
    // This will prevent the automatic cube removal from happening in _applyTravelCardEffects
    player.heavyHaulProcessed = true;

    console.log(`Player ${player.name} discarded ${removedCubes.length} energy cubes for Heavy Haul`);
    return true;
  }

  /**
   * Apply effects based on the player's starting region and end region
   * @param {Object} player - The player who moved
   * @param {string} startRegion - The region where the player started
   * @param {array} path - The path taken by the player
   * @param {array} usedCards - The travel cards used for the movement
   */
  _applyRegionBasedEffects(player, startRegion, path, usedCards) {
    if (!this.currentGlobalEvent || !startRegion) return;

    const effect = this.currentGlobalEvent.effect;

    // Get the end location region
    const endLocation = locations.find(loc => loc.id === path[path.length - 1]);
    const endRegion = endLocation ? endLocation.region : null;

    // Find the vehicles used in this movement
    const vehiclesUsed = new Set();
    usedCards.forEach(card => {
      if (card.vehicle) {
        vehiclesUsed.add(card.vehicle.toLowerCase());
      }
    });

    switch (effect) {
      case 'frozen_north':
        // If player started in North and moved, gain 7 inner points
        if (startRegion === 'North' && path.length > 1) {
          player.innerScore += 7;
          console.log(`Applied Frozen North reward: +7 inner points to ${player.name} for moving from North region`);
          this._recordEvent('region_based_reward', player.id, {
            region: 'North',
            effect: 'frozen_north',
            innerPoints: 7
          });
        }
        break;

      case 'solar_south':
        // Lose 2 energy cubes if ending in South
        if (endRegion === 'South' && player.energyCubes.length > 0) {
          // Set pending selection for player to choose which energy cubes to discard
          player.pendingCubeSelection = {
            type: 'energy_cube_loss',
            effect: 'solar_south',
            region: 'South',
            count: Math.min(2, player.energyCubes.length)
          };

          console.log(`Player ${player.name} needs to select which energy cubes to discard for Solar South effect`);
        }
        break;

      case 'himalayan_ne':
      case 'himalayan_ne_end_turn_reward':
        // For backward compatibility with older global events
        // NOTE: The new effect 'himalayan_ne_end_turn_reward' gives rewards at end of turn
        // but we keep this logic for old saved games
        if (effect === 'himalayan_ne' && startRegion === 'Northeast' && path.length > 1) {
          player.innerScore += 7;
          console.log(`Applied Himalayan NE reward: +7 inner points to ${player.name} for moving from Northeast region`);
          this._recordEvent('region_based_reward', player.id, {
            region: 'Northeast',
            effect: 'himalayan_ne',
            innerPoints: 7
          });
        }
        break;

      case 'central_heart':
      case 'central_heart_end_turn_reward':
        // For backward compatibility with older global events
        // NOTE: The new effect 'central_heart_end_turn_reward' gives rewards at end of turn
        // but we keep this logic for old saved games
        if (effect === 'central_heart' && startRegion === 'Central' && path.length > 1) {
          player.outerScore += 5;
          console.log(`Applied Central Heart reward: +5 outer points to ${player.name} for traveling from Central region`);
          this._recordEvent('region_based_reward', player.id, {
            region: 'Central',
            effect: 'central_heart',
            outerPoints: 5
          });
        }
        break;
    }
  }

  /**
   * Handle player's selection of travel cards to discard from region-based effects
   * @param {string} playerId - The ID of the player selecting cards
   * @param {Array} selectedCardIds - The IDs of the selected travel cards
   * @returns {boolean} - Whether the operation was successful
   */
  handleTravelCardSelection(playerId, selectedCardIds) {
    const player = this._findPlayer(playerId);
    if (!player || !player.pendingCardSelection) return false;

    // Ensure this is for travel card selection
    if (player.pendingCardSelection.type !== 'travel_card_loss') return false;

    // Ensure the correct number of cards are selected
    if (selectedCardIds.length !== player.pendingCardSelection.count) {
      console.log(`Expected ${player.pendingCardSelection.count} cards, but received ${selectedCardIds.length}`);
      return false;
    }

    // Extract the effect information
    const { effect, region } = player.pendingCardSelection;

    // Track the discarded cards
    const discardedCards = [];

    // Remove each selected card from the player's hand
    for (const cardId of selectedCardIds) {
      const cardIndex = player.hand.findIndex(card => card.id === cardId);
      if (cardIndex !== -1) {
        const card = player.hand.splice(cardIndex, 1)[0];
        this.travelDiscard.push(card);
        discardedCards.push(card);
      }
    }

    // Record the event
    this._recordEvent('region_based_penalty', player.id, {
      region,
      effect,
      discardedCards: discardedCards.map(card => card.id)
    });

    console.log(`Player ${player.name} discarded ${discardedCards.length} travel cards for ${effect} effect`);

    // Clear the pending selection
    delete player.pendingCardSelection;

    return true;
  }

  /**
   * Handle player's selection of energy cubes to discard from region-based effects
   * @param {string} playerId - The ID of the player selecting cubes
   * @param {Array} selectedCubes - Information about the selected cubes (type and index)
   * @returns {boolean} - Whether the operation was successful
   */
  handleEnergyCubeSelection(playerId, selectedCubes) {
    const player = this._findPlayer(playerId);
    if (!player || !player.pendingCubeSelection) return false;

    // Ensure this is for energy cube selection
    if (player.pendingCubeSelection.type !== 'energy_cube_loss') return false;

    // Ensure the correct number of cubes are selected
    if (selectedCubes.length !== player.pendingCubeSelection.count) {
      console.log(`Expected ${player.pendingCubeSelection.count} cubes, but received ${selectedCubes.length}`);
      return false;
    }

    // Extract the effect information
    const { effect, region } = player.pendingCubeSelection;

    // Sort the selected cubes by index in descending order
    // This way we remove cubes from right to left, preventing index shifting issues
    const sortedCubes = [...selectedCubes].sort((a, b) => b.index - a.index);

    // Track the removed cubes
    const removedCubes = [];

    // Remove the selected cubes
    for (const cubeInfo of sortedCubes) {
      // Find the cube by type and index
      if (player.energyCubes[cubeInfo.index] === cubeInfo.type) {
        // Remove this cube
        const removedCube = player.energyCubes.splice(cubeInfo.index, 1)[0];
        removedCubes.push(removedCube);

        // Return it to the energy cube pile
        this.energyCubePile[removedCube.toLowerCase()]++;
      }
    }

    // Record the event
    this._recordEvent('region_based_penalty', player.id, {
      region,
      effect,
      lostCubes: removedCubes
    });

    console.log(`Player ${player.name} discarded ${removedCubes.length} energy cubes for ${effect} effect`);

    // Clear the pending selection
    delete player.pendingCubeSelection;

    return true;
  }

  /**
   * Initialize the Om Turn Track stacks.
   * Each player starts at space 0 in join order.
   */
  _initializeOmTrack() {
    console.log("\n--- INITIALIZING OM TURN TRACK ---");
    this.omTrack = Array.from({ length: OM_TRACK_SPACES }, () => []);

    // Stack players in reverse order so first player is on top
    const reversedPlayers = [...this.players].reverse();
    console.log(`Stacking players on space 0 in order:`,
      reversedPlayers.map(p => `${p.name}(${p.id})`).join(' → '));

    reversedPlayers.forEach(player => {
      this.omTrack[0].push(player.id);
    });

    console.log(`Final stack at space 0:`, this.omTrack[0].map(id => {
      const p = this._findPlayer(id);
      return p ? `${p.name}(${id})` : `Unknown(${id})`;
    }).join(' ← '), `(leftmost is bottom of stack)`);

    console.log("--- OM TURN TRACK INITIALIZED ---\n");
  }

  /**
   * Advance a player's marker on the Om Turn Track.
   * Moves count spaces (with wrap) and stacks by arrival order.
   */
  _advanceOmTrack(playerId, count = 1) {
    if (!this.omTrack) return;
    const trackLength = this.omTrack.length;
    // Find player's current position
    let currentPos = this.omTrack.findIndex(stack => stack.includes(playerId));

    const player = this._findPlayer(playerId);
    const playerName = player ? player.name : 'Unknown';

    console.log(`\n--- OM TRACK ADVANCEMENT ---`);
    console.log(`Player ${playerName}(${playerId}) advancing ${count} space(s) on Om Track`);

    if (currentPos >= 0) {
      console.log(`Current position: Space ${currentPos}, stack position ${this.omTrack[currentPos].indexOf(playerId)}`);
      // Remove from old stack
      const idx = this.omTrack[currentPos].indexOf(playerId);
      if (idx !== -1) {
        this.omTrack[currentPos].splice(idx, 1);
        console.log(`Removed from space ${currentPos}, stack position ${idx}`);
      }
    } else {
      console.log(`Player not found on Om Track, assuming position 0`);
      currentPos = 0;
    }

    // Compute new position with wrap
    const newPos = (currentPos + count) % trackLength;
    console.log(`New position: Space ${newPos}`);

    // Place on top of new stack
    this.omTrack[newPos].push(playerId);
    console.log(`Added to space ${newPos}, stack position ${this.omTrack[newPos].length - 1} (TOP)`);

    // Show the updated state of the stack
    const stackPlayers = this.omTrack[newPos].map(id => {
      const p = this._findPlayer(id);
      return p ? `${p.name}(${id})` : `Unknown(${id})`;
    });
    console.log(`Updated stack at space ${newPos}: ${stackPlayers.join(' <- ')} (leftmost is bottom of stack)`);
    console.log(`---------------------------\n`);

    // Record event for tracking
    this._recordEvent('omTrackAdvanced', playerId, { newPos, count });
  }

  /**
   * Reorder players array based on Om Turn Track for next round turn order.
   * Highest space position first, then top of stack first.
   */
  _applyOmTrackTurnOrder() {
    // Add detailed logging
    console.log(`\n=======================================`);
    console.log(`DETAILED OM TURN TRACK ORDER DEBUGGING:`);
    console.log(`=======================================`);
    console.log(`Current Om Track state:`, JSON.stringify(this.omTrack));

    // Log the current player positions on the track for easier debugging
    console.log(`Current player positions on Om Track:`);
    this.omTrack.forEach((stack, spaceIndex) => {
      if (stack.length > 0) {
        const playerNames = stack.map(id => {
          const player = this._findPlayer(id);
          return player ? `${player.name}(${id})` : `Unknown(${id})`;
        });
        console.log(`Space ${spaceIndex}: ${playerNames.join(' <- ')} (leftmost is bottom of stack)`);
      }
    });

    // Build new order of player IDs: highest space first, within same space top of stack (newest arrival) first
    const orderedIds = [];
    for (let pos = this.omTrack.length - 1; pos >= 0; pos--) {
      const stack = this.omTrack[pos];
      // Skip empty spaces
      if (stack.length === 0) continue;

      console.log(`Processing space ${pos} with ${stack.length} players...`);

      // Top of stack is the LAST element (most recently pushed), so iterate from end to beginning
      for (let i = stack.length - 1; i >= 0; i--) {
        const playerId = stack[i];
        const player = this._findPlayer(playerId);
        const playerName = player ? player.name : 'Unknown';
        console.log(`  Adding player ${playerName}(${playerId}) from stack position ${i} (${i === stack.length-1 ? 'TOP' : i === 0 ? 'BOTTOM' : 'MIDDLE'} of stack)`);
        orderedIds.push(playerId);
      }
    }

    console.log(`New player order based on Om Track: ${orderedIds.map(id => {
      const player = this._findPlayer(id);
      return player ? `${player.name}(${id})` : `Unknown(${id})`;
    }).join(' -> ')}`);

    // Map IDs to player objects
    const newPlayers = [];
    orderedIds.forEach(id => {
      const p = this._findPlayer(id);
      if (p) newPlayers.push(p);
    });

    if (newPlayers.length === this.players.length) {
      // Log the turn order change for debugging
      const oldOrder = this.players.map(p => `${p.name}(${p.id})`).join(' -> ');
      const newOrder = newPlayers.map(p => `${p.name}(${p.id})`).join(' -> ');
      console.log(`TURN ORDER CHANGED FROM: ${oldOrder}`);
      console.log(`TURN ORDER CHANGED TO: ${newOrder}`);

      this.players = newPlayers;
      this.turnIndex = 0; // reset to first player in new order
      this._recordEvent('turnOrderUpdated', 'system', { order: orderedIds });
    } else {
      console.error(`ERROR: Player count mismatch after Om Track reordering. Expected ${this.players.length}, got ${newPlayers.length}`);
    }
    console.log(`=======================================\n`);
  }
}

// Shuffle helper function.
function shuffle(array) {
  let currentIndex = array.length,
    randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }
  return array;
}

module.exports = GameState;
// Also expose global event definitions
module.exports.GLOBAL_EVENT_CARDS = GLOBAL_EVENT_CARDS;