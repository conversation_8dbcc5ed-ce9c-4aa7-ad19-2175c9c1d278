/**
 * start_server.js
 * Script to start the server with memory optimizations
 *
 * Usage:
 *   node --expose-gc start_server.js
 *
 * Or import the startServer function:
 *   const { startServer } = require('./start_server');
 *   startServer(port).then(() => console.log('Server started'));
 */

// Set memory limits before loading modules
process.env.NODE_OPTIONS = '--max-old-space-size=512';

// Load the server initialization function
const { initServer } = require('./index.js');

/**
 * Start the server on the specified port
 * @param {number} port - Port number to listen on
 * @returns {Promise} Promise that resolves when server is started
 */
function startServer(port = 4000) {
  console.log('Starting server with memory optimizations!');
  console.log('- Max heap size: 512MB');
  console.log('- Garbage collection: Enabled');
  console.log('- Memory monitoring: Active');
  console.log('- Auto-shutdown: ' + (process.env.AUTO_SHUTDOWN !== 'false' ? 'Enabled' : 'Disabled'));
  console.log('  (Server will automatically shutdown 5 seconds after game completion)');
  console.log('  (Set AUTO_SHUTDOWN=false to disable this feature)');

  return initServer(port);
}

// If this file is run directly, start the server
if (require.main === module) {
  const PORT = process.env.PORT || 4000;
  startServer(PORT).catch(err => {
    console.error('Failed to start server:', err);
    process.exit(1);
  });
}

module.exports = { startServer };