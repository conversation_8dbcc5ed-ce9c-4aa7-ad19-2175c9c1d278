/**
 * Test script to verify auto-shutdown functionality
 * 
 * This script simulates a game completion to test if the server
 * automatically shuts down after the final round is completed.
 * 
 * Usage:
 *   node test_auto_shutdown.js
 */

const { initServer } = require('./index.js');

async function testAutoShutdown() {
  console.log('=== Testing Auto-Shutdown Functionality ===\n');
  
  try {
    // Start the server
    console.log('Starting server...');
    const { gameState, httpServer, io } = await initServer(4001); // Use different port for testing
    
    console.log('Server started on port 4001');
    console.log('Auto-shutdown is enabled by default\n');
    
    // Add test players
    console.log('Adding test players...');
    gameState.addPlayer('player1', 'Alice');
    gameState.addPlayer('player2', 'Bob');
    
    // Start the game
    console.log('Starting game...');
    gameState.startGame();
    
    // Simulate game progression to final round
    console.log('Simulating game progression...');
    
    // Set up a scenario where the game will end
    const player1 = gameState.players[0];
    const player2 = gameState.players[1];
    
    // Give player1 enough score to trigger final round
    player1.outerScore = 100;
    player1.innerScore = 5;
    
    // Trigger final round
    gameState._finalRound = true;
    gameState._finalRoundStarter = 0;
    gameState._finalRoundEnd = 1; // Player 2 will be the last to play
    
    console.log('Final round triggered');
    console.log(`Player 1 score: ${player1.outerScore + player1.innerScore}`);
    console.log(`Player 2 score: ${player2.outerScore + player2.innerScore}`);
    
    // Simulate the final turns
    console.log('\nSimulating final turns...');
    
    // Player 1's final turn
    gameState.turnIndex = 0;
    console.log('Player 1 takes final turn...');
    gameState.endTurn('player1');
    
    // Player 2's final turn (this should trigger game completion)
    console.log('Player 2 takes final turn (should trigger game end)...');
    gameState.endTurn('player2');
    
    console.log('\nGame should now be completed and server should shutdown in 5 seconds...');
    console.log('If auto-shutdown is working, this process will exit automatically.');
    console.log('If not, press Ctrl+C to exit manually.');
    
  } catch (error) {
    console.error('Error during test:', error);
    process.exit(1);
  }
}

// Handle manual exit
process.on('SIGINT', () => {
  console.log('\nTest interrupted by user. Exiting...');
  process.exit(0);
});

// Run the test
testAutoShutdown().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
