import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Updated layout: top row 30–70 (left to right), bottom row 70–30 (right to left)

# Define number ranges
top_row = list(range(30, 71))       # 30 to 70
bottom_row = list(range(70, 29, -1))  # 70 to 30 in reverse

# Layout settings
length = len(top_row)
box_width = 0.8
cell_spacing = 0.2
shadow_offset = 0.05
font_size = 14

# Plot setup
fig, ax = plt.subplots(figsize=(length * 0.6, 2.5), facecolor='none')
ax.set_xlim(0, length * (box_width + cell_spacing))
ax.set_ylim(0, 2 * (box_width + cell_spacing))
ax.set_aspect('equal')
ax.axis('off')

# Draw top row (25–75) in beige
for i, val in enumerate(top_row):
    x = i * (box_width + cell_spacing)
    y = 1 * (box_width + cell_spacing)
    ax.add_patch(patches.Rectangle((x + shadow_offset, y + shadow_offset), box_width, box_width,
                                   facecolor='gray', alpha=0.3))
    ax.add_patch(patches.Rectangle((x, y), box_width, box_width,
                                   facecolor='#F5F5DC', edgecolor='black'))
    ax.text(x + box_width / 2, y + box_width / 2, str(val),
            ha='center', va='center', fontsize=font_size, fontweight='bold', color='black')

# Draw bottom row (75–25) in brown
for i, val in enumerate(bottom_row):
    x = i * (box_width + cell_spacing)
    y = 0
    ax.add_patch(patches.Rectangle((x + shadow_offset, y + shadow_offset), box_width, box_width,
                                   facecolor='gray', alpha=0.3))
    ax.add_patch(patches.Rectangle((x, y), box_width, box_width,
                                   facecolor='#8B4513', edgecolor='black'))
    ax.text(x + box_width / 2, y + box_width / 2, str(val),
            ha='center', va='center', fontsize=font_size, fontweight='bold', color='white')

plt.tight_layout()

plt.savefig("scoreboard_horizontal.png", dpi=300, bbox_inches='tight', transparent=True)
plt.show()
