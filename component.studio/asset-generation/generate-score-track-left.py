import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Define ranges
left_col = list(range(0, 30))      # Bottom to top
right_col = list(range(71, 101))   # Top to bottom

# Settings
box_width = 0.8
cell_spacing = 0.2
column_gap = cell_spacing + 0.1  # Increased spacing between columns
shadow_offset = 0.05
font_size = 14
rows = len(left_col)

# Create figure
fig, ax = plt.subplots(figsize=(4.2, 14), facecolor='none')
ax.set_xlim(0, 2 * (box_width + column_gap))
ax.set_ylim(0, rows * (box_width + cell_spacing))
ax.set_aspect('equal')
ax.axis('off')

# Draw left column (0–29) in brown, bottom to top
for i, val in enumerate(left_col):
    y = i * (box_width + cell_spacing)
    x = 0
    ax.add_patch(patches.Rectangle(
        (x + shadow_offset, y + shadow_offset),
        box_width, box_width,
        facecolor='gray', alpha=0.3
    ))
    ax.add_patch(patches.Rectangle(
        (x, y),
        box_width, box_width,
        facecolor='#F5F5DC', edgecolor='black'
    ))
    ax.text(x + box_width / 2, y + box_width / 2, str(val),
            ha='center', va='center', fontsize=font_size,
            fontweight='bold', color='black')

# Draw right column (71–100) in beige, top to bottom
for i, val in enumerate(right_col):
    y = (rows - 1 - i) * (box_width + cell_spacing)
    x = box_width + column_gap
    ax.add_patch(patches.Rectangle(
        (x + shadow_offset, y + shadow_offset),
        box_width, box_width,
        facecolor='gray', alpha=0.3
    ))
    ax.add_patch(patches.Rectangle(
        (x, y),
        box_width, box_width,
        facecolor='#8B4513', edgecolor='black'
    ))
    ax.text(x + box_width / 2, y + box_width / 2, str(val),
            ha='center', va='center', fontsize=font_size,
            fontweight='bold', color='white')

# Final rendering
plt.tight_layout()
plt.savefig("scoreboard_vertical_swapped.png", dpi=300, bbox_inches='tight', transparent=True)
plt.show()
