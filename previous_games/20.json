{"description": "First game with 4 players.", "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 24, "hand": [{"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}], "energyCubes": ["artha", "karma"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}], "outerScore": 0, "innerScore": 67, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 14, "hand": [{"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}], "energyCubes": [], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 57, "innerScore": 58, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 3, "hand": [{"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": ["karma"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}], "outerScore": 5, "innerScore": 60, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 45, "hand": [{"id": "T34", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "energyCubes": ["bhakti", "bhakti"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}], "outerScore": 29, "innerScore": 40, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}], "started": true, "turnIndex": 1, "roundCount": 9, "travelDeck": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T26", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T39", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T27", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T30", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T45", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T31", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T48", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "eventDeck": [{"id": "E4", "type": "extraHop"}, {"id": "E6", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E7", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI19", "locationId": 40, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI23", "locationId": 17, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI24", "locationId": 35, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO15", "locationId": 35, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T37", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T46", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T29", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T40", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "faceUpEvent": [{"id": "E9", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E8", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "faceUpJourneyOuter": [{"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}], "locationCubes": {"2": "artha", "3": "karma", "4": "artha", "7": "artha", "11": "artha", "13": "artha", "17": "gnana", "20": "gnana", "26": "gnana", "27": "gnana", "29": "karma", "30": "artha", "32": "artha", "33": "karma", "34": "karma", "36": "gnana", "37": "karma", "38": "karma", "40": "artha", "41": "karma", "42": "gnana", "43": "karma", "44": "karma", "46": "bhakti", "47": "bhakti", "48": "artha"}, "locationOm": {"49": true, "50": true, "53": true, "55": true, "56": true, "57": true, "58": true, "60": true}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "omTokenVictory": false, "omTokenVictor": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538268429, "data": {"eventId": "global-event-41", "eventName": "<PERSON><PERSON><PERSON> Dip", "eventEffect": "pushkar_holy_dip_end_turn_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538292705, "data": {"cardType": "travel", "card": {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538294627, "data": {"path": [62, 61, 1], "travelCards": ["T19"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538297632, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 0, "turnIndex": 1, "timestamp": 1748538300635, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 0, "turnIndex": 1, "timestamp": 1748538302606, "data": {"cardType": "travel", "pickedCards": [{"id": "T39", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 0, "turnIndex": 1, "timestamp": 1748538305610, "data": {"path": [64, 15], "travelCards": ["T39"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 0, "turnIndex": 2, "timestamp": 1748538308613, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 0, "turnIndex": 2, "timestamp": 1748538310291, "data": {"path": [65, 31], "travelCards": ["T25"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 0, "turnIndex": 2, "timestamp": 1748538313297, "data": {"cardType": "travel", "pickedCards": [{"id": "T30", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 0, "turnIndex": 3, "timestamp": 1748538316298, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 3, "timestamp": 1748538406904, "data": {"path": [61, 63, 62, 19], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 3, "timestamp": 1748538417822, "data": {"cardType": "travel", "pickedCards": [{"id": "T48", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 3, "timestamp": 1748538420161, "data": {"cardType": "travel", "drawnCards": [{"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "pushkar_holy_dip_reward", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 0, "turnIndex": 3, "timestamp": 1748538424228, "data": {"previousInnerScore": 0, "newInnerScore": 5, "region": "west", "bonus": 5}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1748538424229, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "B-IFwivno0VYxwZ2AAAH", "sX_-gSI809Kiht6GAAAJ", "c40T6NkncvCLynt6AAAD"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424231, "data": {"eventId": "global-event-38", "eventName": "Excess Baggage", "eventEffect": "excess_baggage"}}, {"type": "excess_baggage_hand_limit", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424232, "data": {"currentHandSize": 3, "cardsToDiscard": 1}}, {"type": "excess_baggage_hand_limit", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424232, "data": {"currentHandSize": 3, "cardsToDiscard": 1}}, {"type": "excess_baggage_hand_limit", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424232, "data": {"currentHandSize": 3, "cardsToDiscard": 1}}, {"type": "excess_baggage_hand_limit", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424232, "data": {"currentHandSize": 3, "cardsToDiscard": 1}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538424235, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538426398, "data": {"path": [1, 9], "travelCards": ["T16"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 1, "turnIndex": 1, "timestamp": 1748538432403, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 1, "turnIndex": 1, "timestamp": 1748538434780, "data": {"path": [15, 18, 28], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 1, "turnIndex": 2, "timestamp": 1748538440785, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 1, "turnIndex": 2, "timestamp": 1748538442783, "data": {"path": [31, 65, 39], "travelCards": ["T20"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 1, "turnIndex": 3, "timestamp": 1748538448788, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "region_based_penalty", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 1, "turnIndex": 3, "timestamp": 1748538465166, "data": {"effect": "excess_baggage", "discardedCards": ["T13"]}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 1, "turnIndex": 3, "timestamp": 1748538478642, "data": {"path": [19, 62, 11, 10], "travelCards": ["T48"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 1, "turnIndex": 3, "timestamp": 1748538515815, "data": {"journeyType": "inner", "journeyCardId": "JI6", "omRequirement": 0}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1748538525988, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "B-IFwivno0VYxwZ2AAAH", "sX_-gSI809Kiht6GAAAJ", "c40T6NkncvCLynt6AAAD"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538525989, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538525993, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538528310, "data": {"path": [9, 1, 61, 6], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "region_based_penalty", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538528310, "data": {"effect": "excess_baggage", "discardedCards": ["T3"]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538531313, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T46", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 2, "turnIndex": 1, "timestamp": 1748538534315, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 2, "turnIndex": 1, "timestamp": 1748538536799, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 2, "turnIndex": 2, "timestamp": 1748538539800, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 2, "turnIndex": 2, "timestamp": 1748538541449, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 2, "turnIndex": 3, "timestamp": 1748538544452, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 2, "turnIndex": 3, "timestamp": 1748538584260, "data": {"cardType": "travel", "pickedCards": [{"id": "T45", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 2, "turnIndex": 3, "timestamp": 1748538591506, "data": {"cardType": "travel", "pickedCards": [{"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 2, "turnIndex": 3, "timestamp": 1748538626075, "data": {"path": [10, 11, 62, 14], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1748538633697, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "B-IFwivno0VYxwZ2AAAH", "sX_-gSI809Kiht6GAAAJ", "c40T6NkncvCLynt6AAAD"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1748538633698, "data": {"eventId": "global-event-43", "eventName": "Cultural Exchange", "eventEffect": "cultural_exchange"}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 3, "turnIndex": 0, "timestamp": 1748538633702, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 3, "turnIndex": 0, "timestamp": 1748538636015, "data": {"cardType": "travel", "pickedCards": [{"id": "T27", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 3, "turnIndex": 1, "timestamp": 1748538639017, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 3, "turnIndex": 1, "timestamp": 1748538641362, "data": {"path": [28, 18, 15, 5], "travelCards": ["T6", "T2"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 3, "turnIndex": 1, "timestamp": 1748538644366, "data": {"journeyType": "outer", "journeyCardId": "JO2", "omRequirement": 0}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 3, "turnIndex": 2, "timestamp": 1748538647368, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 3, "turnIndex": 2, "timestamp": 1748538648978, "data": {"path": [39, 65, 63, 21], "travelCards": ["T47"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 3, "turnIndex": 2, "timestamp": 1748538651981, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 0}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 3, "turnIndex": 3, "timestamp": 1748538654983, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 3, "turnIndex": 3, "timestamp": 1748538939271, "data": {"path": [14, 62, 63], "travelCards": ["T42"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 3, "turnIndex": 3, "timestamp": 1748538954201, "data": {"cardType": "travel", "drawnCards": [{"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 3, "turnIndex": 3, "timestamp": 1748538957208, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": *********5589, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "B-IFwivno0VYxwZ2AAAH", "sX_-gSI809Kiht6GAAAJ", "c40T6NkncvCLynt6AAAD"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": *********5590, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "diwali_distraction_points", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 4, "turnIndex": 0, "timestamp": *********5590, "data": {"previousInnerScore": 0, "newInnerScore": 5, "pointsGained": 5}}, {"type": "diwali_distraction_points", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 0, "timestamp": *********5590, "data": {"previousInnerScore": 0, "newInnerScore": 5, "pointsGained": 5}}, {"type": "diwali_distraction_points", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 4, "turnIndex": 0, "timestamp": *********5590, "data": {"previousInnerScore": 20, "newInnerScore": 25, "pointsGained": 5}}, {"type": "diwali_distraction_points", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 0, "timestamp": *********5590, "data": {"previousInnerScore": 29, "newInnerScore": 34, "pointsGained": 5}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 0, "timestamp": *********5596, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 4, "turnIndex": 0, "timestamp": *********7646, "data": {"path": [6, 7], "travelCards": ["T27"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 4, "turnIndex": 0, "timestamp": 1748538970649, "data": {"journeyType": "inner", "journeyCardId": "JI4", "omRequirement": 0}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 4, "turnIndex": 1, "timestamp": 1748538973650, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 1, "timestamp": 1748538975266, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 1, "timestamp": 1748538975266, "data": {"path": [5, 52], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "region_based_penalty", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 1, "timestamp": 1748538975266, "data": {"effect": "excess_baggage", "discardedCards": ["T44"]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 1, "timestamp": 1748538978269, "data": {"cardType": "travel", "pickedCards": [{"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 4, "turnIndex": 2, "timestamp": 1748538981271, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 4, "turnIndex": 2, "timestamp": 1748538983092, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 4, "turnIndex": 2, "timestamp": 1748538983092, "data": {"path": [21, 20, 51], "travelCards": ["T30"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 4, "turnIndex": 2, "timestamp": 1748538986094, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 4, "turnIndex": 3, "timestamp": 1748538989096, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 3, "timestamp": 1748539010693, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 3, "timestamp": 1748539010693, "data": {"path": [63, 24, 23, 59], "travelCards": ["T33"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 3, "timestamp": 1748539027679, "data": {"cardType": "travel", "drawnCards": [{"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 4, "turnIndex": 3, "timestamp": 1748539030277, "data": {"cardType": "travel", "pickedCards": [{"id": "T29", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1748539033068, "data": {"order": ["c40T6NkncvCLynt6AAAD", "sX_-gSI809Kiht6GAAAJ", "B-IFwivno0VYxwZ2AAAH", "2tjB6DehQMj5yogpAAAF"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1748539033068, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 5, "turnIndex": 0, "timestamp": 1748539033069, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 5, "turnIndex": 0, "timestamp": 1748539042366, "data": {"path": [59, 23, 24, 25], "travelCards": ["T45"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 5, "turnIndex": 0, "timestamp": 1748539046817, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 0}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 5, "turnIndex": 1, "timestamp": 1748539201559, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 5, "turnIndex": 1, "timestamp": 1748539203693, "data": {"path": [51, 20, 21, 22], "travelCards": ["T43", "T15"], "extraHopCards": []}}, {"type": "region_based_penalty", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 5, "turnIndex": 1, "timestamp": 1748539203693, "data": {"effect": "excess_baggage", "discardedCards": ["T7"]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 5, "turnIndex": 1, "timestamp": 1748539206695, "data": {"cardType": "travel", "pickedCards": [{"id": "T40", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 5, "turnIndex": 2, "timestamp": 1748539209695, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 5, "turnIndex": 2, "timestamp": 1748539211973, "data": {"path": [52, 5, 15, 18], "travelCards": ["T24"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 5, "turnIndex": 2, "timestamp": 1748539214975, "data": {"cardType": "travel", "pickedCards": [{"id": "T26", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T37", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 5, "turnIndex": 3, "timestamp": 1748539217977, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 5, "turnIndex": 3, "timestamp": 1748539220160, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 5, "turnIndex": 3, "timestamp": 1748539220160, "data": {"path": [7, 8, 54], "travelCards": ["T32"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 5, "turnIndex": 3, "timestamp": 1748539223164, "data": {"cardType": "travel", "pickedCards": [{"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1748539226167, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "c40T6NkncvCLynt6AAAD", "sX_-gSI809Kiht6GAAAJ", "B-IFwivno0VYxwZ2AAAH"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1748539226167, "data": {"eventId": "global-event-34", "eventName": "Urban Ride", "eventEffect": "urban_ride_reward"}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1748539226172, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1748539228608, "data": {"path": [54, 8], "travelCards": ["T28"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 6, "turnIndex": 1, "timestamp": 1748539231611, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 6, "turnIndex": 1, "timestamp": 1748539278398, "data": {"cardType": "travel", "pickedCards": [{"id": "T31", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 6, "turnIndex": 1, "timestamp": 1748539288225, "data": {"vehicle": "rickshaw", "effect": "urban_ride_reward", "outerPoints": 5}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 6, "turnIndex": 1, "timestamp": 1748539288225, "data": {"path": [25, 27, 16], "travelCards": ["T31"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 6, "turnIndex": 2, "timestamp": 1748539325165, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 6, "turnIndex": 2, "timestamp": 1748539327628, "data": {"path": [22, 35], "travelCards": ["T40"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 6, "turnIndex": 2, "timestamp": 1748539330632, "data": {"cardType": "travel", "pickedCards": [{"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 6, "turnIndex": 3, "timestamp": 1748539333633, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 6, "turnIndex": 3, "timestamp": 1748539335326, "data": {"path": [18, 28, 38, 39, 23], "travelCards": ["T35", "T26"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 6, "turnIndex": 3, "timestamp": 1748539338329, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1748539341331, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "c40T6NkncvCLynt6AAAD", "sX_-gSI809Kiht6GAAAJ", "B-IFwivno0VYxwZ2AAAH"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1748539341332, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 7, "turnIndex": 0, "timestamp": 1748539341335, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1748539342962, "data": {"path": [8, 7, 11, 12], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1748539345965, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 7, "turnIndex": 1, "timestamp": 1748539348967, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 7, "turnIndex": 1, "timestamp": 1748539373719, "data": {"path": [16, 27], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 7, "turnIndex": 1, "timestamp": 1748539376964, "data": {"journeyType": "outer", "journeyCardId": "JO12", "omRequirement": 0}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 7, "turnIndex": 2, "timestamp": 1748539652921, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 7, "turnIndex": 2, "timestamp": 1748539654588, "data": {"path": [35, 29, 64, 61, 4, 3], "travelCards": ["T36", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 7, "turnIndex": 2, "timestamp": 1748539657590, "data": {"journeyType": "inner", "journeyCardId": "JI2", "omRequirement": 0}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 7, "turnIndex": 3, "timestamp": 1748539660591, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 7, "turnIndex": 3, "timestamp": 1748539662288, "data": {"path": [23, 39, 40, 36, 32], "travelCards": ["T37", "T23"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 7, "turnIndex": 3, "timestamp": 1748539665291, "data": {"journeyType": "inner", "journeyCardId": "JI16", "omRequirement": 0}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1748539668293, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "c40T6NkncvCLynt6AAAD", "sX_-gSI809Kiht6GAAAJ", "B-IFwivno0VYxwZ2AAAH"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1748539668294, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 8, "turnIndex": 0, "timestamp": 1748539668298, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 8, "turnIndex": 0, "timestamp": 1748539670647, "data": {"path": [12, 11, 62, 63, 21, 20], "travelCards": ["T46", "T17"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 8, "turnIndex": 0, "timestamp": 1748539673650, "data": {"journeyType": "inner", "journeyCardId": "JI11", "omRequirement": 0}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 8, "turnIndex": 1, "timestamp": 1748539676653, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 8, "turnIndex": 1, "timestamp": 1748540131262, "data": {"path": [27, 16, 14], "travelCards": ["T29"], "extraHopCards": []}}, {"type": "trade", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 8, "turnIndex": 1, "timestamp": 1748540144262, "data": {"cubesTraded": ["artha"], "cubeReceived": "gnana", "count": 1}}, {"type": "collectJourney", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 8, "turnIndex": 1, "timestamp": 1748540147027, "data": {"journeyType": "inner", "journeyCardId": "JI7", "omRequirement": 0}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 8, "turnIndex": 1, "timestamp": 1748540147028, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 115, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "endTurn", "playerId": "c40T6NkncvCLynt6AAAD", "playerName": "me", "roundCount": 8, "turnIndex": 2, "timestamp": 1748540152511, "data": {"nextPlayerId": "sX_-gSI809Kiht6GAAAJ", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 8, "turnIndex": 2, "timestamp": 1748540154925, "data": {"cardType": "travel", "pickedCards": [{"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "sX_-gSI809Kiht6GAAAJ", "playerName": "SwiftNomad", "roundCount": 8, "turnIndex": 3, "timestamp": 1748540157926, "data": {"nextPlayerId": "B-IFwivno0VYxwZ2AAAH", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 8, "turnIndex": 3, "timestamp": 1748540159455, "data": {"path": [32, 31, 45], "travelCards": ["T41"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 8, "turnIndex": 3, "timestamp": 1748540162459, "data": {"cardType": "travel", "pickedCards": [{"id": "T34", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1748540165460, "data": {"order": ["2tjB6DehQMj5yogpAAAF", "c40T6NkncvCLynt6AAAD", "sX_-gSI809Kiht6GAAAJ", "B-IFwivno0VYxwZ2AAAH"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1748540165461, "data": {"eventId": "global-event-35", "eventName": "Road Warriors", "eventEffect": "road_warriors_reward"}}, {"type": "endTurn", "playerId": "B-IFwivno0VYxwZ2AAAH", "playerName": "EternalNomad", "roundCount": 9, "turnIndex": 0, "timestamp": 1748540165466, "data": {"nextPlayerId": "2tjB6DehQMj5yogpAAAF", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 9, "turnIndex": 0, "timestamp": 1748540167843, "data": {"path": [20, 21, 63, 24], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 9, "turnIndex": 0, "timestamp": 1748540170847, "data": {"cardType": "travel", "pickedCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "2tjB6DehQMj5yogpAAAF", "playerName": "MightyPathfinder", "roundCount": 9, "turnIndex": 1, "timestamp": 1748540173848, "data": {"nextPlayerId": "c40T6NkncvCLynt6AAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 1, "timestamp": 1748540173852, "data": {"winner": {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "outerScore": 57, "innerScore": 58, "totalScore": 115, "omTotal": 1, "winByOm": false, "winByScore": true}, "winCondition": "SCORE_THRESHOLD", "totalRounds": 9, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "outerScore": 0, "innerScore": 67, "totalScore": 67, "omTotal": 1}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "outerScore": 57, "innerScore": 58, "totalScore": 115, "omTotal": 1}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "outerScore": 5, "innerScore": 60, "totalScore": 65, "omTotal": 1}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "outerScore": 29, "innerScore": 40, "totalScore": 69, "omTotal": 1}]}}], "roundSummaries": [{"roundNumber": 1, "timestamp": 1748538424230, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 1, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 15, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 31, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 19, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T46", "value": 3, "vehicle": "boat"}, {"id": "T47", "value": 3, "vehicle": "train"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-41", "name": "<PERSON><PERSON><PERSON> Dip", "effect": "pushkar_holy_dip_end_turn_reward"}}, {"roundNumber": 2, "timestamp": 1748538525989, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 9, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 28, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 39, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 10, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T46", "value": 3, "vehicle": "boat"}, {"id": "T47", "value": 3, "vehicle": "train"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}, {"id": "JI12", "locationId": 21, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-38", "name": "Excess Baggage", "effect": "excess_baggage"}}, {"roundNumber": 3, "timestamp": 1748538633698, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 6, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 28, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 39, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 14, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 2, "bhakti": null, "gnana": null, "karma": 2, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T24", "value": 3, "vehicle": "truck"}, {"id": "T35", "value": 3, "vehicle": "train"}, {"id": "T32", "value": 2, "vehicle": "motorbike"}, {"id": "T27", "value": 1, "vehicle": "trek"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}, {"id": "JI12", "locationId": 21, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-3", "name": "<PERSON><PERSON>", "effect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"roundNumber": 4, "timestamp": *********5589, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 6, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 5, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 24, "inner": 0, "total": 24}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 21, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 63, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": null}], "faceUpCards": {"travel": [{"id": "T24", "value": 3, "vehicle": "truck"}, {"id": "T35", "value": 3, "vehicle": "train"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T40", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}, {"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-43", "name": "Cultural Exchange", "effect": "cultural_exchange"}}, {"roundNumber": 5, "timestamp": 1748539033068, "players": [{"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 59, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 0}, "scores": {"outer": 0, "inner": 34, "total": 34}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 51, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 25, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 52, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 24, "inner": 5, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 7, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 32, "total": 32}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T40", "value": 1, "vehicle": "cycle"}, {"id": "T26", "value": 1, "vehicle": "horse"}, {"id": "T37", "value": 1, "vehicle": "camel"}, {"id": "T28", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "effect": "gain_5_inner_no_cube_pickup"}}, {"roundNumber": 6, "timestamp": 1748539226167, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 54, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 32, "total": 32}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 25, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 0}, "scores": {"outer": 20, "inner": 34, "total": 54}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 22, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 25, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 18, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 24, "inner": 5, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T36", "value": 3, "vehicle": "truck"}, {"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T31", "value": 2, "vehicle": "rickshaw"}, {"id": "T23", "value": 3, "vehicle": "train"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-26", "name": "Engineer's Precision", "effect": "engineers_precision_reward"}}, {"roundNumber": 7, "timestamp": 1748539341331, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 8, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 32, "total": 32}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 16, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 1}, "scores": {"outer": 25, "inner": 34, "total": 59}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 35, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 25, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 23, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 2, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 24, "inner": 5, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T12", "value": 3, "vehicle": "truck"}, {"id": "T18", "value": 2, "vehicle": "car"}, {"id": "T34", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 35}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 35}, {"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-34", "name": "Urban Ride", "effect": "urban_ride_reward"}}, {"roundNumber": 8, "timestamp": 1748539668293, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 12, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 32, "total": 32}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 1, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 27, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 57, "inner": 34, "total": 91}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 3, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 5, "inner": 60, "total": 65}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": 1}}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 32, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 29, "inner": 40, "total": 69}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T18", "value": 2, "vehicle": "car"}, {"id": "T34", "value": 3, "vehicle": "boat"}, {"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T28", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 35}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 35}]}, "globalEvent": {"id": "global-event-5", "name": "Bountiful Bhandara", "effect": "draw_2_cubes_bonus_5_outer"}}, {"roundNumber": 9, "timestamp": 1748540165461, "players": [{"id": "2tjB6DehQMj5yogpAAAF", "name": "MightyPathfinder", "position": 20, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 67, "total": 67}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "c40T6NkncvCLynt6AAAD", "name": "me", "position": 14, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 57, "inner": 58, "total": 115}, "collectedJourneys": 4, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "sX_-gSI809Kiht6GAAAJ", "name": "SwiftNomad", "position": 3, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 5, "inner": 60, "total": 65}, "collectedJourneys": 2, "gained": null}, {"id": "B-IFwivno0VYxwZ2AAAH", "name": "EternalNomad", "position": 45, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 2, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 29, "inner": 40, "total": 69}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T47", "value": 3, "vehicle": "train"}, {"id": "T25", "value": 1, "vehicle": "camel"}, {"id": "T21", "value": 3, "vehicle": "helicopter"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI20", "locationId": 41, "requiredCubes": {}, "points": 20}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 35}, {"id": "JI18", "locationId": 38, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-23", "name": "Merchant's Midas", "effect": "merchants_midas_reward"}}], "characterDeck": [{"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 4, "karma": 3, "gnana": 8, "bhakti": 10}, "currentGlobalEvent": {"id": "global-event-35", "name": "Road Warriors", "text": "Car or Bus: gain +5 Outer points", "effect": "road_warriors_reward"}, "globalEventDeck": [{"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-37", "name": "Central Heart", "text": "<PERSON>ain 5 inner points if you are in Central at the end of turn", "effect": "central_heart_end_turn_reward"}, {"id": "global-event-32", "name": "Eco Trail", "text": "Cycle or Trek: gain +5 Inner points", "effect": "eco_trail_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-36", "name": "Rails and Sails", "text": "Train or Boat: gain +5 Outer points", "effect": "rails_and_sails_reward"}, {"id": "global-event-40", "name": "Spirit of Seva", "text": "Leader on each track donates 3 points to player with lowest score on that track", "effect": "spirit_of_seva"}, {"id": "global-event-33", "name": "<PERSON><PERSON> Caravan<PERSON>", "text": "Horse or Camel: gain +5 Outer points", "effect": "rajput_caravans_reward"}, {"id": "global-event-42", "name": "<PERSON><PERSON><PERSON> in the Clouds", "text": "<PERSON><PERSON> 5 inner points if you visit > 1 Airports this round", "effect": "parikrama_in_clouds_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-28", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-30", "name": "Himalayan NE", "text": "Gain 5 inner points if you are in North East at the end of turn", "effect": "himalayan_ne_end_turn_reward"}, {"id": "global-event-39", "name": "Heritage Site Renovations", "text": "No outer journey cards can be collected this round", "effect": "no_outer_journey_cards"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}], "globalEventDiscard": [{"id": "global-event-41", "name": "<PERSON><PERSON><PERSON> Dip", "text": "<PERSON><PERSON> 5 inner points if you are in West at the end of turn", "effect": "pushkar_holy_dip_end_turn_reward"}, {"id": "global-event-38", "name": "Excess Baggage", "text": "Hand limit 2 this round, discard down immediately", "effect": "excess_baggage"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-43", "name": "Cultural Exchange", "text": "At the start of turn, you may swap locations with another player who agrees. Both gain 5 Inner points.", "effect": "cultural_exchange"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-34", "name": "Urban Ride", "text": "Motorbike or Rickshaw: gain +5 Outer points", "effect": "urban_ride_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}], "nameMode": false}