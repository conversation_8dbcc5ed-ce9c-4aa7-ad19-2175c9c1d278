{"description": "Only bots; test why 2nd bot got stuck on 5 energy cubes and always tried to move but never collected a JC", "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 3, "hand": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": ["artha", "karma", "bhakti", "karma", "artha"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JI19", "locationId": 40, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "outerScore": 27, "innerScore": 47, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 19, "hand": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "energyCubes": ["bhakti", "gnana", "artha", "artha", "karma"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 25, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}}, {"id": "2neaTGDoMnvBULOeAAAK", "name": "EternalExplorer", "position": 11, "hand": [{"id": "T40", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}], "energyCubes": ["artha", "karma", "karma", "artha", "karma"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}], "outerScore": 0, "innerScore": 55, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T31", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T45", "type": "travel", "value": 3, "vehicle": "helicopter"}], "energyCubes": ["bhakti", "gnana", "artha", "gnana"], "omTemp": [], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [], "outerScore": 0, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}], "started": true, "turnIndex": 1, "roundCount": 15, "travelDeck": [{"id": "T30", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T46", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}], "eventDeck": [{"id": "E4", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E6", "type": "extraHop"}, {"id": "E7", "type": "extraHop"}, {"id": "E8", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI24", "locationId": 35, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI23", "locationId": 17, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}], "journeyDeckOuter": [{"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 35, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T29", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T26", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T27", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T34", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T39", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T48", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T37", "type": "travel", "value": 1, "vehicle": "camel"}], "faceUpEvent": [{"id": "E9", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "faceUpJourneyOuter": [{"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}], "locationCubes": {"5": "bhakti", "9": "artha", "10": "karma", "13": "bhakti", "17": "karma", "19": "karma", "20": "karma", "25": "karma", "26": "gnana", "29": "artha", "30": "bhakti", "32": "bhakti", "34": "gnana", "36": "gnana", "41": "bhakti", "42": "bhakti", "43": "artha", "44": "gnana", "46": "gnana", "47": "artha", "48": "artha"}, "locationOm": {"49": true, "50": true, "52": true, "53": true, "54": true, "56": true, "57": true, "58": true, "60": true}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "omTokenVictory": false, "omTokenVictor": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543885029, "data": {"eventId": "global-event-42", "eventName": "<PERSON><PERSON><PERSON> in the Clouds", "eventEffect": "parikrama_in_clouds_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543895534, "data": {"cardType": "travel", "card": {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543897362, "data": {"path": [61, 1, 2], "travelCards": ["T32"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543900364, "data": {"cardType": "travel", "pickedCards": [{"id": "T48", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 0, "turnIndex": 1, "timestamp": 1748543903367, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1748543905195, "data": {"path": [63, 61, 1], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1748543908198, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "parikrama_in_clouds_reward", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1748543911201, "data": {"previousInnerScore": 0, "newInnerScore": 5, "airportsVisited": [63, 61], "bonus": 5}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1748543911201, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 0, "turnIndex": 2, "timestamp": 1748543913077, "data": {"path": [65, 39], "travelCards": ["T15"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 0, "turnIndex": 2, "timestamp": 1748543916080, "data": {"cardType": "travel", "pickedCards": [{"id": "T30", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 0, "turnIndex": 3, "timestamp": 1748543919084, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 0, "turnIndex": 3, "timestamp": 1748543920982, "data": {"cardType": "travel", "pickedCards": [{"id": "T39", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T31", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 0, "turnIndex": 3, "timestamp": 1748543923986, "data": {"path": [64, 15], "travelCards": ["T39"], "extraHopCards": []}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1748543926990, "data": {"order": ["kqhjW0ar9GVc-5KNAAAJ", "INQ6uzHBONMjwgwFAAAK", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1748543926992, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 1, "turnIndex": 0, "timestamp": 1748543926995, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 1, "turnIndex": 0, "timestamp": 1748543928795, "data": {"path": [2, 1], "travelCards": ["T16"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 1, "turnIndex": 0, "timestamp": 1748543931798, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 1, "turnIndex": 1, "timestamp": 1748543934804, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1748543937308, "data": {"path": [1, 2], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1748543940312, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1748543943314, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 1, "turnIndex": 2, "timestamp": 1748543945244, "data": {"cardType": "travel", "pickedCards": [{"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 1, "turnIndex": 3, "timestamp": 1748543948248, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 1, "turnIndex": 3, "timestamp": 1748543950061, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1748543953064, "data": {"order": ["kqhjW0ar9GVc-5KNAAAJ", "INQ6uzHBONMjwgwFAAAK", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1748543953065, "data": {"eventId": "global-event-30", "eventName": "Himalayan NE", "eventEffect": "himalayan_ne_end_turn_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 2, "turnIndex": 0, "timestamp": 1748543953068, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 2, "turnIndex": 0, "timestamp": 1748543955330, "data": {"path": [1, 61, 63, 21], "travelCards": ["T48"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 2, "turnIndex": 0, "timestamp": 1748543958335, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 0}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 2, "turnIndex": 1, "timestamp": 1748543961336, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1748543963661, "data": {"path": [2, 3, 4], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1748543966664, "data": {"cardType": "travel", "pickedCards": [{"id": "T37", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1748543969667, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 2, "turnIndex": 2, "timestamp": 1748543971533, "data": {"path": [39, 40], "travelCards": ["T38"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 2, "turnIndex": 2, "timestamp": 1748543974536, "data": {"journeyType": "inner", "journeyCardId": "JI19", "omRequirement": 0}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 2, "turnIndex": 3, "timestamp": 1748543977538, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 2, "turnIndex": 3, "timestamp": 1748543979647, "data": {"path": [15, 18], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1748543982650, "data": {"order": ["kqhjW0ar9GVc-5KNAAAJ", "INQ6uzHBONMjwgwFAAAK", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1748543982651, "data": {"eventId": "global-event-32", "eventName": "Eco Trail", "eventEffect": "eco_trail_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 3, "turnIndex": 0, "timestamp": 1748543982655, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 3, "turnIndex": 0, "timestamp": 1748543984862, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 3, "turnIndex": 0, "timestamp": 1748543984862, "data": {"path": [21, 20, 51], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 3, "turnIndex": 0, "timestamp": 1748543987865, "data": {"cardType": "travel", "pickedCards": [{"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 3, "turnIndex": 1, "timestamp": 1748543990867, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1748543992705, "data": {"path": [4, 3, 46, 45], "travelCards": ["T36"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1748543995709, "data": {"journeyType": "outer", "journeyCardId": "JO24", "omRequirement": 0}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1748543998711, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 3, "turnIndex": 2, "timestamp": 1748544001162, "data": {"path": [40, 39, 38], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 3, "turnIndex": 2, "timestamp": 1748544004165, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 3, "turnIndex": 3, "timestamp": 1748544007166, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 3, "turnIndex": 3, "timestamp": 1748544009384, "data": {"cardType": "travel", "pickedCards": [{"id": "T45", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1748544012387, "data": {"order": ["kqhjW0ar9GVc-5KNAAAJ", "INQ6uzHBONMjwgwFAAAK", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544012388, "data": {"eventId": "global-event-38", "eventName": "Excess Baggage", "eventEffect": "excess_baggage"}}, {"type": "excess_baggage_hand_limit", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544012388, "data": {"currentHandSize": 3, "cardsToDiscard": 1}}, {"type": "excess_baggage_hand_limit", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544012388, "data": {"currentHandSize": 4, "cardsToDiscard": 2}}, {"type": "excess_baggage_hand_limit", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544012389, "data": {"currentHandSize": 4, "cardsToDiscard": 2}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544012393, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544014781, "data": {"path": [51, 20, 21, 22], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 4, "turnIndex": 1, "timestamp": 1748544020786, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1748544026148, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 4, "turnIndex": 2, "timestamp": 1748544027694, "data": {"path": [38, 28, 35], "travelCards": ["T30"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 4, "turnIndex": 3, "timestamp": 1748544030696, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1748544032292, "data": {"order": ["kqhjW0ar9GVc-5KNAAAJ", "INQ6uzHBONMjwgwFAAAK", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544032292, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544032295, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544034152, "data": {"path": [22, 26, 23], "travelCards": ["T44"], "extraHopCards": []}}, {"type": "region_based_penalty", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544034153, "data": {"effect": "excess_baggage", "discardedCards": ["T33"]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544037155, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 5, "turnIndex": 1, "timestamp": 1748544040156, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1748544042694, "data": {"cardType": "travel", "pickedCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "omTrackAdvanced", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1748544045698, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1748544045698, "data": {"path": [45, 31, 30, 55], "travelCards": ["T22"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1748544048699, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 5, "turnIndex": 2, "timestamp": 1748544050250, "data": {"path": [35, 28, 38, 39], "travelCards": ["T35"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 5, "turnIndex": 2, "timestamp": 1748544053253, "data": {"journeyType": "outer", "journeyCardId": "JO21", "omRequirement": 0}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 5, "turnIndex": 3, "timestamp": 1748544056255, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1748544058702, "data": {"order": ["INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "PV4yzP-aPD5EefzvAAAI", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1748544058702, "data": {"eventId": "global-event-24", "eventName": "Professor's Insight", "eventEffect": "professors_insight_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 6, "turnIndex": 0, "timestamp": 1748544058705, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1748544060739, "data": {"path": [55, 30, 31, 65, 37], "travelCards": ["T37", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1748544063742, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1748544066743, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 6, "turnIndex": 1, "timestamp": 1748544069030, "data": {"path": [23, 24], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 6, "turnIndex": 1, "timestamp": 1748544072038, "data": {"cardType": "travel", "pickedCards": [{"id": "T46", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 6, "turnIndex": 2, "timestamp": 1748544075039, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "omTrackAdvanced", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 6, "turnIndex": 2, "timestamp": 1748544076645, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 6, "turnIndex": 2, "timestamp": 1748544076645, "data": {"path": [39, 23, 59], "travelCards": ["T19"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 6, "turnIndex": 2, "timestamp": 1748544079648, "data": {"cardType": "travel", "pickedCards": [{"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 6, "turnIndex": 3, "timestamp": 1748544082651, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1748544084912, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1748544084913, "data": {"eventId": "global-event-35", "eventName": "Road Warriors", "eventEffect": "road_warriors_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 7, "turnIndex": 0, "timestamp": 1748544084915, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 7, "turnIndex": 0, "timestamp": 1748544087224, "data": {"path": [59, 23, 24, 25, 27], "travelCards": ["T3", "T47"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 7, "turnIndex": 0, "timestamp": 1748544090228, "data": {"cardType": "travel", "pickedCards": [{"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 7, "turnIndex": 1, "timestamp": 1748544093230, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1748544095068, "data": {"vehicle": "bus", "effect": "road_warriors_reward", "outerPoints": 5}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1748544095068, "data": {"path": [37, 38, 28], "travelCards": ["T17"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1748544098071, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T27", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1748544101072, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 7, "turnIndex": 2, "timestamp": 1748544103070, "data": {"path": [24, 63, 65, 31], "travelCards": ["T46"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 7, "turnIndex": 2, "timestamp": 1748544106072, "data": {"cardType": "travel", "pickedCards": [{"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 7, "turnIndex": 3, "timestamp": 1748544109074, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1748544111195, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1748544111196, "data": {"eventId": "global-event-39", "eventName": "Heritage Site Renovations", "eventEffect": "no_outer_journey_cards"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 8, "turnIndex": 0, "timestamp": 1748544111198, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 8, "turnIndex": 0, "timestamp": 1748544113597, "data": {"cardType": "travel", "pickedCards": [{"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 8, "turnIndex": 0, "timestamp": 1748544116600, "data": {"path": [27, 16], "travelCards": ["T25"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 8, "turnIndex": 1, "timestamp": 1748544119602, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1748544121937, "data": {"path": [28, 29, 33], "travelCards": ["T42"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1748544124941, "data": {"cardType": "travel", "pickedCards": [{"id": "T34", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1748544127942, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 8, "turnIndex": 2, "timestamp": 1748544130152, "data": {"path": [31, 65, 62, 14, 16], "travelCards": ["T28", "T41", "T14"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 8, "turnIndex": 2, "timestamp": 1748544133156, "data": {"journeyType": "inner", "journeyCardId": "JI9", "omRequirement": 0}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 8, "turnIndex": 3, "timestamp": 1748544136156, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1748544138223, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1748544138224, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 9, "turnIndex": 0, "timestamp": 1748544138227, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 9, "turnIndex": 0, "timestamp": 1748544139882, "data": {"path": [16, 14, 62, 11], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 9, "turnIndex": 0, "timestamp": 1748544142883, "data": {"cardType": "travel", "pickedCards": [{"id": "T29", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T26", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 9, "turnIndex": 1, "timestamp": 1748544145884, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1748544147944, "data": {"path": [33, 34, 13, 12], "travelCards": ["T23"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1748544150948, "data": {"cardType": "travel", "pickedCards": [{"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1748544153951, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 9, "turnIndex": 2, "timestamp": 1748544156359, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T40", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 9, "turnIndex": 3, "timestamp": 1748544159360, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1748544161809, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1748544161810, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 10, "turnIndex": 0, "timestamp": 1748544161813, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 10, "turnIndex": 0, "timestamp": 1748544163785, "data": {"path": [11, 62, 63, 24, 23], "travelCards": ["T18", "T29"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 10, "turnIndex": 0, "timestamp": 1748544166788, "data": {"journeyType": "inner", "journeyCardId": "JI13", "omRequirement": 0}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 10, "turnIndex": 1, "timestamp": 1748544169790, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1748544172107, "data": {"path": [12, 11, 7], "travelCards": ["T43"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1748544175109, "data": {"cardType": "travel", "pickedCards": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1748544178111, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 10, "turnIndex": 2, "timestamp": 1748544180021, "data": {"path": [16, 14], "travelCards": ["T13"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 10, "turnIndex": 2, "timestamp": 1748544183024, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 10, "turnIndex": 3, "timestamp": 1748544186026, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1748544187561, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1748544187561, "data": {"eventId": "global-event-37", "eventName": "Central Heart", "eventEffect": "central_heart_end_turn_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 11, "turnIndex": 0, "timestamp": 1748544187563, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 11, "turnIndex": 0, "timestamp": 1748544189103, "data": {"path": [23, 24, 63, 61, 6], "travelCards": ["T21", "T26"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 11, "turnIndex": 0, "timestamp": 1748544192106, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 11, "turnIndex": 1, "timestamp": 1748544195108, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1748544197478, "data": {"path": [7, 11, 10], "travelCards": ["T20"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1748544200481, "data": {"cardType": "travel", "pickedCards": [{"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1748544203483, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 11, "turnIndex": 2, "timestamp": 1748544205141, "data": {"path": [14, 13, 12], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 11, "turnIndex": 2, "timestamp": 1748544208144, "data": {"cardType": "travel", "pickedCards": [{"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 11, "turnIndex": 3, "timestamp": 1748544211146, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "central_heart_reward", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 11, "turnIndex": 3, "timestamp": 1748544212663, "data": {"previousInnerScore": 0, "newInnerScore": 5, "region": "central", "bonus": 5}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1748544212664, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1748544212664, "data": {"eventId": "global-event-7", "eventName": "Election Campaigns", "eventEffect": "double_trade_no_travel"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 12, "turnIndex": 0, "timestamp": 1748544212666, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 12, "turnIndex": 1, "timestamp": 1748544214233, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1748544216648, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 12, "turnIndex": 3, "timestamp": 1748544218595, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1748544220494, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1748544220494, "data": {"eventId": "global-event-21", "eventName": "Heavy Haul", "eventEffect": "heavy_haul_reward"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 13, "turnIndex": 0, "timestamp": 1748544220498, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 13, "turnIndex": 0, "timestamp": 1748544222143, "data": {"cardType": "travel", "pickedCards": [{"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 13, "turnIndex": 1, "timestamp": 1748544225144, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1748544227278, "data": {"path": [10, 11, 62, 65, 37], "travelCards": ["T27", "T34"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1748544230281, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1748544233283, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 13, "turnIndex": 2, "timestamp": 1748544234943, "data": {"path": [12, 11, 7, 8], "travelCards": ["T33"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 13, "turnIndex": 3, "timestamp": 1748544237944, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1748544239668, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1748544239668, "data": {"eventId": "global-event-28", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 14, "turnIndex": 0, "timestamp": 1748544239670, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 14, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 14, "turnIndex": 0, "timestamp": 1748544241241, "data": {"path": [6, 7], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 14, "turnIndex": 1, "timestamp": 1748544244243, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "move", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1748544245806, "data": {"path": [37, 65, 62, 19], "travelCards": ["T47"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1748544248809, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "INQ6uzHBONMjwgwFAAAK", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 2, "timestamp": 1748544251812, "data": {"nextPlayerId": "kqhjW0ar9GVc-5KNAAAJ", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "move", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 14, "turnIndex": 2, "timestamp": 1748544254010, "data": {"path": [8, 7, 11], "travelCards": ["T44"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 14, "turnIndex": 2, "timestamp": 1748544257011, "data": {"cardType": "travel", "pickedCards": [{"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kqhjW0ar9GVc-5KNAAAJ", "playerName": "EternalExplorer", "roundCount": 14, "turnIndex": 3, "timestamp": 1748544260014, "data": {"nextPlayerId": "sgSA8vInIZd6kCJ_AAAL", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "turnOrderUpdated", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1748544261678, "data": {"order": ["PV4yzP-aPD5EefzvAAAI", "INQ6uzHBONMjwgwFAAAK", "kqhjW0ar9GVc-5KNAAAJ", "sgSA8vInIZd6kCJ_AAAL"]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1748544261678, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "endTurn", "playerId": "sgSA8vInIZd6kCJ_AAAL", "playerName": "Brave<PERSON>raveler", "roundCount": 15, "turnIndex": 0, "timestamp": 1748544261680, "data": {"nextPlayerId": "PV4yzP-aPD5EefzvAAAI", "newRound": true, "roundCount": 15, "turnCount": null}}, {"type": "move", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 15, "turnIndex": 0, "timestamp": 1748544264102, "data": {"path": [7, 6, 4, 3], "travelCards": ["T36"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 15, "turnIndex": 0, "timestamp": 1748544267104, "data": {"cardType": "travel", "pickedCards": [{"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PV4yzP-aPD5EefzvAAAI", "playerName": "EternalPilgrim", "roundCount": 15, "turnIndex": 1, "timestamp": 1748544270106, "data": {"nextPlayerId": "INQ6uzHBONMjwgwFAAAK", "newRound": false, "roundCount": 15, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 1, "timestamp": 1748544702861, "data": {"timestamp": 1748544702861, "loadedStateRoundCount": 15, "playerCount": 4}}], "roundSummaries": [{"roundNumber": 1, "timestamp": 1748543926992, "players": [{"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 2, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 1, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 39, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 15, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T38", "value": 1, "vehicle": "horse"}, {"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T5", "value": 2, "vehicle": "bus"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI19", "locationId": 40, "requiredCubes": {}, "points": 20}, {"id": "JI12", "locationId": 21, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-42", "name": "<PERSON><PERSON><PERSON> in the Clouds", "effect": "parikrama_in_clouds_reward"}}, {"roundNumber": 2, "timestamp": 1748543953065, "players": [{"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 1, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 2, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 39, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 15, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T37", "value": 1, "vehicle": "camel"}, {"id": "T33", "value": 3, "vehicle": "helicopter"}, {"id": "T35", "value": 3, "vehicle": "train"}, {"id": "T44", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI19", "locationId": 40, "requiredCubes": {}, "points": 20}, {"id": "JI12", "locationId": 21, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-6", "name": "Turbulent Skies", "effect": "no_airport_travel"}}, {"roundNumber": 3, "timestamp": 1748543982651, "players": [{"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 21, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": 1}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 4, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 40, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": 1}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T33", "value": 3, "vehicle": "helicopter"}, {"id": "T35", "value": 3, "vehicle": "train"}, {"id": "T44", "value": 2, "vehicle": "motorbike"}, {"id": "T45", "value": 3, "vehicle": "helicopter"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-30", "name": "Himalayan NE", "effect": "himalayan_ne_end_turn_reward"}}, {"roundNumber": 4, "timestamp": 1748544012387, "players": [{"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 51, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 45, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 20, "inner": 5, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 38, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-32", "name": "Eco Trail", "effect": "eco_trail_reward"}}, {"roundNumber": 5, "timestamp": 1748544032292, "players": [{"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 22, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 45, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 20, "inner": 5, "total": 25}, "collectedJourneys": 1, "gained": null}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 35, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-38", "name": "Excess Baggage", "effect": "excess_baggage"}}, {"roundNumber": 6, "timestamp": 1748544058702, "players": [{"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 55, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 20, "inner": 5, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 23, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 39, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 20, "total": 47}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T42", "value": 2, "vehicle": "car"}, {"id": "T46", "value": 3, "vehicle": "boat"}, {"id": "T28", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-23", "name": "Merchant's Midas", "effect": "merchants_midas_reward"}}, {"roundNumber": 7, "timestamp": 1748544084912, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 59, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 20, "total": 47}, "collectedJourneys": 2, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 37, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 1}, "scores": {"outer": 20, "inner": 5, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 24, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T18", "value": 2, "vehicle": "car"}, {"id": "T23", "value": 3, "vehicle": "train"}, {"id": "T27", "value": 1, "vehicle": "trek"}, {"id": "T14", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-24", "name": "Professor's Insight", "effect": "professors_insight_reward"}}, {"roundNumber": 8, "timestamp": 1748544111195, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 27, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 20, "total": 47}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 28, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 31, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 20, "total": 20}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T25", "value": 1, "vehicle": "camel"}, {"id": "T21", "value": 3, "vehicle": "helicopter"}, {"id": "T29", "value": 2, "vehicle": "bus"}, {"id": "T34", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 35}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-35", "name": "Road Warriors", "effect": "road_warriors_reward"}}, {"roundNumber": 9, "timestamp": 1748544138223, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 16, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 20, "total": 47}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 33, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 16, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T29", "value": 2, "vehicle": "bus"}, {"id": "T26", "value": 1, "vehicle": "horse"}, {"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T43", "value": 2, "vehicle": "rickshaw"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-39", "name": "Heritage Site Renovations", "effect": "no_outer_journey_cards"}}, {"roundNumber": 10, "timestamp": 1748544161809, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 11, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 20, "total": 47}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 12, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 16, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": null}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T20", "value": 2, "vehicle": "motorbike"}, {"id": "T14", "value": 1, "vehicle": "horse"}, {"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T8", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI13", "locationId": 23, "requiredCubes": {}, "points": 27}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-8", "name": "Triathlon", "effect": "triathlon_bonus"}}, {"roundNumber": 11, "timestamp": 1748544187561, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 23, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 27, "inner": 47, "total": 74}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 7, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 14, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T47", "value": 3, "vehicle": "train"}, {"id": "T33", "value": 3, "vehicle": "helicopter"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}, {"id": "JI8", "locationId": 15, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-27", "name": "Frozen North", "effect": "frozen_north"}}, {"roundNumber": 12, "timestamp": 1748544212664, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 6, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 27, "inner": 47, "total": 74}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 10, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 12, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 1}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T38", "value": 1, "vehicle": "horse"}, {"id": "T36", "value": 3, "vehicle": "truck"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T11", "value": 3, "vehicle": "train"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}, {"id": "JI8", "locationId": 15, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-37", "name": "Central Heart", "effect": "central_heart_end_turn_reward"}}, {"roundNumber": 13, "timestamp": 1748544220494, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 6, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 27, "inner": 47, "total": 74}, "collectedJourneys": 3, "gained": null}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 10, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 12, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 1}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": null}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T38", "value": 1, "vehicle": "horse"}, {"id": "T36", "value": 3, "vehicle": "truck"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T11", "value": 3, "vehicle": "train"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}, {"id": "JI8", "locationId": 15, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-7", "name": "Election Campaigns", "effect": "double_trade_no_travel"}}, {"roundNumber": 14, "timestamp": 1748544239668, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 6, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 27, "inner": 47, "total": 74}, "collectedJourneys": 3, "gained": null}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 37, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 8, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 2}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}, {"id": "T39", "value": 1, "vehicle": "trek"}, {"id": "T41", "value": 2, "vehicle": "bus"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}, {"id": "JI8", "locationId": 15, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-21", "name": "Heavy Haul", "effect": "heavy_haul_reward"}}, {"roundNumber": 15, "timestamp": 1748544261678, "players": [{"id": "PV4yzP-aPD5EefzvAAAI", "name": "EternalPilgrim", "position": 7, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 27, "inner": 47, "total": 74}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "INQ6uzHBONMjwgwFAAAK", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 19, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 1, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "kqhjW0ar9GVc-5KNAAAJ", "name": "EternalExplorer", "position": 11, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 2}, "scores": {"outer": 0, "inner": 55, "total": 55}, "collectedJourneys": 2, "gained": null}, {"id": "sgSA8vInIZd6kCJ_AAAL", "name": "Brave<PERSON>raveler", "position": 18, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T39", "value": 1, "vehicle": "trek"}, {"id": "T42", "value": 2, "vehicle": "car"}, {"id": "T32", "value": 2, "vehicle": "motorbike"}, {"id": "T48", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}, {"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 35}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI3", "locationId": 4, "requiredCubes": {}, "points": 20}, {"id": "JI5", "locationId": 8, "requiredCubes": {}, "points": 27}, {"id": "JI8", "locationId": 15, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-28", "name": "Solar South", "effect": "solar_south"}}], "characterDeck": [{"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 2, "karma": 3, "gnana": 6, "bhakti": 5}, "currentGlobalEvent": {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, "globalEventDeck": [{"id": "global-event-34", "name": "Urban Ride", "text": "Motorbike or Rickshaw: gain +5 Outer points", "effect": "urban_ride_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-43", "name": "Cultural Exchange", "text": "At the start of turn, you may swap locations with another player who agrees. Both gain 5 Inner points.", "effect": "cultural_exchange"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-41", "name": "<PERSON><PERSON><PERSON> Dip", "text": "<PERSON><PERSON> 5 inner points if you are in West at the end of turn", "effect": "pushkar_holy_dip_end_turn_reward"}, {"id": "global-event-36", "name": "Rails and Sails", "text": "Train or Boat: gain +5 Outer points", "effect": "rails_and_sails_reward"}, {"id": "global-event-40", "name": "Spirit of Seva", "text": "Leader on each track donates 3 points to player with lowest score on that track", "effect": "spirit_of_seva"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-33", "name": "<PERSON><PERSON> Caravan<PERSON>", "text": "Horse or Camel: gain +5 Outer points", "effect": "rajput_caravans_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}], "globalEventDiscard": [{"id": "global-event-42", "name": "<PERSON><PERSON><PERSON> in the Clouds", "text": "<PERSON><PERSON> 5 inner points if you visit > 1 Airports this round", "effect": "parikrama_in_clouds_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-30", "name": "Himalayan NE", "text": "Gain 5 inner points if you are in North East at the end of turn", "effect": "himalayan_ne_end_turn_reward"}, {"id": "global-event-32", "name": "Eco Trail", "text": "Cycle or Trek: gain +5 Inner points", "effect": "eco_trail_reward"}, {"id": "global-event-38", "name": "Excess Baggage", "text": "Hand limit 2 this round, discard down immediately", "effect": "excess_baggage"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-35", "name": "Road Warriors", "text": "Car or Bus: gain +5 Outer points", "effect": "road_warriors_reward"}, {"id": "global-event-39", "name": "Heritage Site Renovations", "text": "No outer journey cards can be collected this round", "effect": "no_outer_journey_cards"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-37", "name": "Central Heart", "text": "<PERSON>ain 5 inner points if you are in Central at the end of turn", "effect": "central_heart_end_turn_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-28", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}], "nameMode": false}