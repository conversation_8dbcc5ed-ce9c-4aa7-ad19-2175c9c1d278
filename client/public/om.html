<html lang="en"><head></head><body data-new-gr-c-s-check-loaded="14.1231.0" data-gr-ext-installed="" data-new-gr-c-s-loaded="14.1243.0">\
    <meta charset="utf-8">
    <title>Om: The Journey</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script defer="" src="/static/js/bundle.js"></script>
    <style>
    /* With a location pin for experimentation */

        /* This HTML represents the full board layout of the game without any cubes to serve as a prototype for the physical board print*/
        /* client/src/styles/main.css */

        :root {
            --primary-color: #6C63FF;
            --secondary-color: #FF6584;
            --accent-color: #43C59E;
            --dark-color: #333333;
            --light-color: #FFFFFF;
            --background-color: #F8F9FA;
            --card-background: #FFFFFF;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;

            /* Energy Colors */
            --artha-color: #F39C12;
            --karma-color: #27AE60;
            --gnana-color: #2980B9;
            --bhakti-color: #8E44AD;

            /* Region Colors - More vibrant and distinct */
            --north-color: rgba(33, 150, 243, 0.95);
            /* Vibrant blue */
            --west-color: rgba(76, 175, 80, 0.95);
            /* Vibrant green */
            --south-color: rgba(255, 191, 102, 0.95);
            /* Light orange */
            --central-color: rgba(156, 39, 176, 0.95);
            /* Vibrant purple */
            --east-color: #fd7c03ef;
            /* Orange */
            --northeast-color: rgba(233, 30, 99, 0.95);
            /* Vibrant pink */
            --jyotirlinga-color: rgba(255, 236, 130, 0.95);
            /* Light yellow for jyotirlingas */
            --airport-color: rgba(103, 58, 183, 0.95);
            /* Vibrant indigo for airports */

            /* Journey Types */
            --inner-color: rgba(25, 118, 210, 0.95);
            /* Dark blue for inner journey */
            --outer-color: rgba(56, 142, 60, 0.95);
            /* Dark green for outer journey */
        }

        /* Base Styles */
        body {
            margin: 0;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
                Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background: var(--background-color);
            color: var(--dark-color);
            line-height: 1.6;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 0.5rem 0;
            font-weight: 600;
            line-height: 1.2;
        }

        h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
        }

        h2 {
            font-size: 1.8rem;
            color: var(--dark-color);
        }

        h3 {
            font-size: 1.5rem;
        }

        /* Button Styles */
        button {
            cursor: pointer;
            padding: 0.6rem 1.2rem;
            background: var(--primary-color);
            color: var(--light-color);
            border: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.9rem;
            transition: var(--transition);
            box-shadow: var(--box-shadow);
        }

        button:hover {
            background: #5A52D5;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        button.secondary {
            background: var(--secondary-color);
        }

        button.secondary:hover {
            background: #E55A78;
        }

        button.accent {
            background: var(--accent-color);
        }

        button.accent:hover {
            background: #3AB08D;
        }

        /* Form Elements */
        input {
            padding: 0.8rem 1rem;
            border: 1px solid #DDD;
            border-radius: var(--border-radius);
            font-size: 1rem;
            width: 100%;
            transition: var(--transition);
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);
        }

        /* Card Styles */
        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--box-shadow);
        }

        /* Player Mat Styles */
        .player-mat {
            border-radius: var(--border-radius);
            padding: 1rem;
            background: var(--card-background);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .player-mat.active {
            border: 2px solid var(--accent-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        /* Game Board Layout */
        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .board-container {
            margin: 2rem 0;
        }

        .face-up-cards {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 1.5rem 0;
        }

        .card-item {
            background: var(--card-background);
            border-radius: var(--border-radius);
            padding: 1rem;
            width: 150px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .card-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        /* Energy Cube Visual Styles */
        .energy-cube {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 5px;
        }

        .energy-cube.artha {
            background-color: var(--artha-color);
        }

        .energy-cube.karma {
            background-color: var(--karma-color);
        }

        .energy-cube.gnana {
            background-color: var(--gnana-color);
        }

        .energy-cube.bhakti {
            background-color: var(--bhakti-color);
        }

        /* Selectable Energy Cubes */
        .energy-cube.selectable {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid transparent;
        }

        .energy-cube.selectable:hover {
            transform: translateY(-3px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
        }

        .energy-cube.selected {
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #333;
            transform: translateY(-3px);
        }

        /* Character Card Styles */
        .character-card {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 12px;
            border: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .character-card-image {
            width: 120px;
            height: 120px;
            background-color: #eee;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            color: #555;
            border: 1px solid #ccc;
            overflow: hidden;
            object-fit: cover;
            position: relative;
        }

        .character-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .character-card-title {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 4px;
            text-align: center;
        }

        .character-card-description {
            font-size: 0.8rem;
            text-align: center;
            color: #555;
        }

        .trade-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            margin-top: 8px;
        }

        /* SVG Board Styling */
        .board-svg {
            width: 100%;
            height: auto;
            max-height: 70vh;
            display: block;
            margin: 0 auto;
            background-color: transparent;
        }

        /* Full-screen board styles */
        .board-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            background-color: #ecf5fe;
            /* Slightly darker sky blue for fullscreen */
            overflow: hidden;
        }

        .board-fullscreen-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .board-fullscreen-content {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            padding: 0 180px;
            /* Adjust padding to match sidebar width */
        }

        .board-fullscreen-svg {
            width: 100%;
            height: 100%;
            max-width: none;
            max-height: none;
        }

        /* Location labels with improved visibility */
        .location-label {
            pointer-events: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .location-label-bg {
            fill: transparent;
            stroke: transparent;
            stroke-width: 0;
            rx: 8;
            ry: 8;
            filter: none;
        }

        .location-label-text {
            font-size: 8.4px;
            font-weight: 700;
            text-anchor: middle;
            fill: #ffffff;
            dominant-baseline: middle;
        }

        /* Improved contrast for node region colors - all white with different border colors */
        .node-region-north,
        .node-region-west,
        .node-region-south,
        .node-region-central,
        .node-region-east,
        .node-region-northeast,
        .node-region-jyotirlinga,
        .node-region-airport {
            fill: #ffffff;
            stroke: #333;
            stroke-width: 2px;
        }

        .node-region-jyotirlinga {
            filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));
        }

        .node-region-airport {
            filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));
        }

        .node-region-jyotirlinga:hover,
        .node-region-airport:hover,
        .node:hover {
            fill: rgba(255, 255, 255, 0.9);
            filter: brightness(1.1) drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.5));
            transform: scale(1.05);
            z-index: 10;
        }

        /* Enhanced styles for node hover effects */
        .node {
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.3));
            transform-origin: center center;
        }

        /* hide the node group circles */
        .node-group circle {
            display: none;
        }

        .node-group {
            transform-box: fill-box;
            transform-origin: center center;
        }

        .node-group:hover .location-label-background {
            fill: transparent;
            stroke: transparent;
            stroke-width: 0;
            filter: none;
            transform: scale(1.02);
        }

        /* Enhanced location labels with improved visibility */
        .node-label-container {
            pointer-events: none;
            -webkit-user-select: none;
            user-select: none;
            font-family: ui-serif;
        }

        .hide-labels .node-label-container {
            display: none;
        }

        .node-group:hover .enhanced-location-label {
            font-weight: 800;
            fill: #ffffff;
        }

        /* Enhanced labels for full-screen mode */
        .enhanced-location-label {
            font-size: 14px;
            font-weight: 700;
            text-anchor: middle;
            fill: #ffffff;
            dominant-baseline: middle;
            text-shadow: 0px 0px 3px rgba(0, 0, 0, 1);
        }

        .location-label-background {
            fill: transparent;
            stroke: transparent;
            stroke-width: 0;
            rx: 8;
            ry: 8;
            filter: none;
            transition: all 0.2s ease;
        }

        /* Line styling - now applied to path elements */
        path {
            stroke: #ffffff;
            stroke-width: 2.5;
            stroke-linecap: round;
            opacity: 0.85;
            fill: none;
            filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.2));
            transition: all 0.2s ease;
        }

        path:hover {
            opacity: 1;
            stroke-width: 3.5;
            filter: drop-shadow(0px 0px 5px rgba(255, 255, 255, 0.6));
        }

        /* Board controls styling */
        .board-controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            display: flex;
            gap: 8px;
            background: rgba(255, 255, 255, 0.8);
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        /* Layout Helpers */
        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .gap-sm {
            gap: 0.5rem;
        }

        .gap-md {
            gap: 1rem;
        }

        .gap-lg {
            gap: 2rem;
        }

        .mt-sm {
            margin-top: 0.5rem;
        }

        .mt-md {
            margin-top: 1rem;
        }

        .mt-lg {
            margin-top: 2rem;
        }

        /* Game Over Screen */
        .game-over {
            text-align: center;
            max-width: 600px;
            margin: 3rem auto;
            padding: 2rem;
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .game-over h1 {
            font-size: 3rem;
            margin-bottom: 1.5rem;
        }

        /* Loading State */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .loading::after {
            content: "...";
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {

            0%,
            20% {
                content: '.';
            }

            40% {
                content: '..';
            }

            60% {
                content: '...';
            }

            80%,
            100% {
                content: '';
            }
        }

        /* App Navigation */
        .app-navigation {
            display: flex;
            background-color: #2c3e50;
            padding: 0 20px;
        }

        .app-navigation a {
            padding: 15px 20px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
            position: relative;
            text-decoration: none;
            display: inline-block;
        }

        .app-navigation a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .app-navigation a.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .app-navigation a.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #3498db;
        }

        /* Styles for compact components in fullscreen mode */
        .compact-player-mat.active {
            box-shadow: 0 0 8px var(--accent-color);
        }

        .energy-cube.artha,
        .energy-cube.karma,
        .energy-cube.gnana,
        .energy-cube.bhakti {
            border-radius: 3px;
        }

        /* Scrollbars for fullscreen sidebars */
        .board-fullscreen ::-webkit-scrollbar {
            width: 6px;
        }

        .board-fullscreen ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .board-fullscreen ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .board-fullscreen ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        /* Highlighted node animation */
        .node-highlighted {
            animation: pulse 1.5s infinite alternate;
            filter: drop-shadow(0 0 8px rgba(255, 87, 34, 0.8)) !important;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
            }

            100% {
                transform: scale(1.08);
                filter: drop-shadow(0 0 10px rgba(255, 87, 34, 0.9));
            }
        }

        /* Player movement animation */
        @keyframes moveAnimation {
            0% {
                transform: scale(0.95);
                filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
            }

            50% {
                transform: scale(1.1);
                filter: drop-shadow(0 0 15px rgba(255, 87, 34, 0.9));
            }

            100% {
                transform: scale(0.95);
                filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
            }
        }

        /* Animation for card picking */
        .card-animation-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
        }

        .animated-card {
            position: absolute;
            transition: all 1.8s cubic-bezier(0.2, 0.8, 0.2, 1.0);
            /* Slowed down by 20% */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            animation: cardGlow 1.8s infinite alternate;
            /* Slowed down by 20% */
        }

        @keyframes cardGlow {
            0% {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            100% {
                box-shadow: 0 4px 20px rgba(25, 118, 210, 0.7);
            }
        }

        /* Make sure the animation is visible for a longer time */
        .card-animation-container .animated-card {
            opacity: 1 !important;
            transition-duration: 1.8s !important;
        }

        /*# sourceMappingURL=data:application/json;base64,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 */
    </style>
    <style>
        /* SimulationDashboard.css */
        .simulation-dashboard {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            color: #333;
            background-color: #f9f9f9;
            min-height: 100vh;
        }

        .simulation-dashboard h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }

        .simulation-dashboard h2 {
            color: #3498db;
            margin: 20px 0 15px;
            font-size: 1.4rem;
        }

        .simulation-dashboard h3 {
            color: #2c3e50;
            margin: 15px 0 10px;
            font-size: 1.1rem;
        }

        /* Loading and Error States */
        .simulation-dashboard.loading,
        .simulation-dashboard.error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 70vh;
            text-align: center;
        }

        .simulation-dashboard.error button {
            margin-top: 20px;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* Navigation */
        .dashboard-nav {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .dashboard-nav button {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #7f8c8d;
            position: relative;
        }

        .dashboard-nav button.active {
            color: #3498db;
        }

        .dashboard-nav button.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: #3498db;
        }

        /* Overview Tab */
        .dashboard-overview {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .metric-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px;
            text-align: center;
        }

        .metric-card h3 {
            font-size: 0.9rem;
            margin: 0 0 10px;
            color: #7f8c8d;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .pulse-dot {
            width: 10px;
            height: 10px;
            background-color: #27ae60;
            border-radius: 50%;
            display: inline-block;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
            }
        }

        /* Player Statistics */
        .player-stats-panel {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-x: auto;
        }

        .player-stats-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .player-stats-table th,
        .player-stats-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .player-stats-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #7f8c8d;
        }

        /* Event Distribution */
        .event-stats-panel {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .event-bars {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .event-bar-container {
            display: flex;
            align-items: center;
        }

        .event-label {
            width: 150px;
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .event-bar-wrapper {
            flex: 1 1;
            display: flex;
            align-items: center;
            height: 24px;
        }

        .event-bar {
            height: 24px;
            background-color: #3498db;
            border-radius: 4px;
            min-width: 2px;
        }

        .event-count {
            margin-left: 10px;
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        /* Control Panel */
        .dashboard-control {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .simulation-config {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-group label {
            font-weight: bold;
            color: #7f8c8d;
        }

        .bot-config {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .bot-strategy-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bot-strategy-selector select {
            flex: 1 1;
            padding: 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .remove-bot {
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            width: 28px;
            height: 28px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-bot {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
            margin-top: 10px;
            align-self: flex-start;
        }

        .simulation-controls {
            margin-top: 30px;
            display: flex;
            justify-content: center;
        }

        .start-simulation,
        .stop-simulation {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-weight: bold;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .start-simulation {
            background-color: #27ae60;
            color: white;
        }

        .start-simulation:hover {
            background-color: #2ecc71;
        }

        .stop-simulation {
            background-color: #e74c3c;
            color: white;
        }

        .stop-simulation:hover {
            background-color: #f5574a;
        }

        .current-simulation {
            margin-top: 30px;
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e0e0e0;
        }

        .progress-info {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        /* Simulation Details */
        .dashboard-details {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .simulation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .simulation-meta {
            display: flex;
            gap: 20px;
        }

        .winner-panel {
            margin-bottom: 30px;
        }

        .winner-card {
            background-color: #fff9e0;
            border: 2px solid #f1c40f;
            border-radius: 8px;
            padding: 15px;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-top: 15px;
            max-width: 300px;
        }

        .winner-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .winner-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: #f1c40f;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .players-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .player-card {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }

        .player-card.winner {
            border-color: #f1c40f;
            background-color: #fff9e0;
        }

        .player-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .player-score-breakdown {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .player-om {
            font-weight: bold;
            color: #8e44ad;
        }

        .events-panel {
            margin-top: 30px;
        }

        .event-filters {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .event-filters button {
            padding: 6px 12px;
            background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
        }

        .event-filters button:hover {
            background-color: #e9ecef;
        }

        .events-log {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .events-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .events-table th,
        .events-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .events-table th {
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
        }

        .event-detail {
            margin-bottom: 5px;
        }

        .detail-key {
            font-weight: bold;
            margin-right: 5px;
        }

        /* Event type styling */
        .event-type-MOVE {
            background-color: #e8f7f9;
        }

        .event-type-PICK_CARDS {
            background-color: #e8f4f9;
        }

        .event-type-COLLECT_JOURNEY {
            background-color: #f9f4e8;
        }

        .event-type-END_TURN {
            background-color: #f9e8e8;
        }

        .event-type-GAME_START {
            background-color: #e8f9e8;
        }

        .event-type-GAME_END {
            background-color: #f9e8f9;
        }

        .no-events {
            padding: 20px;
            text-align: center;
            color: #7f8c8d;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .dashboard-overview {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 1200px) {
            .dashboard-overview {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .simulation-meta {
                flex-direction: column;
                gap: 5px;
            }

            .metric-card {
                padding: 10px;
            }

            .metric-value {
                font-size: 1.5rem;
            }

            .player-score-breakdown {
                grid-template-columns: 1fr;
            }
        }

        /*# sourceMappingURL=data:application/json;base64,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 */
    </style>
    <style>
        /* Simulation Page Styles */
        .simulation-page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f8f9fa;
        }

        .simulation-header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .simulation-header h1 {
            margin: 0;
            font-size: 1.4rem;
        }

        .header-controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }

        .header-controls button:hover {
            background-color: #2980b9;
        }

        .simulation-container {
            flex: 1 1;
            padding: 0;
            max-width: 100%;
            width: 100%;
        }

        .simulation-footer {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        /*# sourceMappingURL=data:application/json;base64,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 */
    </style>
<style>.node-region-north {
      fill: #375a60;
      stroke: #333;
      stroke-width: 2px;
    }</style><style>.node-region-west {
    fill: #486537;
    stroke: #333;
    stroke-width: 2px;
  }</style><style>.node-region-central {
    fill: #7a4e5d !important;
    stroke: #333 !important;
    stroke-width: 2px !important;
  }</style><style>.node-region-northeast {
    fill: #b0422b !important;
    stroke: #333 !important;
    stroke-width: 2px !important;
  }</style><style>.node-region-east {
    fill: #c0722a !important;
    stroke: #333 !important;
    stroke-width: 2px !important;
  }</style><style>.node-region-south {
    fill: #c69227 !important;
    stroke: #333 !important;
    stroke-width: 2px !important;
  }</style><link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&amp;display=swap" rel="stylesheet">




    <div id="root">
        <div class="app-navigation"><a class="active" href="/" data-discover="true">Game</a><a href="/simulation" data-discover="true">Simulation Dashboard</a></div>
        <div class="flex-column">
            <div class="flex-grow">
                <div class="interactive-board">
                    <div class="board-header" style="display: flex; justify-content: space-between; align-items: center; padding: 10px; background: rgb(245, 245, 245); border-bottom: 1px solid rgb(221, 221, 221);">
                        <div>
                            <h3>Game Board</h3>
                            <p style="font-size: 0.9rem; color: rgb(102, 102, 102);">Your current position:
                                <strong>63</strong></p>
                        </div>
                        <div class="flex gap-sm"><button class="secondary">Hide Labels</button><button class="secondary">Show Cubes</button><button class="secondary"><span role="img" aria-label="Full Screen">🔍</span> Exit Full Screen</button></div>
                    </div>
                    <div class="board-area" style="position: relative; height: calc(-60px + 100vh); overflow: hidden;">
                        <div class="board-fullscreen" style="position: fixed; inset: 0px; background-color: rgb(240, 248, 255); z-index: 1000; overflow: hidden;">
                            <div style="position: absolute; top: 70px; left: 15px; bottom: 15px; width: 250px; overflow-y: auto; z-index: 5; padding: 5px; display: none; flex-direction: column; gap: 8px; transition: 0.3s;">
                                <div style="margin-bottom: 10px;">
                                    <div style="font-size: 0.9rem; font-weight: bold; background-color: rgba(245, 245, 245, 0.85); padding: 6px 8px; border-radius: 8px; margin-bottom: 4px; display: flex; align-items: center; justify-content: space-between;">
                                        <span>Energy Cubes: </span>
                                        <div style="display: flex; gap: 8px;">
                                            <div style="display: flex; align-items: center;">
                                                <div class="energy-cube artha" style="width: 12px; height: 12px; margin-right: 3px;"></div><span style="font-size: 0.8rem;">2</span>
                                            </div>
                                            <div style="display: flex; align-items: center;">
                                                <div class="energy-cube karma" style="width: 12px; height: 12px; margin-right: 3px;"></div><span style="font-size: 0.8rem;">2</span>
                                            </div>
                                            <div style="display: flex; align-items: center;">
                                                <div class="energy-cube gnana" style="width: 12px; height: 12px; margin-right: 3px;"></div><span style="font-size: 0.8rem;">2</span>
                                            </div>
                                            <div style="display: flex; align-items: center;">
                                                <div class="energy-cube bhakti" style="width: 12px; height: 12px; margin-right: 3px;"></div><span style="font-size: 0.8rem;">2</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="compact-player-mat active  " data-player-id="geYpgQxtwuy342OYAAAD" style="width: 230px; padding: 8px; background-color: rgba(255, 255, 240, 0.95); border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; margin: 4px 0px; font-size: 0.8rem; border: 2px solid var(--accent-color); position: relative;">
                                        <h4 style="margin: 0px 0px 5px; padding: 0px 0px 5px; border-bottom: 1px solid rgb(221, 221, 221); display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem;">
                                            <span>1</span><span style="font-size: 0.75rem;">0/0</span></h4>
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; font-size: 0.8rem;">
                                            <div>Pos: 63</div>
                                            <div><strong>Score: O: 0 I: 0</strong></div>
                                            <div>OM: 0</div>
                                        </div>
                                        <div style="display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; margin-bottom: 5px;">
                                            <div style="display: flex; align-items: center;">
                                                <div title="Click to view character details" style="display: flex; align-items: center; background: rgba(240, 240, 240, 0.5); padding: 3px 6px; border-radius: 4px; margin-right: 6px; cursor: pointer;">
                                                    <div style="width: 24px; height: 24px; border-radius: 4px; background: rgb(221, 221, 221); display: flex; justify-content: center; align-items: center; font-size: 8px; margin-right: 4px; font-weight: bold; overflow: hidden;">
                                                        <img src="/assets/images/characters/pilgrim.jpg" alt="Pilgrim" class="character-debug-img-compact" style="width: 100%; height: 100%; object-fit: cover;"></div>
                                                    <span style="font-size: 0.7rem;">Pilgrim</span>
                                                </div>
                                            </div>
                                            <div style="display: flex; flex-direction: column; margin-top: 8px;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; gap: 6px; margin-bottom: 6px;">
                                                    <button style="padding: 2px 6px; font-size: 0.7rem; background-color: var(--primary-color); color: white; border: none; border-radius: 3px; cursor: pointer; box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 3px;">Travel</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="margin-top: 6px; border-top: 1px solid rgb(238, 238, 238); padding-top: 6px;">
                                            <div style="font-size: 0.75rem; font-weight: bold; margin-bottom: 2px;">Hand
                                                Cards (2/5):</div>
                                            <div>
                                                <div>
                                                    <div style="display: flex; flex-wrap: wrap; margin-bottom: 4px;">
                                                        <div style="width: 52px; height: 75px; background-color: rgb(245, 245, 245); border-radius: 4px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 4px 0px 0px; padding: 2px; position: relative; overflow: hidden;">
                                                            <img src="/assets/images/vehicles/1/camel.png" alt="camel (1)" style="width: 100%; height: 100%; object-fit: contain;">
                                                        </div>
                                                        <div style="width: 52px; height: 75px; background-color: rgb(245, 245, 245); border-radius: 4px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 4px 0px 0px; padding: 2px; position: relative; overflow: hidden;">
                                                            <img src="/assets/images/vehicles/3/boat.png" alt="boat (3)" style="width: 100%; height: 100%; object-fit: contain;">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-top: 8px; justify-content: space-between; font-size: 0.8rem;">
                                            <div><span style="font-weight: bold;">Pos: </span>63</div>
                                        </div>
                                    </div>
                                    <div class="compact-player-mat   " data-player-id="eA5yfG6CBzJYtOp4AAAB" style="width: 230px; padding: 8px; background-color: rgba(245, 245, 245, 0.85); border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; margin: 4px 0px; font-size: 0.8rem; border: 1px solid rgb(221, 221, 221); position: relative;">
                                        <h4 style="margin: 0px 0px 5px; padding: 0px 0px 5px; border-bottom: 1px solid rgb(221, 221, 221); display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem;">
                                            <span>2</span><span style="font-size: 0.75rem;">0/0</span></h4>
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; font-size: 0.8rem;">
                                            <div>Pos: 61</div>
                                            <div><strong>Score: O: 0 I: 0</strong></div>
                                            <div>OM: 0</div>
                                        </div>
                                        <div style="display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; margin-bottom: 5px;">
                                            <div style="display: flex; align-items: center;">
                                                <div title="Click to view character details" style="display: flex; align-items: center; background: rgba(240, 240, 240, 0.5); padding: 3px 6px; border-radius: 4px; margin-right: 6px; cursor: pointer;">
                                                    <div style="width: 24px; height: 24px; border-radius: 4px; background: rgb(221, 221, 221); display: flex; justify-content: center; align-items: center; font-size: 8px; margin-right: 4px; font-weight: bold; overflow: hidden;">
                                                        <img src="/assets/images/characters/engineer.jpg" alt="Engineer" class="character-debug-img-compact" style="width: 100%; height: 100%; object-fit: cover;"></div>
                                                    <span style="font-size: 0.7rem;">Engineer</span>
                                                </div>
                                            </div>
                                            <div style="display: flex; flex-direction: column; margin-top: 8px;">
                                                <div style="display: flex; justify-content: space-between; align-items: center; gap: 6px; margin-bottom: 6px;">
                                                </div>
                                            </div>
                                        </div>
                                        <div style="margin-top: 6px; border-top: 1px solid rgb(238, 238, 238); padding-top: 6px;">
                                            <div style="font-size: 0.75rem; font-weight: bold; margin-bottom: 2px;">Hand
                                                Cards (2/5):</div>
                                            <div>
                                                <div>
                                                    <div style="display: flex; flex-wrap: wrap; margin-bottom: 4px;">
                                                        <div style="width: 52px; height: 75px; background-color: rgb(245, 245, 245); border-radius: 4px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 4px 0px 0px; padding: 2px; position: relative; overflow: hidden;">
                                                            <img src="/assets/images/vehicles/2/motorbike.png" alt="motorbike (2)" style="width: 100%; height: 100%; object-fit: contain;">
                                                        </div>
                                                        <div style="width: 52px; height: 75px; background-color: rgb(245, 245, 245); border-radius: 4px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 4px 0px 0px; padding: 2px; position: relative; overflow: hidden;">
                                                            <img src="/assets/images/vehicles/1/horse.png" alt="horse (1)" style="width: 100%; height: 100%; object-fit: contain;">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="display: flex; align-items: center; margin-top: 8px; justify-content: space-between; font-size: 0.8rem;">
                                            <div><span style="font-weight: bold;">Pos: </span>61</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="position: absolute; bottom: 15px; left: 15px; width: 250px; z-index: 5; padding: 5px; display: none; transition: 0.3s;">
                                <div class="compact-cards-container" style="padding: 8px; background-color: rgb(245, 245, 245); border-radius: 8px; margin-bottom: 16px;">
                                    <div style="margin-bottom: 10px;">
                                        <div style="font-weight: bold; font-size: 0.8rem; margin-bottom: 5px; color: rgb(230, 81, 0);">
                                            Global Event</div>
                                        <div class="global-event-card" style="width: 100%; background-color: rgb(255, 249, 196); border-radius: 4px; border: 1px solid rgb(251, 192, 45); padding: 3px; box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px; margin-bottom: 5px;">
                                            <div style="font-weight: bold; font-size: 0.7rem; color: rgb(230, 81, 0); margin-bottom: 2px; text-align: center;">
                                                Scenic Cruise</div>
                                            <div style="font-size: 0.7rem; text-align: center; color: rgb(66, 66, 66);">
                                                Use the Boat travel card for 5 outer points</div>
                                        </div>
                                    </div>
                                    <div style="margin-bottom: 12px;">
                                        <div style="font-weight: bold; font-size: 0.9rem; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center; color: rgb(25, 118, 210);">
                                            <span>Travel Cards</span>
                                            <div><button disabled="" style="padding: 4px 8px; margin-right: 4px; background-color: rgb(224, 224, 224); color: rgb(158, 158, 158); border: none; border-radius: 4px; cursor: default; font-size: 0.7rem;">Pick
                                                    Selected (0)</button><button style="padding: 4px 8px; background-color: rgb(25, 118, 210); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.7rem;">From
                                                    Deck</button></div>
                                        </div>
                                        <div style="display: flex; flex-wrap: wrap;">
                                            <div class="compact-card travel-card " style="width: 75px; height: 70px; background-color: rgb(255, 248, 225); border-radius: 6px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; padding: 4px; position: relative; cursor: pointer; transition: 0.2s; overflow: hidden;">
                                                <img src="/assets/images/vehicles/1/cycle.png" alt="cycle (1)" style="width: 100%; height: 100%; object-fit: contain;"></div>
                                            <div class="compact-card travel-card " style="width: 75px; height: 70px; background-color: rgb(255, 248, 225); border-radius: 6px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; padding: 4px; position: relative; cursor: pointer; transition: 0.2s; overflow: hidden;">
                                                <img src="/assets/images/vehicles/2/car.png" alt="car (2)" style="width: 100%; height: 100%; object-fit: contain;"></div>
                                            <div class="compact-card travel-card " style="width: 75px; height: 70px; background-color: rgb(255, 248, 225); border-radius: 6px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; padding: 4px; position: relative; cursor: pointer; transition: 0.2s; overflow: hidden;">
                                                <img src="/assets/images/vehicles/3/train.png" alt="train (3)" style="width: 100%; height: 100%; object-fit: contain;"></div>
                                            <div class="compact-card travel-card " style="width: 75px; height: 70px; background-color: rgb(255, 248, 225); border-radius: 6px; border: 1px solid rgb(25, 118, 210); display: flex; justify-content: center; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; padding: 4px; position: relative; cursor: pointer; transition: 0.2s; overflow: hidden;">
                                                <img src="/assets/images/vehicles/3/helicopter.png" alt="helicopter (3)" style="width: 100%; height: 100%; object-fit: contain;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="position: absolute; top: 15px; right: 15px; bottom: 15px; width: 420px; overflow-y: auto; z-index: 10; padding: 5px; display: none; flex-direction: column; gap: 8px; background: rgba(255, 255, 255, 0.85); border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 8px; transition: 0.3s;">
                                <div style="font-size: 1rem; font-weight: bold; margin-bottom: 5px; padding: 5px; border-bottom: 1px solid rgb(221, 221, 221); text-align: center;">
                                    Journey Cards</div>
                                <div class="compact-journey-cards-container" style="background-color: rgba(245, 245, 245, 0.85); border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; padding: 8px; width: 100%; max-width: 450px; margin: 0px;">
                                    <div>
                                        <div style="font-size: 0.8rem; margin-top: 0px; margin-bottom: 4px; font-weight: bold;">
                                            Outer Journey:</div>
                                        <div style="display: flex; flex-wrap: wrap;">
                                            <div class="compact-card journey-card outer " style="width: 110px; height: 160px; background-color: rgb(255, 243, 224); border-radius: 8px; border: 1px solid rgb(245, 124, 0); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--northeast-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(230, 81, 0); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(245, 124, 0, 0.2); color: rgb(230, 81, 0); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(245, 124, 0);">45</span><span>Kangla
                                                                Fort</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/45.png" alt="Kangla Fort" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(255, 152, 0); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        20</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube karma" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card outer " style="width: 110px; height: 160px; background-color: rgb(255, 243, 224); border-radius: 8px; border: 1px solid rgb(245, 124, 0); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--northeast-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(230, 81, 0); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(245, 124, 0, 0.2); color: rgb(230, 81, 0); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(245, 124, 0);">43</span><span>Living
                                                                Root Bridge</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/43.png" alt="Living Root Bridge" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(255, 152, 0); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        27</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube karma" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube artha" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card outer " style="width: 110px; height: 160px; background-color: rgb(255, 243, 224); border-radius: 8px; border: 1px solid rgb(245, 124, 0); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--west-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(230, 81, 0); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(245, 124, 0, 0.2); color: rgb(230, 81, 0); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(245, 124, 0);">12</span><span>Jaisalmer
                                                                Fort</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/12.png" alt="Jaisalmer Fort" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(255, 152, 0); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        30</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube karma" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube artha" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card outer " style="width: 110px; height: 160px; background-color: rgb(255, 243, 224); border-radius: 8px; border: 1px solid rgb(245, 124, 0); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--central-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(230, 81, 0); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(245, 124, 0, 0.2); color: rgb(230, 81, 0); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(245, 124, 0);">5</span><span>Kurukshetra</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/5.png" alt="Kurukshetra" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(255, 152, 0); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        24</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube karma" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube artha" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div style="font-size: 0.8rem; margin-top: 8px; margin-bottom: 4px; font-weight: bold;">
                                            Inner Journey:</div>
                                        <div style="display: flex; flex-wrap: wrap;">
                                            <div class="compact-card journey-card inner " style="width: 110px; height: 160px; background-color: rgb(232, 234, 246); border-radius: 8px; border: 1px solid rgb(63, 81, 181); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--west-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(40, 53, 147); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(63, 81, 181, 0.2); color: rgb(40, 53, 147); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(63, 81, 181);">19</span><span>Basilica
                                                                of Bom Jesus</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/19.png" alt="Basilica of Bom Jesus" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(63, 81, 181); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        27</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube bhakti" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube gnana" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card inner " style="width: 110px; height: 160px; background-color: rgb(232, 234, 246); border-radius: 8px; border: 1px solid rgb(63, 81, 181); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--northeast-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(40, 53, 147); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(63, 81, 181, 0.2); color: rgb(40, 53, 147); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(63, 81, 181);">48</span><span>Rumtek
                                                                Monastery</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/48.png" alt="Rumtek Monastery" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(63, 81, 181); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        30</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube bhakti" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube gnana" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card inner " style="width: 110px; height: 160px; background-color: rgb(232, 234, 246); border-radius: 8px; border: 1px solid rgb(63, 81, 181); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--south-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(40, 53, 147); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(63, 81, 181, 0.2); color: rgb(40, 53, 147); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(63, 81, 181);">23</span><span>Sabarimala</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/23.png" alt="Sabarimala" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(63, 81, 181); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        27</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube bhakti" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">1</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube gnana" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="compact-card journey-card inner " style="width: 110px; height: 160px; background-color: rgb(232, 234, 246); border-radius: 8px; border: 1px solid rgb(63, 81, 181); display: flex; flex-direction: column; justify-content: space-between; align-items: center; margin: 0px 6px 6px 0px; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px; padding: 4px; font-size: 0.7rem; overflow: hidden; cursor: pointer; transition: 0.2s; position: relative;">
                                                <div style="display: flex; align-items: center; justify-content: center; width: 100%; margin-bottom: 2px;">
                                                    <div style="display: flex; align-items: center; gap: 4px; width: 100%;">
                                                        <div style="width: 10px; height: 10px; border-radius: 50%; background-color: var(--north-color); border: 1px solid rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                        </div>
                                                        <div style="font-size: 0.7rem; font-weight: bold; color: rgb(40, 53, 147); text-align: left; flex: 1 1 0%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: flex; align-items: center; gap: 2px;">
                                                            <span style="font-size: 0.6rem; background-color: rgba(63, 81, 181, 0.2); color: rgb(40, 53, 147); border-radius: 4px; padding: 1px 2px; font-weight: bold; min-width: 14px; text-align: center; border: 1px solid rgb(63, 81, 181);">3</span><span>Golden
                                                                Temple</span></div>
                                                    </div>
                                                </div>
                                                <div style="width: 95%; height: 60px; border-radius: 4px; position: relative; margin-bottom: 2px; overflow: hidden;">
                                                    <img src="/assets/images/cards/3.png" alt="Golden Temple" style="width: 100%; height: 100%; object-fit: cover; object-position: center center;">
                                                    <div style="position: absolute; bottom: 2px; right: 2px; background-color: rgb(63, 81, 181); color: white; width: 22px; height: 22px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.8rem; font-weight: bold; border: 1px solid white; box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 3px;">
                                                        30</div>
                                                </div>
                                                <div style="margin-bottom: 2px;">
                                                    <div style="display: flex; flex-wrap: wrap; justify-content: center; margin-bottom: 2px;">
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube bhakti" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                        <div style="display: flex; align-items: center; margin: 0px 2px;">
                                                            <div class="energy-cube gnana" style="width: 12px; height: 12px; margin: 0px 2px 0px 0px;">
                                                            </div><span style="font-size: 0.7rem;">2</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="board-fullscreen-content" style="cursor: grab; position: absolute; inset: 0px; margin-right: 0px; display: flex; justify-content: center; align-items: center; transition: 0.3s;">
                                <svg viewBox="-100 0 1800 1400" class="board-fullscreen-svg " style="transform: scale(1) translate(-250px, 0px); transform-origin: center center; width: 100%; height: 100%; margin: 0px auto;">
                                    <symbol id="map-pin" viewBox="0 0 50 50">
                                        <path d="M25.015 2.4c-7.8 0-14.121 6.204-14.121 13.854
                                                 0 7.652 14.121 32.746 14.121 32.746
                                                 s14.122-25.094 14.122-32.746
                                                 c0-7.65-6.325-13.854-14.122-13.854z"></path>
                                      </symbol>
                                    <image href="/assets/images/map_take_2.jpg" width="1800" height="1400" x="-100" y="0" preserveAspectRatio="xMidYMid slice"></image>
                                    <path d="M 350 220 Q 434 242 510 200" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 250 380 Q 201 285 100 250" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path><path d="M 350 220 Q 349 324 420 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 100 250 Q 201 215 250 120" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 510 200 Q 590.7232500091137 290.0486363470659 710 310" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 710 310 Q 640 340 610 410" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 610 410 Q 516 442 470 530" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 470 530 Q 382.5334729856604 421.9509062876981 250 380" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 350 220 Q 414 176 430 100" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 430 100 Q 336 74 250 120" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 710 310 Q 714 213 650 140" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
                                    <path d="M 610 410 Q 517 367 420 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 470 530 Q 471 455 420 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 250 380 Q 225 457 260 530" fill="none" stroke="#ffffff" stroke-width="7.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 710 310 Q 843.1440314051019 393.20256547244946 1000 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 120 730 Q 222.7692768207616 652.9384937745332 260 530" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 260 530 Q 291.21965686201287 653.9216810143333 390 735" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 390 735 Q 360 795.5 380 860" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 380 860 Q 302 852 240 900" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 240 900 Q 180 963 180 1050" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 180 1050 Q 177.59173693953747 926.3302774765318 100 830" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 180 1050 Q 266 1067 340 1020" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 340 1020 Q 392 948 380 860" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
                                    <path d="M 380 860 Q 437 906 510 900" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 120 730 Q 131 659 90 600" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 390 735 Q 410 685 390 635" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 260 530 Q 195.7767729723632 647.1553545944727 210 780" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 240 900 Q 249 834 210 780" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 100 830 Q 165 827 210 780" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 180 1050 Q 289.7330819535106 1191.0093994206916 450 1270" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 580 1200 Q 660.5 1194.5 715 1135" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 715 1135 Q 786 1189.5 875 1180" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 875 1180 Q 900.5 1080 850 990" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 875 1180 Q 956.5 1174.9 1012 1115" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>

                                    <path d="M 1150 1210 Q 1004.9312764091262 1247.8987524295278 900 1355" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 900 1355 Q 810 1319 720 1355" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 720 1355 Q 597.0114350093083 1274.3460299704323 450 1270" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 580 1200 Q 535 1153 470 1150" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1150 1210 Q 1096 1272.5 1100 1355" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 900 1355 Q 840 1273 740 1255" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 720 1355 Q 750 1309 740 1255" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 715 1135 Q 703.5 1200 740 1255" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
<path d="M 850 990 Q 838 904 770 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 550 680 Q 684.6822902667161 649.8225135488053 780 550" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 780 550 Q 838.5351345165261 654.9935142146037 950 700" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 950 700 Q 945 785 1000 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1000 850 Q 885 810 770 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 850 990 Q 952.2927300144005 949.2422107297148 1000 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 770 850 Q 693 796 600 810" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 600 810 Q 537 837 510 900" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 600 810 Q 710.858000378159 789.1312733460784 780 700" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
                                    <path d="M 1012 1115 Q 1062 1190.1 1150 1210" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9" class="ai-style-change-1"></path>
<path d="M 1000 850 Q 1043 974 1150 1050" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 550 680 Q 610 646 630 580" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 950 700 Q 980 625 950 550" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 780 550 Q 750 625 780 700" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 770 850 Q 805 777 780 700" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
                                    <path d="M 1160 750 Q 1284 707 1360 600" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1360 600 Q 1375 664 1430 700" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1430 700 Q 1375.4425458852681 794.0663818827902 1400 900" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1300 850 Q 1255 890 1250 950" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1250 950 Q 1180 980 1150 1050" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1410 1010 Q 1427 953 1400 900" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 1300 1100 Q 1373 1077 1410 1010" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 1150 1050 Q 1215 1105 1300 1100" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1160 750 Q 1215 685 1210 600" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1430 700 Q 1458 775 1530 810" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1360 600 Q 1291.1045079207793 715.665081900987 1300 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1300 1100 Q 1340 975 1300 850" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    
                                    <path d="M 1150 1210 Q 1247 1185 1300 1100" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 1360 600 Q 1305 520 1210 500" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1320 80 Q 1276 132 1280 200" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1280 200 Q 1362.1114561800016 285.77708763999664 1480 300" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1480 300 Q 1395 324 1350 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1350 400 Q 1260 422 1210 500" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1210 500 Q 1122.197343215694 413.88557924704276 1000 400" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1000 400 Q 1117.8885438199984 385.77708763999664 1200 300" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 1000 400 Q 970 330 900 300" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 900 300 Q 978 250 1000 160" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1000 160 Q 1088 172 1160 120" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
<path d="M 1160 120 Q 1248 132 1320 80" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1480 300 Q 1480 213 1420 150" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1280 200 Q 1220 234 1200 300" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1350 400 Q 1295 320 1200 300" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <path d="M 1360 600 Q 1305 520 1210 500" fill="none" stroke="#ffffff" stroke-width="4.5" stroke-linecap="round" opacity="0.9"></path>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/blue_location_board_41.png" width="50" height="50" x="327" y="170"></image>
                                        <g class="node-label-container" transform="translate(370, 260)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="110" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-20" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-3">Vaishno
                                                Devi</text>
                                        </g>
                                        <title>Node 1 (Vaishno Devi)</title>
                                    </g>
                                    <g class="node-group">
                                        /* <circle cx="510" cy="200" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle> */
                                        <image href="/assets/images/pins/blue_location_board_42.png" width="50" height="50" x="485" y="155"></image>
                                        <g class="node-label-container" transform="translate(505, 160)" style="cursor: move;">
                                            <rect x="-30" y="-14" width="70" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-4">Chail</text>
                                        </g>
                                        <title>Node 2 (Chail)</title>
                                    </g>
                                    <g class="node-group">
                                        /* <circle cx="710" cy="310" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle> */
                                        <image href="/assets/images/pins/blue_location_board_43.png" width="50" height="50" x="685" y="265"></image>

                                        <g class="node-label-container" transform="translate(780, 270)" style="
    transform: translate(780px, 270px) rotate(339deg);
">
                                            <rect x="-60" y="-14" width="120" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-5">Golden
                                                Temple</text>
                                        </g>
                                        <title>Node 3 (Golden Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="610" cy="410" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <image href="/assets/images/pins/blue_location_board_44.png" width="50" height="50" x="585" y="365"></image>

                                        <g class="node-label-container" transform="translate(710, 405)" style="cursor: move;">
                                            <rect x="-75" y="-14" width="150" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-6">Durgiana
                                                Temple</text>
                                        </g>
                                        <title>Node 4 (Durgiana Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="550" cy="680" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(642, 680)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="100" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-20" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-7">Kurukshetra</text>
                                        </g>
                                        <title>Node 5 (Kurukshetra)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/blue_location_board_45.png" width="50" height="50" x="445" y="505"></image>

                                        <g class="node-label-container" transform="translate(385, 535)" style="cursor: move;">
                                            <rect x="-45" y="-14" width="100" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-8">Taj
                                                Mahal</text>
                                        </g>
                                        <title>Node 6 (Taj Mahal)</title>
                                    </g>
                                    <g class="node-group"><image href="/assets/images/pins/blue_location_board_47.png" width="50" height="50" x="75" y="225"></image><g class="node-label-container" transform="translate(100, 205)"><rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label">Amarnath Caves</text></g><title>Node 8 (Amarnath Caves)</title></g>
<g class="node-group">

                                        <image href="/assets/images/pins/blue_location_board_46.png" width="50" height="50" x="225" y="355"></image>

                                        <g class="node-label-container" transform="translate(172, 385)" style="cursor: move;">
                                            <rect x="-50" y="-14" width="100" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-9">Haridwar</text>
                                        </g>
                                        <title>Node 7 (Haridwar)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/blue_location_board_48.png" width="50" height="50" x="405" y="75"></image>

                                        <g class="node-label-container" transform="translate(450, 60)" style="cursor: move;">
                                            <rect x="-85" y="-14" width="150" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-10" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-10">Valley of
                                                Flowers</text>
                                        </g>
                                        <title>Node 9 (Valley of Flowers)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_33.png" width="50" height="50" x="95" y="705"></image>
                                        <g class="node-label-container" transform="translate(30, 730)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-11">Ambaji
                                                Temple</text>
                                        </g>
                                        <title>Node 10 (Ambaji Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_34.png" width="50" height="50" x="235" y="505"></image>
                                        <g class="node-label-container" transform="translate(170, 530)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-12">Rann of
                                                Kutch</text>
                                        </g>
                                        <title>Node 11 (Rann of Kutch)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_35.png" width="50" height="50" x="365" y="710"></image>
                                        <g class="node-label-container" transform="translate(485, 735)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="90" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-30" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-25">Jaisalmer</tspan><tspan x="-25" dy="1.2em">Fort</tspan></text>
                                        </g>
                                        <title>Node 12 (Jaisalmer Fort)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_36.png" width="50" height="50" x="355" y="835"></image>
                                        <g class="node-label-container" transform="translate(370, 910)" style="cursor: move;">
                                            <rect x="-30" y="-14" width="75" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="0">Hawa</tspan><tspan x="0" dy="1.2em">Mahal</tspan></text>
                                        </g>
                                        <title>Node 13 (Hawa Mahal)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_37.png" width="50" height="50" x="215" y="875"></image>
                                        <g class="node-label-container" transform="translate(290, 940)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="90" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-30" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-35">Brahma</tspan><tspan x="-35" dy="1.2em">Temple</tspan></text>
                                        </g>
                                        <title>Node 14 (Brahma Temple, Pushkar)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="780" cy="550" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(815, 495)" style="cursor: move;transform: translate(830px, 500px) rotate(339deg);">
                                            <rect x="-45" y="-14" width="100" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="0">Shirdi Sai</tspan><tspan x="0" dy="1.2em">Baba Temple</tspan></text>
                                        </g>
                                        <title>Node 15 (Shirdi Sai Baba Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_38.png" width="50" height="50" x="155" y="1025"></image>
                                        <g class="node-label-container" transform="translate(60, 1050)" style="cursor: move;">
                                            <rect x="-100" y="-14" width="200" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-17">Siddhivinayak Temple</text>
                                        </g>
                                        <title>Node 16 (Siddhivinayak Temple)</title>
                                    </g><g class="node-group"><image href="/assets/images/pins/green_location_board_39.png" width="50" height="50" x="315" y="995"></image><g class="node-label-container" transform="translate(350, 1060)"><rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label">Kailasa Temple</text></g><title>Node 17 (Kailasa Temple)</title></g>
                                    <g class="node-group">
                                        <circle cx="950" cy="700" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1090, 700)" style="cursor: move;transform: translate(1080px, 650px) rotate(335deg);">
                                            <rect x="-115" y="-14" width="90" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-70">Ajanta &amp;</tspan><tspan x="-70" dy="1.2em">Ellora Caves</tspan></text>
                                        </g>
                                        <title>Node 18 (Ajanta and Ellora Caves)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/green_location_board_40.png" width="50" height="50" x="75" y="805"></image>
                                        <g class="node-label-container" transform="translate(15, 830)" style="cursor: move;">
                                            <rect x="-55" y="-14" width="105" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="0">Basilica Of</tspan><tspan x="0" dy="1.2em">Bom Jesus</tspan></text>
                                        </g>
                                        <title>Node 19 (Basilica of Bom Jesus)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_25.png" width="50" height="50" x="555" y="1175"></image>
                                        <g class="node-label-container" transform="translate(580, 1240)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="160" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-20">Meenakshi
                                                Temple</text>
                                        </g>
                                        <title>Node 20 (Meenakshi Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_26.png" width="50" height="50" x="690" y="1110"></image>
                                        <g class="node-label-container" transform="translate(755, 1074)" style="cursor: move;">
                                            <rect x="-95" y="-14" width="130" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-30">Annamalaiyar</tspan><tspan x="-30" dy="1.2em">Temple</tspan></text>
                                        </g>
                                        <title>Node 21 (Annamalaiyar Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_27.png" width="50" height="50" x="850" y="1155"></image>
                                        <g class="node-label-container" transform="translate(910, 1220)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="80" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-10" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-30">Nilgiri</tspan><tspan x="-30" dy="1.2em">Hills</tspan></text>
                                        </g>
                                        <title>Node 22 (Nilgiri Hills)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_28.png" width="50" height="50" x="1125" y="1185"></image>
                                        <g class="node-label-container" transform="translate(1242, 1215)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-23">Sabarimala</text>
                                        </g>
                                        <title>Node 23 (Sabarimala)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_29.png" width="50" height="50" x="875" y="1330"></image>
                                        <g class="node-label-container" transform="translate(970, 1392)" style="cursor: move;">
                                            <rect x="-110" y="-14" width="220" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-24">Padmanabhaswamy Temple</text>
                                        </g>
                                        <title>Node 24 (Padmanabhaswamy Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_30.png" width="50" height="50" x="695" y="1330"></image>
                                        <g class="node-label-container" transform="translate(650, 1392)" style="cursor: move;">
                                            <rect x="-100" y="-14" width="200" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-25">Backwaters of Kerala</text>
                                        </g>
                                        <title>Node 25 (Backwaters of Kerala)</title>
                                    </g>
                                    <g class="node-group">
                                        <image href="/assets/images/pins/orange_location_board_32.png" width="50" height="50" x="425" y="1245"></image>
                                        <g class="node-label-container" transform="translate(450, 1310)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-26">Hampi</text>
                                        </g>
                                        <title>Node 27 (Hampi)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1000" cy="850" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1035, 900)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="90" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-20">Msyore</tspan><tspan x="-20" dy="1.2em">Palace</tspan></text>
                                        </g>
                                        <title>Node 28 (Mysore Palace)</title>
                                    </g>
                                    <g class="node-group"><image href="/assets/images/pins/orange_location_board_31.png" width="50" height="50" x="987" y="1090"></image><g class="node-label-container" transform="translate(1125, 1110)"><rect x="-85" y="-14" width="130" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="-20">Brihadeeswara</tspan><tspan x="-20" dy="1.2em">Temple</tspan></text></g><title>Node 26 (Brihadeeswara Temple)</title></g>
<g class="node-group">
                                        <circle cx="770" cy="850" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(815, 895)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="55" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-45" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-28">Coorg</text>
                                        </g>
                                        <title>Node 29 (Coorg)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1160" cy="750" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1170, 790)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-29">Charminar</text>
                                        </g>
                                        <title>Node 30 (Charminar)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1360" cy="600" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1465, 600)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="160" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-30">Ramoji
                                                Film City</text>
                                        </g>
                                        <title>Node 31 (Ramoji Film City)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1430" cy="700" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1560, 700)" style="cursor: move;">
                                            <rect x="-105" y="-14" width="210" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-31">Tirumala
                                                Venkateswara</text>
                                        </g>
                                        <title>Node 32 (Tirumala Venkateswara)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="600" cy="810" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(630, 770)" style="cursor: move;">
                                            <rect x="-85" y="-14" width="170" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-32">Khajuraho
                                                Temples</text>
                                        </g>
                                        <title>Node 33 (Khajuraho Temples)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="510" cy="900" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(600, 900)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-33">Pachmarhi</text>
                                        </g>
                                        <title>Node 34 (Pachmarhi)</title>
                                    </g>
                     <g class="node-group"><circle cx="850" cy="990" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></circle><g class="node-label-container" transform="translate(780, 990)"><rect x="-90" y="-14" width="135" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="-20" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label">Ram Raja Temple</text></g><title>Node 35 (Shri Ram Raja Mandir)</title></g>               
                                    <g class="node-group"><circle cx="1410" cy="1010" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></circle><g class="node-label-container" transform="translate(1510, 1010)"><rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label">Parsuram Kund</text></g><title>Node 40 (Parsuram Kund)</title></g>
<g class="node-group">
                                        <circle cx="1400" cy="900" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1505, 900)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="160" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-34">Jagannath
                                                Temple</text>
                                        </g>
                                        <title>Node 36 (Jagannath Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1250" cy="950" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1245, 990)" style="cursor: move;">
                                            <rect x="-40" y="-14" width="80" height="48" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-19"><tspan x="0">Chilika</tspan><tspan x="0" dy="1.2em">Lake</tspan></text>
                                        </g>
                                        <title>Node 37 (Chilika Lake)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1150" cy="1050" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1060, 1050)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-36">Tarapith</text>
                                        </g>
                                        <title>Node 38 (Tarapith)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1300" cy="1100" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>
                                        <g class="node-label-container" transform="translate(1420, 1110)" style="cursor: move;">
                                            <rect x="-95" y="-14" width="190" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-37">Betla
                                                National Park</text>
                                        </g>
                                        <title>Node 39 (Betla National Park)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1280" cy="200" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(1175, 195)" style="cursor: move;">
                                            <rect x="-75" y="-14" width="150" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-38">Kamakhya
                                                Temple</text>
                                        </g>
                                        <title>Node 42 (Kamakhya Temple)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1480" cy="300" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(1595, 295)" style="cursor: move;">
                                            <rect x="-90" y="-14" width="180" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-39">Living
                                                Root Bridge</text>
                                        </g>
                                        <title>Node 43 (Living Root Bridge)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1350" cy="400" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(1455, 400)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="160" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-40">Ujjayanta
                                                Palace</text>
                                        </g>
                                        <title>Node 44 (Ujjayanta Palace)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1210" cy="500" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(1300, 495)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-41">Kangla
                                                Fort</text>
                                        </g>
                                        <title>Node 45 (Kangla Fort)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="1000" cy="400" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(960, 440)" style="cursor: move;">
                                            <rect x="-95" y="-14" width="190" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-42">Phawngpui
                                                Blue Mountain</text>
                                        </g>
                                        <title>Node 46 (Phawngpui Blue Mountain)</title>
                                    </g>
                                    <g class="node-group">
                                        <circle cx="900" cy="300" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(995, 295)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-43">Dzükou
                                                Valley</text>
                                        </g>
                                        <title>Node 47 (Dzükou Valley)</title>
                                    </g>
                                    <g class="node-group"><circle cx="1160" cy="120" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></circle><g class="node-label-container" transform="translate(1160, 75)"><rect x="-85" y="-14" width="170" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label">Gonjang Monastery</text></g><title>Node 41 (Gonjang Monastery)</title></g>
<g class="node-group">

                                        <circle cx="1320" cy="80" r="22.5" fill="#ffffff" stroke="#333" stroke-width="1.5" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;">
                                        </circle>

                                        <g class="node-label-container" transform="translate(1425, 80)" style="cursor: move;">
                                            <rect x="-80" y="-14" width="160" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-44">Rumtek
                                                Monastery</text>
                                        </g>
                                        <title>Node 48 (Rumtek Monastery)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="65" y="575" width="75" height="75" class="node-region-west node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(0, 600)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-45">Somnath</text>
                                        </g>
                                        <title>Node 49 (Somnath)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="365" y="610" width="75" height="75" class="node-region-west node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(480, 635)" style="cursor: move;">
                                            <rect x="-50" y="-14" width="100" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-46">Nageswar</text>
                                        </g>
                                        <title>Node 50 (Nageswar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="445" y="1125" width="75" height="75" class="node-region-south node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(510, 1110)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-47">Mallikarjuna</text>
                                        </g>
                                        <title>Node 51 (Mallikarjuna)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="605" y="555" width="75" height="75" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(645, 535)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-2">Mahakaleswar</text>
                                        </g>
                                        <title class="ai-style-change-1">Node 52 (Mahakaleswar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="925" y="525" width="75" height="75" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(1010, 510)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-49">Omkareshwar</text>
                                        </g>
                                        <title>Node 53 (Omkareshwar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="225" y="95" width="75" height="75" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(250, 75)" style="cursor: move;">

                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-50">Kedarnath</text>
                                        </g>
                                        <title>Node 54 (Kedarnath)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="1185" y="575" width="75" height="75" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(1240, 560)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="130" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-51">Bhimashankar</text>
                                        </g>
                                        <title>Node 55 (Bhimashankar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="625" y="115" width="75" height="75" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(650, 95)" style="cursor: move;">

                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-52">Viswanath</text>
                                        </g>
                                        <title>Node 56 (Viswanath)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="975" y="135" width="75" height="75" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(910, 140)" style="cursor: move;">

                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-53">Triambakeshwar</text>
                                        </g>
                                        <title>Node 57 (Triambakeshwar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="1505" y="785" width="75" height="75" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(1625, 810)" style="cursor: move;">
                                            <rect x="-50" y="-14" width="100" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-54">Vaidyanath</text>
                                        </g>
                                        <title>Node 58 (Vaidyanath)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="1075" y="1330" width="75" height="75" class="node-region-south node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(1200, 1350)" style="cursor: move;">
                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-55">Rameshwar</text>
                                        </g>
                                        <title>Node 59 (Rameshwar)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="/assets/images/jyotirlinga.png" x="1395" y="125" width="75" height="75" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <g class="node-label-container" transform="translate(1520, 135)" style="cursor: move;">

                                            <rect x="-70" y="-14" width="140" height="28" rx="6" ry="6" class="location-label-background" fill="rgba(255, 255, 255, 0.95)" stroke="#333" stroke-width="1"></rect><text x="0" y="0" text-anchor="middle" dominant-baseline="middle" font-size="13" font-weight="bold" fill="#333" class="enhanced-location-label ai-style-change-56">Grishneshwar</text>
                                        </g>
                                        <title>Node 60 (Grishneshwar)</title>
                                    </g>
                                    <g class="node-group">

    <image href="https://i.imgur.com/PQRpnS5.png" x="395" y="325" width="75" height="75" class="node-region-north node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <title>Node 61 (North Airport)</title>

                                    </g>
                                    <g class="node-group">

    <image href="https://i.imgur.com/cEFqDhV.png" x="200" y="740" width="75" height="75" class="node-region-west node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <title>Node 62 (West Airport)</title>
                                    </g>
                                    <g class="node-group">

    <image href="https://i.imgur.com/nWm75yD.png" x="715" y="1230" width="70" height="70" class="node-region-south node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <title>Node 63 (South Airport)</title>
                                    </g>
                                    <g class="node-group">

    <image href="https://i.imgur.com/oifmd4F.png" x="755" y="675" width="75" height="75" class="node-region-central node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <title>Node 64 (Central Airport)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="https://i.imgur.com/3YARswm.png" x="1275" y="795" width="75" height="75" class="node-region-east node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
                                        <title>Node 65 (East Airport)</title>
                                    </g>
                                    <g class="node-group">
                                    <image href="https://i.imgur.com/t2WELw7.png" x="1175" y="275" width="80" height="80" class="node-region-northeast node " style="cursor: default; transition: 0.3s; pointer-events: none; filter: none;"></image>
    

                                        <title>Node 66 (Northeast Airport)</title>
                                    </g>
                                </svg></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="player-section flex" style="flex-wrap: wrap; justify-content: center; gap: 1rem; margin-top: 1rem;">
                <div class="player-mat active " style="width: 250px; position: relative;">
                    <h3 style="border-bottom: 2px solid var(--accent-color); padding-bottom: 0.5rem; margin-bottom: 0.75rem;">
                        1</h3>
                    <div class="character-card" style="margin: 1rem 0px;">
                        <div class="character-card-image" style="border-radius: 8px; width: 100%; height: 180px; overflow: hidden; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;">
                            <img src="/assets/images/characters/pilgrim.jpg" alt="Pilgrim" class="character-debug-img" style="width: 100%; height: 100%; object-fit: contain; border-radius: inherit;"></div>
                        <div class="character-card-title" style="text-align: center; font-weight: bold; margin-top: 0.5rem; font-size: 1.1rem;">
                            Pilgrim</div>
                        <div style="text-align: center; font-size: 0.8rem; margin-top: 0.25rem; color: rgb(102, 102, 102);">
                            Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube</div>
                    </div>
                    <div class="flex" style="justify-content: space-between;">
                        <div>
                            <p><strong>Position:</strong> 63</p>
                            <p><strong>OM (temp):</strong> 0</p>
                        </div>
                        <div>
                            <p><strong>Score:</strong></p>
                            <p>Outer: 0</p>
                            <p>Inner: 0</p>
                        </div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <p><strong>OM Slots:</strong></p>
                        <div class="flex" style="justify-content: space-between;">
                            <div>
                                <p>Outer: 0/7</p>
                                <div class="flex gap-sm">
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p>Inner: 0/7</p>
                                <div class="flex gap-sm">
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <div class="flex" style="justify-content: space-between; align-items: center;">
                            <p><strong>Energy Cubes:</strong></p>
                        </div>
                        <div class="flex gap-sm" style="flex-wrap: wrap;"></div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <div class="flex" style="justify-content: space-between; align-items: center;">
                            <p><strong>Hand (2/4):</strong></p><button class="travel-btn accent" style="background-color: var(--primary-color); color: white; padding: 5px 10px; border-radius: 4px; border: none; cursor: pointer; font-weight: bold; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;">Travel</button>
                        </div>
                        <div>
                            <div class="card-item" style="padding: 0.75rem; margin: 0.5rem 0px; background: rgb(255, 248, 225); border-radius: 8px; border: 1px solid rgb(25, 118, 210); box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; display: flex; justify-content: center; align-items: center; height: 150px;">
                                <img src="/assets/images/vehicles/1/camel.png" alt="camel (1)" style="max-width: 100%; max-height: 100%; object-fit: contain;"></div>
                            <div class="card-item" style="padding: 0.75rem; margin: 0.5rem 0px; background: rgb(255, 248, 225); border-radius: 8px; border: 1px solid rgb(25, 118, 210); box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; display: flex; justify-content: center; align-items: center; height: 150px;">
                                <img src="/assets/images/vehicles/3/boat.png" alt="boat (3)" style="max-width: 100%; max-height: 100%; object-fit: contain;"></div>
                        </div>
                    </div>
                </div>
                <div class="player-mat  " style="width: 250px; position: relative;">
                    <h3 style="border-bottom: 2px solid var(--accent-color); padding-bottom: 0.5rem; margin-bottom: 0.75rem;">
                        2</h3>
                    <div class="character-card" style="margin: 1rem 0px;">
                        <div class="character-card-image" style="border-radius: 8px; width: 100%; height: 180px; overflow: hidden; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 4px;">
                            <img src="/assets/images/characters/engineer.jpg" alt="Engineer" class="character-debug-img" style="width: 100%; height: 100%; object-fit: contain; border-radius: inherit;"></div>
                        <div class="character-card-title" style="text-align: center; font-weight: bold; margin-top: 0.5rem; font-size: 1.1rem;">
                            Engineer</div>
                        <div style="text-align: center; font-size: 0.8rem; margin-top: 0.25rem; color: rgb(102, 102, 102);">
                            Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube</div>
                    </div>
                    <div class="flex" style="justify-content: space-between;">
                        <div>
                            <p><strong>Position:</strong> 61</p>
                            <p><strong>OM (temp):</strong> 0</p>
                        </div>
                        <div>
                            <p><strong>Score:</strong></p>
                            <p>Outer: 0</p>
                            <p>Inner: 0</p>
                        </div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <p><strong>OM Slots:</strong></p>
                        <div class="flex" style="justify-content: space-between;">
                            <div>
                                <p>Outer: 0/7</p>
                                <div class="flex gap-sm">
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p>Inner: 0/7</p>
                                <div class="flex gap-sm">
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                    <div style="width: 20px; height: 20px; border: 1px solid rgb(204, 204, 204); background: transparent; border-radius: 4px; text-align: center; line-height: 20px; font-size: 0.7rem;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <div class="flex" style="justify-content: space-between; align-items: center;">
                            <p><strong>Energy Cubes:</strong></p>
                        </div>
                        <div class="flex gap-sm" style="flex-wrap: wrap;"></div>
                    </div>
                    <div style="margin: 0.75rem 0px;">
                        <div class="flex" style="justify-content: space-between; align-items: center;">
                            <p><strong>Hand (2/4):</strong></p>
                        </div>
                        <div>
                            <div class="card-item" style="padding: 0.75rem; margin: 0.5rem 0px; background: rgb(255, 248, 225); border-radius: 8px; border: 1px solid rgb(25, 118, 210); box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; display: flex; justify-content: center; align-items: center; height: 150px;">
                                <img src="/assets/images/vehicles/2/motorbike.png" alt="motorbike (2)" style="max-width: 100%; max-height: 100%; object-fit: contain;"></div>
                            <div class="card-item" style="padding: 0.75rem; margin: 0.5rem 0px; background: rgb(255, 248, 225); border-radius: 8px; border: 1px solid rgb(25, 118, 210); box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px; display: flex; justify-content: center; align-items: center; height: 150px;">
                                <img src="/assets/images/vehicles/1/horse.png" alt="horse (1)" style="max-width: 100%; max-height: 100%; object-fit: contain;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="face-up-cards-section" style="margin-top: 1rem;">
                <div class="face-up-cards">
                    <div class="card-container global-event-card">
                        <div class="card-title" style="color: rgb(216, 67, 21); border-bottom: 2px solid rgb(255, 183, 77);">Global Event
                        </div>
                        <div class="card-item" style="background: linear-gradient(135deg, rgb(255, 248, 225) 0%, rgb(255, 224, 178) 100%); border: 2px solid rgb(255, 183, 77); box-shadow: rgba(255, 167, 38, 0.2) 0px 4px 8px; padding: 16px; border-radius: 8px;">
                            <h3 style="color: rgb(216, 67, 21); margin-top: 0px; margin-bottom: 12px; font-size: 1.2rem; text-align: center;">
                                Scenic Cruise</h3>
                            <p style="font-size: 1rem; margin-bottom: 0px; text-align: center; font-style: italic; color: rgb(93, 64, 55);">
                                Use the Boat travel card for 5 outer points</p>
                        </div>
                    </div>
                    <div class="card-container travel-cards">
                        <div class="card-title">Travel Cards<div class="card-actions"><button class="action-button disabled" disabled="">Pick Selected (0)</button><button class="action-button">Pick from Top</button></div>
                        </div>
                        <div class="card-grid">
                            <div class="card-item travel-card  " style="cursor: default; opacity: 0.7; border: 1px solid rgb(221, 221, 221); background: rgb(255, 248, 225); border-radius: 8px; width: 120px; height: 180px; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px; position: relative; margin: 10px;">
                                <div style="position: absolute; top: 8px; left: 8px; font-weight: bold; font-size: 1.5rem;">
                                    1</div>
                                <div style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center;">
                                    <img src="/assets/images/vehicles/1/cycle.png" alt="cycle (1)" style="max-width: 100%; max-height: 100%; object-fit: contain; margin-bottom: 10px;">
                                </div>
                                <div style="font-size: 0.9rem; margin-top: auto; text-align: center; text-transform: capitalize;">
                                    cycle (1)</div>
                            </div>
                            <div class="card-item travel-card  " style="cursor: default; opacity: 0.7; border: 1px solid rgb(221, 221, 221); background: rgb(255, 248, 225); border-radius: 8px; width: 120px; height: 180px; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px; position: relative; margin: 10px;">
                                <div style="position: absolute; top: 8px; left: 8px; font-weight: bold; font-size: 1.5rem;">
                                    2</div>
                                <div style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center;">
                                    <img src="/assets/images/vehicles/2/car.png" alt="car (2)" style="max-width: 100%; max-height: 100%; object-fit: contain; margin-bottom: 10px;">
                                </div>
                                <div style="font-size: 0.9rem; margin-top: auto; text-align: center; text-transform: capitalize;">
                                    car (2)</div>
                            </div>
                            <div class="card-item travel-card  " style="cursor: default; opacity: 0.7; border: 1px solid rgb(221, 221, 221); background: rgb(255, 248, 225); border-radius: 8px; width: 120px; height: 180px; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px; position: relative; margin: 10px;">
                                <div style="position: absolute; top: 8px; left: 8px; font-weight: bold; font-size: 1.5rem;">
                                    3</div>
                                <div style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center;">
                                    <img src="/assets/images/vehicles/3/train.png" alt="train (3)" style="max-width: 100%; max-height: 100%; object-fit: contain; margin-bottom: 10px;">
                                </div>
                                <div style="font-size: 0.9rem; margin-top: auto; text-align: center; text-transform: capitalize;">
                                    train (3)</div>
                            </div>
                            <div class="card-item travel-card  " style="cursor: default; opacity: 0.7; border: 1px solid rgb(221, 221, 221); background: rgb(255, 248, 225); border-radius: 8px; width: 120px; height: 180px; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px; position: relative; margin: 10px;">
                                <div style="position: absolute; top: 8px; left: 8px; font-weight: bold; font-size: 1.5rem;">
                                    3</div>
                                <div style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center;">
                                    <img src="/assets/images/vehicles/3/helicopter.png" alt="helicopter (3)" style="max-width: 100%; max-height: 100%; object-fit: contain; margin-bottom: 10px;">
                                </div>
                                <div style="font-size: 0.9rem; margin-top: auto; text-align: center; text-transform: capitalize;">
                                    helicopter (3)</div>
                            </div>
                        </div>
                    </div>
                    <div class="journey-cards-section" style="display: flex; flex-direction: row; justify-content: space-between; gap: 16px;">
                        <div class="card-container journey-cards outer" style="flex: 1 1 0%;">
                            <div class="card-title">Outer Journey Cards</div>
                            <div class="card-grid">
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--accent-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--accent-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Kangla Fort</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#45</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--northeast-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/45.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--accent-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            20</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Outer Journey: Outer</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--karma-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--accent-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--accent-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Living Root Bridge</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#43</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--northeast-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/43.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--accent-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            27</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Outer Journey: Outer</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--karma-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--artha-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--artha-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--accent-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--accent-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Jaisalmer Fort</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#12</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--west-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/12.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--accent-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            30</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Outer Journey: Outer</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--karma-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--karma-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--artha-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--artha-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--accent-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--accent-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Kurukshetra</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#5</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--central-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/5.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--accent-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            24</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Outer Journey: Outer</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--karma-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--artha-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-container journey-cards inner" style="flex: 1 1 0%;">
                            <div class="card-title">Inner Journey Cards</div>
                            <div class="card-grid">
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--primary-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--primary-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Basilica of Bom Jesus</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#19</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--west-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/19.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--primary-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            27</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Inner Journey: Inner</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--primary-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--primary-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Rumtek Monastery</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#48</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--northeast-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/48.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--primary-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            30</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Inner Journey: Inner</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--primary-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--primary-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Sabarimala</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#23</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--south-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/23.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--primary-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            27</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Inner Journey: Inner</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-item journey-card" style="cursor: default; opacity: 0.7; width: 200px; height: 280px; border-radius: 8px; overflow: hidden; position: relative; box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 8px; background: rgb(255, 255, 255); border: 2px solid var(--primary-color); transition: transform 0.2s, box-shadow 0.2s;">
                                    <div style="background: var(--primary-color); color: white; padding: 8px 12px; border-top-left-radius: 6px; border-top-right-radius: 6px; display: flex; justify-content: space-between; align-items: center;">
                                        <div style="font-weight: bold; font-size: 1rem;">Golden Temple</div>
                                        <div style="display: flex; align-items: center; gap: 4px; font-size: 0.8rem;">
                                            <span>#3</span>
                                            <div style="width: 12px; height: 12px; border-radius: 50%; background: var(--north-color); border: 1px solid rgb(255, 255, 255);">
                                            </div>
                                        </div>
                                    </div>
                                    <div style="height: 160px; background: url(&quot;/assets/images/cards/3.png&quot;) center center / cover rgb(238, 238, 238); position: relative;">
                                        <div style="position: absolute; bottom: 8px; right: 8px; background: var(--primary-color); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; font-size: 1.2rem; font-weight: bold; border: 2px solid white;">
                                            30</div>
                                    </div>
                                    <div style="padding: 12px; border-top: 1px solid rgb(238, 238, 238);">
                                        <div style="font-size: 0.85rem; color: rgb(102, 102, 102); margin-bottom: 8px; font-style: italic;">
                                            Inner Journey: Inner</div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div style="display: flex; gap: 4px;">
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--bhakti-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                                <div style="width: 24px; height: 24px; border-radius: 4px; background: var(--gnana-color); border: 1px solid rgb(102, 102, 102); box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px;">
                                                </div>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 4px;">
                                                <div style="background: rgb(255, 215, 0); color: rgb(51, 51, 51); width: 24px; height: 24px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 0.9rem; font-weight: bold; border: 1px solid rgb(51, 51, 51);">
                                                    1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex gap-md mt-md" style="justify-content: center;"><button class="secondary">Save
                    Game</button><button class="secondary" title="Enable sound">🔇 Sound Off</button></div>
        </div>
    </div>

    <span id="okta-plugin-message-channel-available" style="display: none;"></span>
<grammarly-desktop-integration data-grammarly-shadow-root="true"></grammarly-desktop-integration>

</body></html>