import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import io from 'socket.io-client';
import PlayerLobby from './components/PlayerLobby';
import GameBoard from './components/GameBoard';
import './styles/main.css';

const socket = io('http://192.168.2.86:4000'); // Connect to local server

function App() {
  const [gameState, setGameState] = useState(null);
  const [winner, setWinner] = useState(null);
  const [isConnecting, setIsConnecting] = useState(true);

  useEffect(() => {
    socket.on('connect', () => {
      console.log('Connected to server with socket ID:', socket.id);
      setIsConnecting(false);
    });

    socket.on('gameState', (state) => {
      console.log('Received updated game state:', {
        started: state.started,
        faceUpTravel: state.faceUpTravel?.map(c => c.id),
        turnIndex: state.turnIndex,
        currentPlayerName: state.players?.[state.turnIndex]?.name
      });
      setGameState(state);
    });

    socket.on('joinError', (msg) => alert(msg));
    socket.on('startError', (msg) => alert(msg));

    socket.on('gameOver', ({ winner }) => {
      console.log('Game Over! Winner:', winner);
      console.log('Win condition:', winner.winByOm ? 'OM Tokens' : (winner.winByScore ? 'Score' : 'Unknown'));
      console.log('OM Total:', winner.omTotal, 'Score:', winner.totalScore);
      setWinner(winner);
    });

    return () => {
      socket.off('connect');
      socket.off('gameState');
      socket.off('joinError');
      socket.off('startError');
      socket.off('gameOver');
    };
  }, []);

  // Navigation header component
  const AppNavigation = () => (
    <div className="app-navigation">
      <Link to="/" className="active">Game</Link>
      <Link to="/simulation">Simulation Dashboard</Link>
    </div>
  );

  if (isConnecting) {
    return (
      <>
        <AppNavigation />
        <div className="loading">Connecting to server</div>
      </>
    );
  }

  if (!gameState) {
    return (
      <>
        <AppNavigation />
        <div className="loading">Loading game state</div>
      </>
    );
  }

  console.log('Game started state:', gameState.started);

  if (!gameState.started) {
    return (
      <>
        <AppNavigation />
        <PlayerLobby socket={socket} gameState={gameState} />
      </>
    );
  }

  return (
    <>
      <AppNavigation />
      <GameBoard socket={socket} gameState={gameState} winner={winner} />
    </>
  );
}

export default App;