import React from 'react';

function JourneyCardModal({ card, onClose, onCollect, isAvailable, currentPlayer, gameState }) {
  if (!card) return null;

  // Determine if the current player can collect this card
  // We now accept isAvailable as a function that provides a reliable check
  const canCollect = () => {
    // Use the isAvailable function if provided (more reliable)
    if (typeof isAvailable === 'function') {
      return isAvailable() && currentPlayer?.position === card.locationId;
    }
    
    // Otherwise use old behavior for backward compatibility
    return currentPlayer && currentPlayer.position === card.locationId;
  };

  // Determine card type (inner or outer journey)
  const isInnerJourney = card.reward && card.reward.inner !== undefined;
  const journeyType = isInnerJourney ? 'Inner' : 'Outer';
  const journeyValue = isInnerJourney ? card.reward.inner : card.reward.outer;
  const accentColor = isInnerJourney ? 'var(--primary-color)' : 'var(--accent-color)';

  // Find the location info based on locationId
  const location = gameState?.locations?.find(loc => loc.id === card.locationId);
  const locationName = location ? location.name : 'Unknown Location';
  const region = location ? location.region : 'unknown';
  const journeySubType = location ? location.journeyType : '';

  // Get energy requirements for display
  const energyRequirements = [];
  if (card.required) {
    Object.entries(card.required).forEach(([type, count]) => {
      for (let i = 0; i < count; i++) {
        energyRequirements.push(type.toLowerCase());
      }
    });
  }
  
  // Calculate wild cubes needed
  const calculateWildCubesNeeded = () => {
    const currentPlayer = gameState.players.find(p => p.id === localStorage.getItem('playerId'));
    if (!currentPlayer || !card.required) return 0;
    
    const energyCubes = currentPlayer.energyCubes || [];
    
    // Count available energy cubes by type
    const available = {
      artha: energyCubes.filter(cube => cube === 'artha').length,
      karma: energyCubes.filter(cube => cube === 'karma').length,
      gnana: energyCubes.filter(cube => cube === 'gnana').length,
      bhakti: energyCubes.filter(cube => cube === 'bhakti').length
    };
    
    // Calculate total shortfall across all energy types
    let totalWildNeeded = 0;
    for (const [color, req] of Object.entries(card.required)) {
      const shortfall = Math.max(0, req - available[color]);
      totalWildNeeded += shortfall;
    }
    
    return totalWildNeeded;
  };
  
  const wildCubesNeeded = calculateWildCubesNeeded();
  const wildCubesAvailable = currentPlayer?.hand ? currentPlayer.hand.filter(card => card.type === 'wildCube').length : 0;

  // Count how many journey cards of this type the player already has
  const countExistingJourneys = () => {
    if (!currentPlayer || !currentPlayer.collectedJourneys) return 0;
    
    return currentPlayer.collectedJourneys.filter(j => {
      if (isInnerJourney) {
        return j.reward && j.reward.inner !== undefined;
      } else {
        return j.reward && j.reward.outer !== undefined;
      }
    }).length;
  };
  
  // Calculate the OM token cost based on player count and existing journeys
  const calculateOmCost = () => {
    const journeyCount = countExistingJourneys();
    const playerCount = gameState?.players?.length || 3;
    
    // OM cost is 0-1-1-2 for 4 players, 1-1-2-3 for 2-3 players
    if (playerCount === 4) {
      return [0, 1, 1, 2][journeyCount < 4 ? journeyCount : 3];
    } else {
      return [1, 1, 2, 3][journeyCount < 4 ? journeyCount : 3];
    }
  };
  
  const omCost = calculateOmCost();
  const omAvailable = currentPlayer?.omTemp ? currentPlayer.omTemp.length : 0;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1500,
      backdropFilter: 'blur(3px)'
    }}>
      <div style={{
        width: '90%',
        maxWidth: '550px',
        background: 'white',
        borderRadius: '12px',
        overflow: 'hidden',
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
        display: 'flex',
        flexDirection: 'column',
        maxHeight: '90vh'
      }}>
        {/* Header with location name and close button */}
        <div style={{
          background: accentColor,
          color: 'white',
          padding: '16px 20px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold' }}>
              {locationName}
            </h2>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '8px',
              fontSize: '0.9rem',
              marginTop: '4px'
            }}>
              <span>#{card.locationId}</span>
              <div style={{ 
                width: '12px', 
                height: '12px', 
                borderRadius: '50%', 
                background: `var(--${region.toLowerCase()}-color)`,
                border: '1px solid white'
              }} />
              <span>{region}</span>
            </div>
          </div>
          <button 
            onClick={onClose}
            style={{
              background: 'rgba(255, 255, 255, 0.3)',
              border: 'none',
              borderRadius: '50%',
              width: '36px',
              height: '36px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.2rem',
              cursor: 'pointer',
              color: 'white',
              boxShadow: 'none'
            }}
          >
            ✕
          </button>
        </div>
        
        {/* Main content with image and details */}
        <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'auto' }}>
          {/* Location image */}
          <div style={{
            height: '240px',
            backgroundImage: `url(${process.env.PUBLIC_URL}/assets/images/cards/${card.locationId}.png)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            position: 'relative'
          }}>
            {/* Journey type badge */}
            <div style={{
              position: 'absolute',
              bottom: '16px',
              right: '16px',
              background: accentColor,
              color: 'white',
              borderRadius: '50%',
              width: '48px',
              height: '48px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              fontSize: '1.4rem',
              fontWeight: 'bold',
              border: '3px solid white',
              boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
            }}>
              {journeyValue}
            </div>
          </div>
          
          {/* Card details */}
          <div style={{ padding: '24px' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
              <div style={{
                fontSize: '1rem',
                fontWeight: 'bold',
                color: accentColor,
                marginRight: '10px',
                textTransform: 'uppercase'
              }}>
                {journeyType} Journey
              </div>
              <div style={{
                fontSize: '0.9rem',
                color: '#666',
                fontStyle: 'italic'
              }}>
                {journeySubType}
              </div>
            </div>
            
            {/* Requirements section */}
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ 
                fontSize: '1rem', 
                marginBottom: '10px',
                color: '#333',
                borderBottom: '1px solid #eee',
                paddingBottom: '8px'
              }}>
                Required Resources
              </h3>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {/* Energy Cubes */}
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <span style={{ minWidth: '100px', fontWeight: 'bold' }}>Energy Cubes:</span>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {energyRequirements.map((cubeType, i) => (
                      <div 
                        key={i}
                        style={{
                          width: '36px',
                          height: '36px',
                          borderRadius: '6px',
                          background: `var(--${cubeType}-color)`,
                          border: '1px solid #666',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                        }}
                      />
                    ))}
                    {energyRequirements.length === 0 && (
                      <div style={{ color: '#666', fontStyle: 'italic' }}>None</div>
                    )}
                  </div>
                </div>
                
                {/* OM Tokens */}
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <span style={{ minWidth: '100px', fontWeight: 'bold' }}>OM Tokens:</span>
                  <div style={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <div style={{
                      background: omAvailable >= omCost ? 'gold' : '#f0c040',
                      color: '#333',
                      width: '36px',
                      height: '36px',
                      borderRadius: '50%',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      border: '1px solid #333',
                      opacity: omAvailable >= omCost ? 1 : 0.7
                    }}>
                      {omCost}
                    </div>
                    <span style={{ 
                      color: omAvailable >= omCost ? 'green' : 'red',
                      fontWeight: 'bold'
                    }}>
                      {omAvailable}/{omCost} available
                    </span>
                  </div>
                </div>
                
                {/* Wild cube information */}
                {wildCubesNeeded > 0 && currentPlayer && (
                  <div style={{ 
                    padding: '8px 12px',
                    background: wildCubesNeeded <= wildCubesAvailable ? 'rgba(0, 180, 0, 0.1)' : 'rgba(220, 0, 0, 0.1)',
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <span style={{ 
                      fontWeight: 'bold', 
                      color: wildCubesNeeded <= wildCubesAvailable ? 'green' : 'red'
                    }}>
                      Will use {wildCubesNeeded} wild cube{wildCubesNeeded > 1 ? 's' : ''}
                    </span>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      ({wildCubesAvailable} available)
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Reward section */}
            <div>
              <h3 style={{ 
                fontSize: '1rem', 
                marginBottom: '10px',
                color: '#333',
                borderBottom: '1px solid #eee',
                paddingBottom: '8px'
              }}>
                Reward
              </h3>
              
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                gap: '12px'
              }}>
                <div style={{ 
                  fontSize: '1.1rem', 
                  fontWeight: 'bold',
                  color: accentColor,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span style={{
                    background: accentColor,
                    color: 'white',
                    width: '36px',
                    height: '36px',
                    borderRadius: '50%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    +{journeyValue}
                  </span>
                  <span>{journeyType} OM Points</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Footer with action buttons */}
        <div style={{ 
          padding: '16px',
          borderTop: '1px solid #eee',
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px'
        }}>
          <button 
            className="secondary" 
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            onClick={() => onCollect(card)}
            style={{ 
              background: accentColor,
              opacity: canCollect() ? 1 : 0.5,
              cursor: canCollect() ? 'pointer' : 'not-allowed'
            }}
            disabled={!canCollect()}
          >
            Collect Card
          </button>
        </div>
      </div>
    </div>
  );
}

export default JourneyCardModal;