import React, { useState, useEffect } from 'react';
import JourneyCardModal from './JourneyCardModal';

function FaceUpCards({ socket, gameState }) {
  const {
    faceUpTravel,
    faceUpEvent,
    faceUpJourneyOuter,
    faceUpJourneyInner,
    players,
    turnIndex,
    currentGlobalEvent,
    energyCubePile,
    locations
  } = gameState;
  const currentPlayer = players[turnIndex];

  const [selectedTravel, setSelectedTravel] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [journeyModal, setJourneyModal] = useState(null);

  // Clear selections whenever the game state changes (e.g., after picking cards)
  useEffect(() => {
    setSelectedTravel([]);
    setSelectedEvent(null);
    console.log('FaceUpCards received updated gameState, current faceUpTravel:',
      faceUpTravel ? faceUpTravel.map(c => c.id) : 'none');
  }, [gameState]);

  const isMyTurn = (p) => p.id === currentPlayer.id;

  // Check if the player meets the energy requirement using energy cubes and wildCube event cards in hand.
  const meetsEnergyRequirement = (required, player) => {
    // Count available wildCube event cards in player's hand.
    const wildCountInitial = player.hand ? player.hand.filter(card => card.type === 'wildCube').length : 0;
    const energyCubes = player.energyCubes || []; // array of cube colors

    // Count available energy cubes by type
    const available = {
      artha: energyCubes.filter(cube => cube === 'artha').length,
      karma: energyCubes.filter(cube => cube === 'karma').length,
      gnana: energyCubes.filter(cube => cube === 'gnana').length,
      bhakti: energyCubes.filter(cube => cube === 'bhakti').length
    };

    // Calculate total shortfall across all energy types
    let totalShortfall = 0;
    for (const [color, req] of Object.entries(required)) {
      const shortfall = Math.max(0, req - available[color]);
      totalShortfall += shortfall;
    }

    // Check if we have enough wild cubes to cover the shortfall
    return totalShortfall <= wildCountInitial;
  };

  // Helper to get the number of journey cards already collected for this reward type.
  const getJourneyCount = (player, card) => {
    if (card.reward.outer !== undefined) {
      return player.collectedJourneys.filter(j => j.reward && j.reward.outer !== undefined).length;
    } else {
      return player.collectedJourneys.filter(j => j.reward && j.reward.inner !== undefined).length;
    }
  };

  // Check if the player has enough Om tokens in omTemp to pay for the next journey card.
  const meetsOmRequirement = (card, player) => {
    const costArray = [1, 1, 2, 3];
    const count = getJourneyCount(player, card);
    if (count >= costArray.length) return false;
    const requiredOm = costArray[count];
    return player.omTemp.length >= requiredOm;
  };

  // Combined check: on location, meets energy req (with wildCube cards), and meets om token req.
  const canCollectJourneyCard = (card, player) => {
    const onLocation = player.position === card.locationId;

    // Check if inner journey cards are blocked by global event (Drought of Spirits)
    if (currentGlobalEvent?.effect === 'no_inner_journey_cards' && card.reward.inner !== undefined) {
      return false;
    }

    // Check if outer journey cards are blocked by global event (Heritage Site Renovations)
    if (currentGlobalEvent?.effect === 'no_outer_journey_cards' && card.reward.outer !== undefined) {
      return false;
    }

    return onLocation && meetsEnergyRequirement(card.required, player) && meetsOmRequirement(card, player);
  };

  const handlePickTravel = () => {
    if (!selectedTravel.length) return;
    console.log('Picking travel cards with IDs:', selectedTravel);
    console.log('Current faceUpTravel before request:', faceUpTravel.map(c => c.id));
    socket.emit('pickCards', {
      type: 'travel',
      pickFromFaceUp: selectedTravel,
    });
    setSelectedTravel([]);
  };

  const handlePickTravelFromTop = () => {
    console.log('Picking travel card from top of deck');
    socket.emit('pickCards', {
      type: 'travel',
      pickFromTop: true,
    });
  };

  const handlePickEvent = () => {
    if (!selectedEvent) return;
    console.log('Picking event card with ID:', selectedEvent);
    console.log('Current faceUpEvent before request:', faceUpEvent.map(c => c.id));
    socket.emit('pickCards', {
      type: 'event',
      pickFromFaceUp: [selectedEvent],
    });
    setSelectedEvent(null);
  };

  const handlePickEventFromTop = () => {
    console.log('Picking event card from top of deck');
    socket.emit('pickCards', {
      type: 'event',
      pickFromTop: true,
    });
  };

  const handleCollectJourney = (card) => {
    if (!canCollectJourneyCard(card, currentPlayer)) {
      alert("You do not meet the requirements to collect this journey card.");
      return;
    }

    const costArray = [1, 1, 2, 3];
    const journeyCount = getJourneyCount(currentPlayer, card);
    const requiredOm = costArray[journeyCount];
    const journeyType = card.reward.outer !== undefined ? 'outer' : 'inner';

    // Close the modal
    setJourneyModal(null);

    // Send the collect event to the server
    socket.emit('collectJourney', {
      journeyCardId: card.id,
      journeyType,
      requiredOm,
    });
  };

  // Calculate the minimum number of wild cubes needed for a given requirement
  const calcWildCubesNeeded = (required, player) => {
    const energyCubes = player.energyCubes || [];

    // Count available energy cubes by type
    const available = {
      artha: energyCubes.filter(cube => cube === 'artha').length,
      karma: energyCubes.filter(cube => cube === 'karma').length,
      gnana: energyCubes.filter(cube => cube === 'gnana').length,
      bhakti: energyCubes.filter(cube => cube === 'bhakti').length
    };

    // Calculate total shortfall across all energy types
    let totalWildNeeded = 0;
    for (const [color, req] of Object.entries(required)) {
      const shortfall = Math.max(0, req - available[color]);
      totalWildNeeded += shortfall;
    }

    return totalWildNeeded;
  };

  // Helper to render energy requirement with wild cube calculation
  const renderEnergyRequirement = (required) => {
    if (!required) return null;

    // Calculate wild cubes needed if this is for the current player
    const wildCubesNeeded = currentPlayer ? calcWildCubesNeeded(required, currentPlayer) : 0;
    const wildCubesAvailable = currentPlayer?.hand ? currentPlayer.hand.filter(card => card.type === 'wildCube').length : 0;

    return (
      <div>
        <div className="flex gap-sm">
          {Object.entries(required).map(([color, count]) => (
            <div key={color} style={{ display: 'flex', alignItems: 'center' }}>
              <div
                className={`energy-cube ${color.toLowerCase()}`}
                style={{ marginRight: '2px' }}
              />
              <span>x{count}</span>
            </div>
          ))}
        </div>
        {wildCubesNeeded > 0 && (
          <div style={{ marginTop: '4px', fontSize: '0.8rem', color: wildCubesNeeded <= wildCubesAvailable ? 'green' : 'red' }}>
            <span>{`Will use ${wildCubesNeeded} wild cube${wildCubesNeeded > 1 ? 's' : ''}`}</span>
          </div>
        )}
      </div>
    );
  };

  // Render global event card
  const renderGlobalEventCard = () => {
    if (!currentGlobalEvent) return null;

    return (
      <div className="card-container global-event-card">
        <div className="card-title" style={{ color: '#d84315', borderBottom: '2px solid #ffb74d' }}>
          Global Event
        </div>
        <div className="card-item" style={{
          background: 'linear-gradient(135deg, #fff8e1 0%, #ffe0b2 100%)',
          border: '2px solid #ffb74d',
          boxShadow: '0 4px 8px rgba(255, 167, 38, 0.2)',
          padding: '16px',
          borderRadius: '8px'
        }}>
          <h3 style={{
            color: '#d84315',
            marginTop: '0',
            marginBottom: '12px',
            fontSize: '1.2rem',
            textAlign: 'center'
          }}>
            {currentGlobalEvent.name}
          </h3>
          <p style={{
            fontSize: '1rem',
            marginBottom: '0',
            textAlign: 'center',
            fontStyle: 'italic',
            color: '#5d4037'
          }}>
            {currentGlobalEvent.text}
          </p>
        </div>
      </div>
    );
  };

  // Render energy cube pile
  const renderEnergyCubePile = () => {
    if (!energyCubePile) return null;

    return (
      <div className="card-container energy-cube-pile">
        <div className="card-title" style={{ color: '#00796b', borderBottom: '2px solid #4db6ac' }}>
          Energy Cube Pile
        </div>
        <div className="card-item" style={{
          background: 'linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%)',
          border: '2px solid #4db6ac',
          boxShadow: '0 4px 8px rgba(77, 182, 172, 0.2)',
          padding: '16px',
          borderRadius: '8px',
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          {Object.entries(energyCubePile).map(([type, count]) => (
            <div key={type} style={{ textAlign: 'center', padding: '0 8px' }}>
              <div
                className={`energy-cube ${type}`}
                style={{
                  width: '30px',
                  height: '30px',
                  margin: '0 auto 8px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                }}
              />
              <div style={{ fontWeight: 'bold', color: '#00695c' }}>{count}</div>
              <div style={{ fontSize: '0.8rem', color: '#00796b', textTransform: 'capitalize' }}>{type}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render different card types with appropriate styling
  const renderTravelCard = (card, key) => {
    const isSelected = selectedTravel.includes(card.id);
    const isCurrentPlayer = currentPlayer && currentPlayer.id === socket.id;
    const canPickUp = isCurrentPlayer && isMyTurn(currentPlayer);
    const vehicleImagePath = `/assets/images/vehicles/${card.value}/${card.vehicle || 'camel'}.png`;

    // Check if "Drizzle of Delay" global event is active
    const maxMovesRestricted = currentGlobalEvent?.effect === 'max_moves_2_and_cost_artha_north_east';
    const isDisabled = maxMovesRestricted && card.value > 2;

    return (
      <div
        key={key}
        className={`card-item travel-card ${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}`}
        style={{
          cursor: (canPickUp && !isDisabled) ? 'pointer' : 'default',
          opacity: (canPickUp && !isDisabled) ? 1 : 0.7,
          border: isSelected ? '2px solid var(--primary-color)' : '1px solid #ddd',
          background: isSelected ? '#e3f2fd' : (isDisabled ? '#eeeeee' : '#FFF8E1'),
          borderRadius: '8px',
          width: '120px',
          height: '180px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '10px',
          position: 'relative',
          margin: '10px'
        }}
        onClick={() => {
          if (canPickUp && !isDisabled) {
            if (selectedTravel.includes(card.id)) {
              setSelectedTravel(selectedTravel.filter(id => id !== card.id));
            } else if (selectedTravel.length < 2) {
              setSelectedTravel([...selectedTravel, card.id]);
            } else {
              alert('You can only select up to 2 travel cards.');
            }
          }
        }}
      >
        <div style={{ position: 'absolute', top: '8px', left: '8px', fontWeight: 'bold', fontSize: '1.5rem' }}>
          {card.value}
        </div>
        <div style={{ width: '100px', height: '100px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <img
            src={vehicleImagePath}
            alt={`${card.vehicle} (${card.value})`}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              marginBottom: '10px'
            }}
            onError={(e) => {
              // Fallback if image doesn't load
              e.target.style.display = 'none';
              e.target.nextSibling.style.fontSize = '4rem';
            }}
          />
        </div>
        <div style={{
          fontSize: '0.9rem',
          marginTop: 'auto',
          textAlign: 'center',
          textTransform: 'capitalize'
        }}>
          {card.vehicle || 'Vehicle'} ({card.value})
        </div>

        {isDisabled && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0,0,0,0.3)',
            color: 'white',
            fontWeight: 'bold',
            borderRadius: '8px',
            fontSize: '1rem'
          }}>
            Max 2 Moves
          </div>
        )}
      </div>
    );
  };

  const renderEventCard = (card, key) => {
    const isSelected = selectedEvent === card.id;
    const isCurrentPlayer = currentPlayer && currentPlayer.id === socket.id;
    const canPickUp = isCurrentPlayer && isMyTurn(currentPlayer);

    const cardType = card.type || 'event';
    const displayName = card.name || card.type;

    // Determine icon and color based on card type
    let icon = '★';
    if (cardType === 'wildCube') icon = '⬛';
    if (cardType === 'extraHop') icon = '⟿';

    return (
      <div
        key={key}
        className={`card-item event-card ${isSelected ? 'selected' : ''}`}
        style={{
          cursor: canPickUp ? 'pointer' : 'default',
          opacity: canPickUp ? 1 : 0.7,
          border: isSelected ? '2px solid var(--secondary-color)' : '1px solid #ddd',
          background: isSelected ? '#f3e5f5' : 'white',
          borderRadius: '8px',
          width: '120px',
          height: '160px',
          boxShadow: isSelected ? '0 4px 8px rgba(0,0,0,0.2)' : '0 2px 4px rgba(0,0,0,0.1)',
          display: 'flex',
          flexDirection: 'column',
          padding: '10px',
          transition: 'all 0.2s ease',
          position: 'relative',
          overflow: 'hidden'
        }}
        onClick={() => {
          if (canPickUp) {
            setSelectedEvent(isSelected ? null : card.id);
          }
        }}
      >
        {/* Card header */}
        <div style={{ textAlign: 'center', marginBottom: '6px' }}>
          <h3 style={{
            fontSize: '0.9rem',
            color: 'var(--secondary-color)',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            margin: '0'
          }}>
            Event
          </h3>
        </div>

        {/* Card icon */}
        <div style={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '8px'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            background: '#f3e5f5',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)',
            fontSize: '2rem',
            color: 'var(--secondary-color)'
          }}>
            {icon}
          </div>

          <div style={{
            fontSize: '0.85rem',
            fontWeight: 'bold',
            textAlign: 'center',
            color: 'var(--secondary-color)',
            padding: '0 4px'
          }}>
            {displayName}
          </div>
        </div>

        {/* Card description */}
        <p style={{
          fontSize: '0.7rem',
          marginTop: '8px',
          color: '#666',
          textAlign: 'center',
          borderTop: '1px solid #eee',
          paddingTop: '4px'
        }}>
          {card.description || (cardType === 'wildCube' ? 'Use as any cube type' : 'Add extra movements')}
        </p>

        {/* Selection indicator */}
        {isSelected && (
          <div style={{
            position: 'absolute',
            top: '0',
            right: '0',
            background: 'var(--secondary-color)',
            color: 'white',
            width: '30px',
            height: '30px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            clipPath: 'polygon(0 0, 100% 0, 100% 100%)',
            fontSize: '0.8rem'
          }}>
            ✓
          </div>
        )}
      </div>
    );
  };

  const renderJourneyCard = (card, index, type) => {
    if (!card) return null;
    
    // Get location details
    const location = locations.find(loc => loc.id === card.locationId);
    
    // Check if the current player can collect this journey card
    const canCollect = 
      canCollectJourneyCard(card, currentPlayer);
    
    // Determine card type (inner/outer)
    const journeyType = card.name || (type === 'inner' ? 'Inner Journey' : 'Outer Journey');
    
    // Extract required energy cubes
    const energyRequirements = [];
    if (card.required) {
      for (const [type, count] of Object.entries(card.required)) {
        for (let i = 0; i < count; i++) {
          energyRequirements.push(type);
        }
      }
    }
    
    // Determine reward value
    const rewardValue = card.reward ? (card.reward.inner || card.reward.outer) : 0;
    
    // Determine OM token cost based on player count and how many journey cards of this type the player already has
    const playerCount = gameState.players.length;
    const journeyCount = getJourneyCount(currentPlayer, card);
    
    // OM token cost is 0-1-1-2 for 4 players, 1-1-2-3 for others
    let cost;
    if (playerCount === 4) {
      cost = [0, 1, 1, 2][journeyCount];
    } else {
      cost = [1, 1, 2, 3][journeyCount];
    }
    
    // Check if inner journey cards are blocked by "Drought of Spirits" global event
    const isInnerJourneyBlocked = 
      gameState.currentGlobalEvent?.effect === 'no_inner_journey_cards' && 
      type === 'inner';
      
    // Check if outer journey cards are blocked by "Heritage Site Renovations" global event  
    const isOuterJourneyBlocked = 
      gameState.currentGlobalEvent?.effect === 'no_outer_journey_cards' && 
      type === 'outer';

    return (
      <div
        key={index}
        className="card-item journey-card"
        style={{
          cursor: canCollect ? 'pointer' : 'default',
          opacity: canCollect ? 1 : 0.7,
          width: '200px',
          height: '280px',
          borderRadius: '8px',
          overflow: 'hidden',
          position: 'relative',
          boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
          background: '#fff',
          border: `2px solid ${type === 'inner' ? 'var(--primary-color)' : 'var(--accent-color)'}`,
          transition: 'transform 0.2s ease, box-shadow 0.2s ease'
        }}
        onClick={() => {
          if (canCollect) {
            setJourneyModal(card);
          }
        }}
      >
        {/* Header with location name, number and region */}
        <div style={{
          background: type === 'inner' ? 'var(--primary-color)' : 'var(--accent-color)',
          color: 'white',
          padding: '8px 12px',
          borderTopLeftRadius: '6px',
          borderTopRightRadius: '6px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ fontWeight: 'bold', fontSize: '1rem' }}>
            {location?.name || 'Unknown Location'}
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            fontSize: '0.8rem'
          }}>
            <span>#{card.locationId}</span>
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: `var(--${location?.region?.toLowerCase()}-color)`,
              border: '1px solid #fff'
            }} />
          </div>
        </div>

        {/* Main card image */}
        <div style={{
          height: '160px',
          background: '#eee',
          backgroundImage: `url(${process.env.PUBLIC_URL}/assets/images/cards/${card.locationId}.png)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          position: 'relative'
        }}>
          {/* Journey type indicator */}
          <div style={{
            position: 'absolute',
            bottom: '8px',
            right: '8px',
            background: type === 'inner' ? 'var(--primary-color)' : 'var(--accent-color)',
            color: 'white',
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            border: '2px solid white'
          }}>
            {rewardValue}
          </div>

          {/* Block overlay for inner journey cards during Drought of Spirits */}
          {isInnerJourneyBlocked && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column',
              color: 'white',
              padding: '10px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '8px' }}>
                BLOCKED
              </div>
              <div style={{ fontSize: '0.8rem' }}>
                Drought of Spirits event prevents inner journey collection
              </div>
            </div>
          )}
        </div>

        {/* Card footer with requirements */}
        <div style={{
          padding: '12px',
          borderTop: '1px solid #eee',
        }}>
          {/* Journey type and description */}
          <div style={{
            fontSize: '0.85rem',
            color: '#666',
            marginBottom: '8px',
            fontStyle: 'italic'
          }}>
            {journeyType}
          </div>

          {/* Requirements display */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            {/* Energy cubes */}
            <div style={{
              display: 'flex',
              gap: '4px'
            }}>
              {energyRequirements.map((cubeType, i) => (
                <div
                  key={i}
                  style={{
                    width: '24px',
                    height: '24px',
                    borderRadius: '4px',
                    background: `var(--${cubeType}-color)`,
                    border: '1px solid #666',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.2)'
                  }}
                />
              ))}
            </div>

            {/* OM token cost */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}>
              <div style={{
                background: '#ffd700',
                color: '#333',
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                border: '1px solid #333'
              }}>
                {cost}
              </div>
            </div>
          </div>

          {/* Available badge */}
          {canCollect && (
            <div style={{
              position: 'absolute',
              top: '40px',
              right: '-28px',
              background: 'var(--accent-color)',
              color: 'white',
              padding: '2px 30px',
              transform: 'rotate(45deg)',
              boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
              fontSize: '0.8rem',
              fontWeight: 'bold'
            }}>
              Available
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="face-up-cards">
      {/* Display the Global Event Card at the top */}
      {renderGlobalEventCard()}

      {/* Travel Card Section */}
      <div className="card-container travel-cards">
        <div className="card-title">
          Travel Cards
          <div className="card-actions">
            <button
              className={`action-button ${!selectedTravel.length ? 'disabled' : ''}`}
              onClick={handlePickTravel}
              disabled={!selectedTravel.length}
            >
              Pick Selected ({selectedTravel.length})
            </button>
            <button
              className="action-button"
              onClick={handlePickTravelFromTop}
            >
              Pick from Top
            </button>
          </div>
        </div>
        <div className="card-grid">
          {faceUpTravel?.map((card, index) => renderTravelCard(card, `travel-${index}`))}
        </div>
      </div>

      {/* Event Card Section */}
      {/* <div className="card-container event-cards">
        <div className="card-title">
          Event Cards
          <div className="card-actions">
            <button
              className={`action-button ${!selectedEvent ? 'disabled' : ''}`}
              onClick={handlePickEvent}
              disabled={!selectedEvent}
            >
              Pick Selected
            </button>
            <button
              className="action-button"
              onClick={handlePickEventFromTop}
            >
              Pick from Top
            </button>
          </div>
        </div>
        <div className="card-grid">
          {faceUpEvent?.map((card, index) => renderEventCard(card, `event-${index}`))}
        </div>
      </div> */}

      {/* Journey Card Sections */}
      <div className="journey-cards-section" style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: '16px'
      }}>
        <div className="card-container journey-cards outer" style={{ flex: 1 }}>
          <div className="card-title">Outer Journey Cards</div>
          <div className="card-grid">
            {faceUpJourneyOuter?.map((card, index) => renderJourneyCard(card, index, 'outer'))}
          </div>
        </div>

        <div className="card-container journey-cards inner" style={{ flex: 1 }}>
          <div className="card-title">Inner Journey Cards</div>
          <div className="card-grid">
            {faceUpJourneyInner?.map((card, index) => renderJourneyCard(card, index, 'inner'))}
          </div>
        </div>
      </div>

      {/* Journey Card Modal */}
      {journeyModal && (
        <JourneyCardModal
          card={journeyModal}
          onClose={() => setJourneyModal(null)}
          onCollect={handleCollectJourney}
          canCollect={canCollectJourneyCard(journeyModal, currentPlayer)}
          player={currentPlayer}
        />
      )}
    </div>
  );
}

export default FaceUpCards;