import React, { useState } from 'react';

function PlayerLobby({ socket, gameState }) {
  const [playerName, setPlayerName] = useState('');
  const [isBotModeEnabled, setIsBotModeEnabled] = useState(false);
  const [isAddingBot, setIsAddingBot] = useState(false);

  const handleJoin = () => {
    if (playerName.trim() !== '') {
      socket.emit('joinGame', playerName.trim());
      setPlayerName('');
    }
  };

  const handleStart = () => {
    console.log('Attempting to start game...');
    
    // With the new bot architecture, we don't need to add bots here.
    // External bot processes will connect via websockets instead
    socket.emit('startGame');
    
    // Add a listener specifically for this button click to detect if the game starts
    const checkGameStarted = (state) => {
      console.log('Game state after startGame event:', {
        started: state.started,
        players: state.players.length
      });
    };
    
    socket.once('gameState', checkGameStarted);
  };

  // Display a message to remind users to start bot clients
  const getBotModeMessage = () => {
    if (isBotModeEnabled) {
      return "Bot Mode enabled. Start your bot clients using 'npm run start-bot' in a separate terminal.";
    }
    return null;
  };

  // Handle enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleJoin();
    }
  };

  return (
    <div className="game-container" style={{ 
      backgroundImage: `url(${process.env.PUBLIC_URL}/assets/images/om_the_journey_poster.jpg)`,
      backgroundSize: 'contain',
      backgroundPosition: 'center center',
      backgroundRepeat: 'no-repeat',
      backgroundColor: '#150D07', // Dark background color matching the poster
      minHeight: '100vh',
      width: '100%',
      display: 'flex',
      alignItems: 'flex-end', // Position at bottom
      justifyContent: 'center',
      padding: '0 20px 40px' // Add padding at bottom
    }}>
      <div className="card" style={{ 
        maxWidth: '400px', 
        margin: '0 auto',
        textAlign: 'center',
        background: 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(5px)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        borderRadius: '12px',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h2 style={{ 
          color: '#5D3D1E', 
          fontSize: '1.5rem', 
          marginBottom: '0.5rem',
          marginTop: '0.5rem' 
        }}>
          Join the Game
        </h2>
        
        <div style={{ padding: '0.5rem 1rem 1.5rem' }}>
          <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
            <input
              type="text"
              placeholder="Enter your name"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              onKeyPress={handleKeyPress}
              style={{ 
                flex: 1, 
                padding: '0.6rem',
                borderRadius: '6px',
                border: '1px solid #ddd'
              }}
            />
            <button 
              onClick={handleJoin}
              style={{
                background: '#5D3D1E',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '0.6rem 1rem',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              Join
            </button>
          </div>
          
          {/* Bot Mode Toggle */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            marginTop: '1rem',
            justifyContent: 'center',
            background: 'rgba(255, 255, 255, 0.3)',
            padding: '0.5rem',
            borderRadius: '6px' 
          }}>
            <input
              type="checkbox"
              id="botMode"
              checked={isBotModeEnabled}
              onChange={(e) => setIsBotModeEnabled(e.target.checked)}
              style={{ marginRight: '0.5rem' }}
            />
            <label 
              htmlFor="botMode"
              style={{ 
                color: '#5D3D1E', 
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
            >
              Enable Bot Mode (Play against external bots)
            </label>
          </div>
          
          {/* Bot Mode Message */}
          {isBotModeEnabled && (
            <div style={{
              marginTop: '0.5rem',
              padding: '0.5rem',
              background: 'rgba(212, 160, 72, 0.2)',
              borderRadius: '6px',
              fontSize: '0.9rem',
              color: '#5D3D1E'
            }}>
              {getBotModeMessage()}
            </div>
          )}
        </div>
        
        <div style={{ 
          borderTop: '1px solid rgba(0,0,0,0.1)', 
          padding: '1rem',
          backgroundColor: 'rgba(255, 255, 255, 0.5)',
          borderBottomLeftRadius: '12px',
          borderBottomRightRadius: '12px'
        }}>
          <h3 style={{ 
            margin: '0 0 0.8rem',
            color: '#5D3D1E'
          }}>
            Players ({gameState.players.length}/4)
          </h3>
          {gameState.players.length === 0 ? (
            <p style={{ color: '#5D3D1E', fontStyle: 'italic', margin: '0.5rem 0' }}>No players have joined yet</p>
          ) : (
            <div className="flex gap-md" style={{ justifyContent: 'center', flexWrap: 'wrap', margin: '0.8rem 0' }}>
              {gameState.players.map((p) => (
                <div 
                  key={p.id} 
                  className="card" 
                  style={{ 
                    minWidth: '120px', 
                    padding: '0.6rem', 
                    background: p.id === socket.id ? '#5D3D1E' : 'rgba(255, 255, 255, 0.8)',
                    color: p.id === socket.id ? 'white' : '#5D3D1E',
                    borderRadius: '6px',
                    margin: '0.25rem',
                    fontWeight: p.id === socket.id ? 'bold' : 'normal'
                  }}
                >
                  {p.name} {p.id === socket.id ? '(you)' : ''}
                  {p.id && p.id.startsWith('bot-') ? ' (Bot)' : ''}
                </div>
              ))}
            </div>
          )}
          
          {/* Only human player can start game with bot in bot mode */}
          {isBotModeEnabled && gameState.players.length === 1 && gameState.players[0].id === socket.id ? (
            <button 
              onClick={handleStart} 
              style={{ 
                backgroundColor: '#D4A048',
                color: '#5D3D1E',
                border: 'none',
                borderRadius: '6px',
                padding: '0.6rem 1.5rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                margin: '0.5rem 0 0'
              }}
            >
              Start Game with Bot
            </button>
          ) : (
            (gameState.players.length >= 2 && gameState.players.length <= 4) ? (
              <button 
                onClick={handleStart} 
                style={{ 
                  backgroundColor: '#D4A048',
                  color: '#5D3D1E',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.6rem 1.5rem',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  margin: '0.5rem 0 0'
                }}
              >
                Start Game
              </button>
            ) : (
              <p style={{ color: '#5D3D1E', margin: '0.5rem 0 0' }}>
                {gameState.players.length < 1 
                  ? 'Join the game to play' 
                  : isBotModeEnabled
                    ? 'Bot Mode enabled but you need to join first'
                    : 'At least 2 players required to start'}
              </p>
            )
          )}
        </div>
      </div>
    </div>
  );
}

export default PlayerLobby;